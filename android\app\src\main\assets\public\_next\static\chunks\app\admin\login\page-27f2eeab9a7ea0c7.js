(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2116],{585:(e,t,a)=>{"use strict";a.d(t,{L$:()=>i});var r=a(95155);a(12115);var s=a(68289);function i(e){let{variant:t="default",className:a=""}=e,i={default:{gradient:"bg-gradient-to-br from-gray-900 via-green-900 to-black",particles:30,colors:["bg-green-400","bg-orange-400","bg-yellow-400"]},auth:{gradient:"bg-gradient-to-br from-gray-900 via-black to-gray-900",particles:20,colors:["bg-green-500","bg-orange-500","bg-yellow-500"]},dashboard:{gradient:"bg-gradient-to-br from-gray-900 via-gray-800 to-black",particles:15,colors:["bg-green-400","bg-orange-400","bg-yellow-400"]}}[t];return(0,r.jsxs)("div",{className:"fixed inset-0 z-0 ".concat(a),children:[(0,r.jsx)("div",{className:"absolute inset-0 ".concat(i.gradient," animate-gradient")}),(0,r.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,r.jsx)(s.P.div,{className:"absolute -top-40 -right-40 w-80 h-80 bg-orange-500 rounded-full mix-blend-multiply filter blur-xl opacity-20",animate:{x:[0,30,-20,0],y:[0,-50,20,0],scale:[1,1.1,.9,1]},transition:{duration:7,repeat:1/0,ease:"easeInOut"}}),(0,r.jsx)(s.P.div,{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20",animate:{x:[0,-30,20,0],y:[0,50,-20,0],scale:[1,.9,1.1,1]},transition:{duration:7,repeat:1/0,ease:"easeInOut",delay:2}}),(0,r.jsx)(s.P.div,{className:"absolute top-40 left-40 w-80 h-80 bg-amber-500 rounded-full mix-blend-multiply filter blur-xl opacity-20",animate:{x:[0,20,-30,0],y:[0,-30,40,0],scale:[1,1.2,.8,1]},transition:{duration:7,repeat:1/0,ease:"easeInOut",delay:4}})]}),(0,r.jsx)("div",{className:"absolute inset-0 opacity-20",children:[...Array(i.particles)].map((e,t)=>(0,r.jsx)(s.P.div,{className:"absolute w-1 h-1 ".concat(i.colors[t%i.colors.length]," rounded-full"),style:{left:"".concat(100*Math.random(),"%"),top:"".concat(100*Math.random(),"%")},animate:{y:[0,-20,0],opacity:[.2,.8,.2],scale:[1,1.5,1]},transition:{duration:3+2*Math.random(),repeat:1/0,delay:2*Math.random(),ease:"easeInOut"}},t))}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-black/30"})]})}},3727:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>h});var r=a(95155),s=a(68289),i=a(6874),n=a.n(i),l=a(35695),o=a(12115),c=a(10351),d=a(585),u=a(48016),m=a(13741),x=a(98030);function h(){let e=(0,l.useRouter)(),{adminLogin:t,isLoading:a,error:i,clearError:h,isAuthenticated:p,user:g}=(0,x.A)(),[y,f]=(0,o.useState)({email:"",password:"",adminCode:""}),[b,v]=(0,o.useState)(!1),[j,w]=(0,o.useState)({});(0,o.useEffect)(()=>{p&&(null==g?void 0:g.role)==="ADMIN"?e.push("/admin/dashboard"):p&&(null==g?void 0:g.role)==="USER"&&e.push("/dashboard")},[p,g,e]),(0,o.useEffect)(()=>{i&&h(),w({})},[y,i,h]);let N=async a=>{if(a.preventDefault(),(()=>{var e;let t={};return y.email.trim()?/\S+@\S+\.\S+/.test(y.email)||(t.email="Email is invalid"):t.email="Email is required",y.password||(t.password="Password is required"),(null==(e=y.adminCode)?void 0:e.trim())||(t.adminCode="Admin code is required"),w(t),0===Object.keys(t).length})())try{await t(y),e.push("/admin/dashboard")}catch(e){console.error("Admin login failed:",e)}},A=e=>{let{name:t,value:a}=e.target;f(e=>({...e,[t]:a}))};return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-green-900 to-black text-white overflow-hidden relative",children:[(0,r.jsx)(d.L$,{variant:"default"}),(0,r.jsx)("nav",{className:"relative z-50 px-6 py-4",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto flex items-center justify-between",children:[(0,r.jsx)(n(),{href:"/",className:"flex items-center",children:(0,r.jsx)(u.UU,{size:"md",variant:"light"})}),(0,r.jsxs)(n(),{href:"/",className:"inline-flex items-center text-gray-400 hover:text-white transition-colors",children:[(0,r.jsx)(c.kRp,{className:"mr-2"}),"Back to Home"]})]})}),(0,r.jsx)("div",{className:"relative z-10 flex items-center justify-center min-h-[calc(100vh-80px)] p-8",children:(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"w-full max-w-md bg-gray-900/80 backdrop-blur-lg border border-gray-800/50 rounded-2xl p-8 shadow-2xl",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,r.jsx)(u.UU,{size:"lg"}),(0,r.jsx)(c.pcC,{className:"text-red-400 ml-2",size:24})]}),(0,r.jsx)("h1",{className:"text-2xl font-bold mb-2",children:"Admin Access"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Secure administrative portal"})]}),i&&(0,r.jsx)(s.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-6",children:(0,r.jsx)("p",{className:"text-red-400 text-sm",children:i})}),(0,r.jsxs)("form",{onSubmit:N,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Admin Email"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(c.pHD,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,r.jsx)("input",{type:"email",name:"email",value:y.email,onChange:A,className:"w-full pl-10 pr-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg focus:border-red-500 focus:outline-none transition-colors",placeholder:"<EMAIL>"})]}),j.email&&(0,r.jsx)("p",{className:"text-red-400 text-xs mt-1",children:j.email})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(c.F5$,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,r.jsx)("input",{type:b?"text":"password",name:"password",value:y.password,onChange:A,className:"w-full pl-10 pr-12 py-3 bg-gray-900/50 border border-gray-700 rounded-lg focus:border-red-500 focus:outline-none transition-colors",placeholder:"••••••••"}),(0,r.jsx)("button",{type:"button",onClick:()=>v(!b),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white",children:b?(0,r.jsx)(c._NO,{}):(0,r.jsx)(c.Vap,{})})]}),j.password&&(0,r.jsx)("p",{className:"text-red-400 text-xs mt-1",children:j.password})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Admin Access Code"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(c.pcC,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,r.jsx)("input",{type:"password",name:"adminCode",value:y.adminCode||"",onChange:A,className:"w-full pl-10 pr-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg focus:border-red-500 focus:outline-none transition-colors",placeholder:"Enter admin code"})]}),j.adminCode&&(0,r.jsx)("p",{className:"text-red-400 text-xs mt-1",children:j.adminCode})]}),(0,r.jsx)(m.$n,{type:"submit",disabled:a,className:"w-full",variant:"primary",children:a?"Authenticating...":"Access Admin Panel"})]}),(0,r.jsx)("div",{className:"text-center mt-6",children:(0,r.jsxs)("p",{className:"text-gray-400",children:["Not an admin?"," ",(0,r.jsx)(n(),{href:"/auth/login",className:"text-green-400 hover:text-green-300 font-medium",children:"User Login"})]})}),(0,r.jsx)("div",{className:"text-center mt-4",children:(0,r.jsxs)(n(),{href:"/",className:"inline-flex items-center text-gray-400 hover:text-white transition-colors",children:[(0,r.jsx)(c.kRp,{className:"mr-2"}),"Back to Home"]})})]})})]})}},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}})},41778:(e,t,a)=>{Promise.resolve().then(a.bind(a,3727))},48016:(e,t,a)=>{"use strict";a.d(t,{UU:()=>o});var r=a(95155),s=a(68289),i=a(66766);let n={sm:{container:"h-8",text:"text-lg",icon:"w-6 h-6"},md:{container:"h-10",text:"text-xl",icon:"w-8 h-8"},lg:{container:"h-12",text:"text-2xl",icon:"w-10 h-10"},xl:{container:"h-16",text:"text-3xl",icon:"w-12 h-12"}},l={light:"text-white",dark:"text-gray-900",gradient:"bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"};function o(e){let{size:t="md",variant:a="gradient",showIcon:o=!0,className:c=""}=e,d=n[t],u=l[a];return(0,r.jsxs)(s.P.div,{className:"flex items-center space-x-2 ".concat(d.container," ").concat(c),whileHover:{scale:1.05},transition:{type:"spring",stiffness:400,damping:10},children:[o&&(0,r.jsx)(s.P.div,{className:"".concat(d.icon," flex items-center justify-center relative"),whileHover:{scale:1.1},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:10},children:(0,r.jsx)(i.default,{src:"/images/logo-text.png",alt:"Better Interest Logo",width:4*parseInt(d.icon.split(" ")[0].replace("w-","")),height:4*parseInt(d.icon.split(" ")[1].replace("h-","")),className:"".concat(d.icon," object-contain"),priority:!0})}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsxs)(s.P.h1,{className:"font-inter font-bold leading-tight ".concat(d.text," ").concat(u," relative"),initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5},style:{letterSpacing:"0.02em",textShadow:"0 0 20px rgba(255, 255, 255, 0.1)"},children:[(0,r.jsx)("span",{className:"text-white relative",style:{textShadow:"0 0 15px rgba(255, 255, 255, 0.2)",fontWeight:"700"},children:"Better"}),(0,r.jsx)("span",{className:"text-green-400 ml-1 relative",style:{textShadow:"0 0 20px rgba(34, 197, 94, 0.4), 0 0 40px rgba(34, 197, 94, 0.2)",fontWeight:"800",letterSpacing:"0.05em"},children:"Interest"})]}),"lg"===t||"xl"===t?(0,r.jsx)(s.P.p,{className:"text-xs text-gray-400 -mt-1",initial:{opacity:0},animate:{opacity:1},transition:{duration:.5,delay:.2},children:"Smart Savings Platform"}):null]})]})}},98030:(e,t,a)=>{"use strict";a.d(t,{A:()=>d,AuthProvider:()=>c});var r=a(95155),s=a(12115),i=a(96365);let n={user:null,token:null,isAuthenticated:!1,isLoading:!0,error:null};function l(e,t){switch(t.type){case"AUTH_START":return{...e,isLoading:!0,error:null};case"AUTH_SUCCESS":return{...e,user:t.payload.user,token:t.payload.token,isAuthenticated:!0,isLoading:!1,error:null};case"AUTH_FAILURE":return{...e,user:null,token:null,isAuthenticated:!1,isLoading:!1,error:t.payload};case"LOGOUT":return{...e,user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null};case"UPDATE_USER":return{...e,user:t.payload};case"CLEAR_ERROR":return{...e,error:null};default:return e}}let o=(0,s.createContext)(void 0);function c(e){let{children:t}=e,[a,c]=(0,s.useReducer)(l,n);(0,s.useEffect)(()=>{d()},[]),(0,s.useEffect)(()=>{if(a.token&&a.isAuthenticated){let e=setInterval(async()=>{try{a.token&&i.authService.isTokenExpired(a.token)&&await i.authService.refreshToken()}catch(e){console.error("Token refresh failed:",e),h()}},3e5);return()=>clearInterval(e)}},[a.token,a.isAuthenticated]);let d=async()=>{try{let e=i.authService.getStoredToken();if(!e)return void c({type:"AUTH_FAILURE",payload:"No token found"});if(i.authService.isTokenExpired(e))try{await i.authService.refreshToken();let e=i.authService.getStoredToken();if(e){let t=await i.authService.getCurrentUser();c({type:"AUTH_SUCCESS",payload:{user:t,token:e}})}else throw Error("Token refresh failed")}catch(e){c({type:"AUTH_FAILURE",payload:"Session expired"});return}else{let t=await i.authService.getCurrentUser();c({type:"AUTH_SUCCESS",payload:{user:t,token:e}})}}catch(e){c({type:"AUTH_FAILURE",payload:"Authentication check failed"})}},u=async e=>{try{c({type:"AUTH_START"});let t=await i.authService.login(e);c({type:"AUTH_SUCCESS",payload:{user:t.user,token:t.token}})}catch(e){throw c({type:"AUTH_FAILURE",payload:e instanceof Error?e.message:"Login failed"}),e}},m=async e=>{try{c({type:"AUTH_START"});let t=await i.authService.signup(e);c({type:"AUTH_SUCCESS",payload:{user:t.user,token:t.token}})}catch(e){throw c({type:"AUTH_FAILURE",payload:e instanceof Error?e.message:"Signup failed"}),e}},x=async e=>{try{c({type:"AUTH_START"});let t=await i.authService.adminLogin(e);c({type:"AUTH_SUCCESS",payload:{user:t.user,token:t.token}})}catch(e){throw c({type:"AUTH_FAILURE",payload:e instanceof Error?e.message:"Admin login failed"}),e}},h=async()=>{try{await i.authService.logout()}catch(e){console.error("Logout error:",e)}finally{c({type:"LOGOUT"})}},p={...a,login:u,signup:m,adminLogin:x,logout:h,updateUser:e=>{c({type:"UPDATE_USER",payload:e})},clearError:()=>{c({type:"CLEAR_ERROR"})},checkAuth:d};return(0,r.jsx)(o.Provider,{value:p,children:t})}function d(){let e=(0,s.useContext)(o);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}}},e=>{e.O(0,[844,5236,6874,6766,3289,8441,5964,7358],()=>e(e.s=41778)),_N_E=e.O()}]);