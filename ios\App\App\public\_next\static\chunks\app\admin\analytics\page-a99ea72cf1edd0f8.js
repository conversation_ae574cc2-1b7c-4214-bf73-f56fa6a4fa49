(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1093],{8619:(e,t,s)=>{"use strict";s.d(t,{d:()=>l});var r=s(60098),a=s(12115),n=s(51508),i=s(82885);function l(e){let t=(0,i.M)(()=>(0,r.OQ)(e)),{isStatic:s}=(0,a.useContext)(n.Q);if(s){let[,s]=(0,a.useState)(e);(0,a.useEffect)(()=>t.on("change",s),[])}return t}},19958:(e,t,s)=>{"use strict";s.d(t,{YO:()=>x,uk:()=>d});var r=s(95155),a=s(12115),n=s(8619),i=s(37602),l=s(58829),o=s(68289),c=s(57740);function d(e){let{children:t,className:s="",intensity:d="medium",glowEffect:x=!0,hoverScale:m=!0,borderGradient:h=!1,elevation:g=2,onClick:u}=e,{theme:p}=(0,c.DP)(),f=(0,c.Yx)(p),y=(0,a.useRef)(null),b=(0,n.d)(0),j=(0,n.d)(0),v=(0,i.z)(b),N=(0,i.z)(j),w=(0,l.G)(N,[-.5,.5],"light"===d?[-5,5]:"medium"===d?[-10,10]:[-15,15]),k=(0,l.G)(v,[-.5,.5],"light"===d?[-5,5]:"medium"===d?[-10,10]:[-15,15]),S=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s={1:"light"===p?"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)":"0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.5)",2:"light"===p?"0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)":"0 3px 6px rgba(0, 0, 0, 0.4), 0 3px 6px rgba(0, 0, 0, 0.6)",3:"light"===p?"0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)":"0 10px 20px rgba(0, 0, 0, 0.5), 0 6px 6px rgba(0, 0, 0, 0.7)",4:"light"===p?"0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22)":"0 14px 28px rgba(0, 0, 0, 0.6), 0 10px 10px rgba(0, 0, 0, 0.8)",5:"light"===p?"0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22)":"0 19px 38px rgba(0, 0, 0, 0.7), 0 15px 12px rgba(0, 0, 0, 0.9)"};return s[t?Math.min(e+2,5):e]||s[2]},T="\n    relative rounded-xl overflow-hidden transition-all duration-300 group\n    ".concat(f.bg.card,"\n    ").concat(f.border.primary,"\n    ").concat(h?"border-2 border-transparent bg-gradient-to-r from-green-400/20 to-blue-400/20 p-[1px]":"border","\n    ").concat(s,"\n  "),F=(0,r.jsxs)(o.P.div,{ref:y,className:T,style:{rotateY:k,rotateX:w,transformStyle:"preserve-3d",boxShadow:S(g)},onMouseMove:e=>{if(!y.current)return;let t=y.current.getBoundingClientRect(),s=t.width,r=t.height,a=(e.clientX-t.left)/s-.5,n=(e.clientY-t.top)/r-.5;b.set(a),j.set(n)},onMouseLeave:()=>{b.set(0),j.set(0)},whileHover:m?{scale:1.02,boxShadow:S(g,!0),transition:{duration:.2,ease:"easeOut"}}:{},transition:{type:"spring",stiffness:300,damping:30},onClick:u,children:[x&&(0,r.jsx)(o.P.div,{className:"absolute inset-0 bg-gradient-to-r from-green-400/5 to-blue-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl blur-xl",initial:{scale:.8},whileHover:{scale:1.1},transition:{duration:.3}}),(0,r.jsx)(o.P.div,{className:"absolute inset-0 bg-green-400/10 opacity-0 group-hover:opacity-20 rounded-xl",initial:{scale:0},whileHover:{scale:1},transition:{duration:.4,ease:"easeOut"}}),(0,r.jsx)("div",{className:"relative z-10 ".concat(h?"".concat(f.bg.card," rounded-xl"):""),children:t}),(0,r.jsx)(o.P.div,{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ".concat("dark"===p?"via-white/5":"via-white/10"),initial:{x:"-100%"},whileHover:{x:"100%"},transition:{duration:.6,ease:"easeInOut"}})]});return(0,r.jsx)("div",{className:"group",children:F})}function x(e){let{title:t,value:s,subtitle:a,icon:n,color:i="green",className:l=""}=e,{theme:o}=(0,c.DP)(),x=(0,c.Yx)(o);return(0,r.jsx)(d,{className:"p-6 ".concat(l),glowEffect:!0,borderGradient:!0,children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium ".concat(x.text.secondary),children:t}),(0,r.jsx)("p",{className:"text-2xl font-bold ".concat(x.text.primary," mt-1"),children:s}),a&&(0,r.jsx)("p",{className:"text-xs ".concat(x.text.tertiary," mt-1"),children:a})]}),n&&(0,r.jsx)("div",{className:"w-12 h-12 rounded-lg bg-gradient-to-r ".concat({green:"from-green-400 to-green-600",blue:"from-blue-400 to-blue-600",purple:"from-purple-400 to-purple-600",yellow:"from-yellow-400 to-yellow-600"}[i]," flex items-center justify-center"),children:(0,r.jsx)(n,{className:"w-6 h-6 text-white"})})]})})}},31246:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>c,jn:()=>l,rp:()=>o});var r=s(95155);s(12115);var a=s(68289);let n=()=>(0,r.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("circle",{cx:37,cy:37,r:"35.5",stroke:"currentColor",strokeWidth:3}),(0,r.jsx)("path",{d:"M25 35.5C24.1716 35.5 23.5 36.1716 23.5 37C23.5 37.8284 24.1716 38.5 25 38.5V35.5ZM49.0607 38.0607C49.6464 37.4749 49.6464 36.5251 49.0607 35.9393L39.5147 26.3934C38.9289 25.8076 37.9792 25.8076 37.3934 26.3934C36.8076 26.9792 36.8076 27.9289 37.3934 28.5147L45.8787 37L37.3934 45.4853C36.8076 46.0711 36.8076 47.0208 37.3934 47.6066C37.9792 48.1924 38.9289 48.1924 39.5147 47.6066L49.0607 38.0607ZM25 38.5L48 38.5V35.5L25 35.5V38.5Z",fill:"currentColor"})]});function i(e){let{children:t,onClick:s,href:i,variant:l="primary",size:o="md",disabled:c=!1,className:d="",type:x="button",icon:m}=e,h="\n    cursor-pointer font-bold font-sans transition-all duration-200 \n    border-2 border-transparent flex items-center justify-center\n    rounded-full relative overflow-hidden group\n    shadow-lg hover:shadow-xl active:shadow-inner\n    transform hover:-translate-y-1 active:translate-y-0\n    ".concat(c?"opacity-50 cursor-not-allowed":"","\n  "),g="\n    ".concat(h,"\n    ").concat({primary:"\n      bg-gradient-to-r from-green-400 to-green-600 \n      hover:from-green-500 hover:to-green-700\n      text-white border-green-500\n      active:border-green-600 active:shadow-inner\n    ",secondary:"\n      bg-gradient-to-r from-blue-400 to-blue-600 \n      hover:from-blue-500 hover:to-blue-700\n      text-white border-blue-500\n      active:border-blue-600 active:shadow-inner\n    ",outline:"\n      bg-transparent border-green-400 text-green-400\n      hover:bg-green-400 hover:text-black\n      active:border-green-500 active:shadow-inner\n    "}[l],"\n    ").concat({sm:"px-3 py-1.5 text-xs min-w-[80px]",md:"px-4 py-2 text-sm min-w-[100px]",lg:"px-6 py-3 text-base min-w-[120px]"}[o],"\n    ").concat(d,"\n  "),u=()=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-400/20 to-green-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full blur-xl"}),(0,r.jsxs)("span",{className:"relative z-10 flex items-center justify-center",children:[(0,r.jsx)("span",{className:"text",children:t}),(m||"primary"===l)&&(0,r.jsx)(a.P.div,{className:"ml-2 text-current",whileHover:{x:3},transition:{type:"spring",stiffness:400,damping:10},children:m||(0,r.jsx)(n,{})})]})]});return i?(0,r.jsx)(a.P.a,{href:i,className:g,whileHover:{scale:1.02,y:-1},whileTap:{scale:.96,y:1},transition:{type:"spring",stiffness:400,damping:10},children:(0,r.jsx)(u,{})}):(0,r.jsx)(a.P.button,{type:x,onClick:s,disabled:c,className:g,whileHover:{scale:c?1:1.02,y:c?0:-1},whileTap:{scale:c?1:.96,y:+!c},transition:{type:"spring",stiffness:400,damping:10},children:(0,r.jsx)(u,{})})}function l(e){return(0,r.jsx)(i,{...e,variant:"primary"})}function o(e){return(0,r.jsx)(i,{...e,variant:"outline"})}let c=i},37602:(e,t,s)=>{"use strict";s.d(t,{z:()=>x});var r=s(64803),a=s(30532),n=s(69515);function i(e){return"number"==typeof e?e:parseFloat(e)}var l=s(12115),o=s(51508),c=s(8619),d=s(58829);function x(e,t={}){let{isStatic:s}=(0,l.useContext)(o.Q),m=()=>(0,r.S)(e)?e.get():e;if(s)return(0,d.G)(m);let h=(0,c.d)(m());return(0,l.useInsertionEffect)(()=>(function(e,t,s){let l,o,c=e.get(),d=null,x=c,m="string"==typeof c?c.replace(/[\d.-]/g,""):void 0,h=()=>{d&&(d.stop(),d=null)},g=()=>{h(),d=new a.s({keyframes:[i(e.get()),i(x)],velocity:e.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...s,onUpdate:l})};return e.attach((t,s)=>(x=t,l=e=>{var t,r;return s((t=e,(r=m)?t+r:t))},n.Gt.postRender(g),e.get()),h),(0,r.S)(t)&&(o=t.on("change",t=>{var s,r;return e.set((s=t,(r=m)?s+r:s))}),e.on("destroy",o)),o})(h,e,t),[h,JSON.stringify(t)]),h}},57740:(e,t,s)=>{"use strict";s.d(t,{DP:()=>l,ThemeProvider:()=>i,Yx:()=>c});var r=s(95155),a=s(12115);let n=(0,a.createContext)(void 0);function i(e){let{children:t}=e,[s,i]=(0,a.useState)("dark"),[l,o]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{o(!0);let e=localStorage.getItem("theme");e?i(e):i("dark")},[]),(0,a.useEffect)(()=>{l&&(document.documentElement.classList.remove("light","dark"),document.documentElement.classList.add(s),localStorage.setItem("theme",s))},[s,l]),l)?(0,r.jsx)(n.Provider,{value:{theme:s,toggleTheme:()=>{i(e=>"light"===e?"dark":"light")},setTheme:e=>{i(e)}},children:t}):(0,r.jsx)("div",{className:"min-h-screen bg-black",children:t})}function l(){let e=(0,a.useContext)(n);return void 0===e?(console.warn("useTheme called outside of ThemeProvider, using fallback values"),{theme:"dark",toggleTheme:()=>{},setTheme:()=>{}}):e}let o={light:{bg:{primary:"bg-white",secondary:"bg-gray-50",tertiary:"bg-gray-100",card:"bg-white",sidebar:"bg-white",header:"bg-white/80"},text:{primary:"text-gray-900",secondary:"text-gray-600",tertiary:"text-gray-500",inverse:"text-white"},border:{primary:"border-gray-200",secondary:"border-gray-300",accent:"border-green-200"},button:{primary:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white hover:shadow-green-500/25",secondary:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white hover:shadow-blue-500/25",outline:"bg-transparent border-2 border-green-500 text-green-600 hover:bg-green-500 hover:text-white"},accent:{green:"text-green-600",blue:"text-blue-600",purple:"text-purple-600",yellow:"text-yellow-600"}},dark:{bg:{primary:"bg-black",secondary:"bg-gray-900",tertiary:"bg-gray-800",card:"bg-gray-900",sidebar:"bg-gray-900",header:"bg-gray-900/80"},text:{primary:"text-white",secondary:"text-gray-300",tertiary:"text-gray-400",inverse:"text-black"},border:{primary:"border-gray-800",secondary:"border-gray-700",accent:"border-green-500/30"},button:{primary:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white hover:shadow-green-500/25",secondary:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white hover:shadow-blue-500/25",outline:"bg-transparent border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-black"},accent:{green:"text-green-400",blue:"text-blue-400",purple:"text-purple-400",yellow:"text-yellow-400"}}};function c(e){return o[e]}},58646:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(95155),a=s(12115),n=s(10351),i=s(31246),l=s(19958),o=s(64198),c=s(24630);function d(){let[e,t]=(0,a.useState)(!0),[s,d]=(0,a.useState)("month"),[x,m]=(0,a.useState)(null);(0,a.useEffect)(()=>{h()},[s]);let h=async()=>{try{t(!0);let e=await c.ZJ.getAnalytics(s);m(e)}catch(e){o.P0.error("Failed to load analytics")}finally{t(!1)}},g=e=>new Intl.NumberFormat("en-NG",{style:"currency",currency:"NGN"}).format(e),u=e=>"".concat(e>=0?"+":"").concat(e.toFixed(1),"%"),p=e=>e>=0?"text-green-400":"text-red-400",f=e=>e>=0?(0,r.jsx)(n.ARf,{}):(0,r.jsx)(n.JW4,{});return e?(0,r.jsx)("div",{className:"min-h-screen bg-gray-900 text-white p-6",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"})})}):x?(0,r.jsx)("div",{className:"min-h-screen bg-gray-900 text-white p-6",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-white",children:"Analytics Dashboard"}),(0,r.jsx)("p",{className:"text-gray-400 mt-2",children:"Comprehensive platform insights and metrics"})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("select",{value:s,onChange:e=>d(e.target.value),className:"bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-green-500",children:[(0,r.jsx)("option",{value:"week",children:"Last Week"}),(0,r.jsx)("option",{value:"month",children:"Last Month"}),(0,r.jsx)("option",{value:"quarter",children:"Last Quarter"}),(0,r.jsx)("option",{value:"year",children:"Last Year"})]}),(0,r.jsxs)(i.rp,{onClick:h,children:[(0,r.jsx)(n.jTZ,{className:"mr-2"}),"Refresh"]}),(0,r.jsxs)(i.rp,{children:[(0,r.jsx)(n.a4x,{className:"mr-2"}),"Export"]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsx)("div",{className:"bg-gray-800 border border-gray-700 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Users"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-white",children:x.users.total.toLocaleString()}),(0,r.jsxs)("div",{className:"flex items-center mt-1",children:[(0,r.jsxs)("span",{className:"text-sm ".concat(p(x.users.growth)),children:[f(x.users.growth),u(x.users.growth)]}),(0,r.jsxs)("span",{className:"text-gray-400 text-sm ml-1",children:["vs last ",s]})]})]}),(0,r.jsx)(n.cfS,{className:"text-blue-500 text-3xl"})]})}),(0,r.jsx)("div",{className:"bg-gray-800 border border-gray-700 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Volume"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-white",children:g(x.financial.totalVolume)}),(0,r.jsxs)("div",{className:"flex items-center mt-1",children:[(0,r.jsxs)("span",{className:"text-sm ".concat(p(x.financial.volumeGrowth)),children:[f(x.financial.volumeGrowth),u(x.financial.volumeGrowth)]}),(0,r.jsxs)("span",{className:"text-gray-400 text-sm ml-1",children:["vs last ",s]})]})]}),(0,r.jsx)(n.z8N,{className:"text-green-500 text-3xl"})]})}),(0,r.jsx)("div",{className:"bg-gray-800 border border-gray-700 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"Active Savings"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-white",children:x.savings.activePlans.toLocaleString()}),(0,r.jsxs)("div",{className:"flex items-center mt-1",children:[(0,r.jsxs)("span",{className:"text-sm ".concat(p(x.savings.growth)),children:[f(x.savings.growth),u(x.savings.growth)]}),(0,r.jsxs)("span",{className:"text-gray-400 text-sm ml-1",children:["vs last ",s]})]})]}),(0,r.jsx)(n.x_j,{className:"text-purple-500 text-3xl"})]})}),(0,r.jsx)(l.uk,{className:"bg-gray-800 border-gray-700 p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"Transaction Success"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-white",children:[x.transactions.successRate.toFixed(1),"%"]}),(0,r.jsxs)("div",{className:"flex items-center mt-1",children:[(0,r.jsxs)("span",{className:"text-sm ".concat(p(x.transactions.successRateChange)),children:[f(x.transactions.successRateChange),u(x.transactions.successRateChange)]}),(0,r.jsxs)("span",{className:"text-gray-400 text-sm ml-1",children:["vs last ",s]})]})]}),(0,r.jsx)(n.z1n,{className:"text-yellow-500 text-3xl"})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(l.uk,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white",children:"User Growth Trend"}),(0,r.jsx)(n.hht,{className:"text-blue-500"})]}),(0,r.jsx)("div",{className:"h-64 bg-gray-700 rounded-lg flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(n.hht,{className:"mx-auto text-4xl text-gray-500 mb-2"}),(0,r.jsx)("p",{className:"text-gray-400",children:"User Growth Chart"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Chart visualization would be here"})]})}),(0,r.jsxs)("div",{className:"mt-4 grid grid-cols-3 gap-4 text-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"New Users"}),(0,r.jsx)("p",{className:"text-white font-semibold",children:x.users.newUsers})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"Active Users"}),(0,r.jsx)("p",{className:"text-white font-semibold",children:x.users.activeUsers})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"Retention Rate"}),(0,r.jsxs)("p",{className:"text-white font-semibold",children:[x.users.retentionRate.toFixed(1),"%"]})]})]})]}),(0,r.jsxs)(l.uk,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Revenue Breakdown"}),(0,r.jsx)(n.eXT,{className:"text-green-500"})]}),(0,r.jsx)("div",{className:"h-64 bg-gray-700 rounded-lg flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(n.eXT,{className:"mx-auto text-4xl text-gray-500 mb-2"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Revenue Pie Chart"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Chart visualization would be here"})]})}),(0,r.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),(0,r.jsx)("span",{className:"text-gray-400",children:"Deposit Fees"})]}),(0,r.jsx)("span",{className:"text-white",children:g(x.revenue.depositFees)})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),(0,r.jsx)("span",{className:"text-gray-400",children:"Withdrawal Fees"})]}),(0,r.jsx)("span",{className:"text-white",children:g(x.revenue.withdrawalFees)})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-purple-500 rounded-full"}),(0,r.jsx)("span",{className:"text-gray-400",children:"Interest Earned"})]}),(0,r.jsx)("span",{className:"text-white",children:g(x.revenue.interestEarned)})]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)(l.uk,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[(0,r.jsx)(n.cfS,{className:"mr-2"}),"User Analytics"]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Total Registered"}),(0,r.jsx)("span",{className:"text-white font-semibold",children:x.users.total.toLocaleString()})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"KYC Verified"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-green-400 font-semibold",children:x.users.kycVerified.toLocaleString()}),(0,r.jsxs)("span",{className:"bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs font-medium",children:[(x.users.kycVerified/x.users.total*100).toFixed(1),"%"]})]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Monthly Active"}),(0,r.jsx)("span",{className:"text-blue-400 font-semibold",children:x.users.monthlyActive.toLocaleString()})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Avg. Session Duration"}),(0,r.jsxs)("span",{className:"text-white font-semibold",children:[x.users.avgSessionDuration," min"]})]})]})]}),(0,r.jsxs)(l.uk,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[(0,r.jsx)(n.z8N,{className:"mr-2"}),"Financial Analytics"]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Total Deposits"}),(0,r.jsx)("span",{className:"text-green-400 font-semibold",children:g(x.financial.totalDeposits)})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Total Withdrawals"}),(0,r.jsx)("span",{className:"text-blue-400 font-semibold",children:g(x.financial.totalWithdrawals)})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Net Flow"}),(0,r.jsx)("span",{className:"text-white font-semibold",children:g(x.financial.totalDeposits-x.financial.totalWithdrawals)})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Avg. Transaction"}),(0,r.jsx)("span",{className:"text-white font-semibold",children:g(x.financial.avgTransactionSize)})]})]})]}),(0,r.jsxs)(l.uk,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[(0,r.jsx)(n.x_j,{className:"mr-2"}),"Savings Analytics"]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Active Plans"}),(0,r.jsx)("span",{className:"text-purple-400 font-semibold",children:x.savings.activePlans.toLocaleString()})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Completed Plans"}),(0,r.jsx)("span",{className:"text-green-400 font-semibold",children:x.savings.completedPlans.toLocaleString()})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Total Saved"}),(0,r.jsx)("span",{className:"text-white font-semibold",children:g(x.savings.totalSaved)})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Completion Rate"}),(0,r.jsxs)("span",{className:"bg-green-500/20 text-green-400 px-2 py-1 rounded-full text-xs font-medium",children:[x.savings.completionRate.toFixed(1),"%"]})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-gray-800 border border-gray-700 rounded-lg p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[(0,r.jsx)(n.z1n,{className:"mr-2"}),"Platform Performance"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-green-600/20 rounded-full flex items-center justify-center mx-auto mb-2",children:(0,r.jsx)(n.lZI,{className:"text-green-400 text-2xl"})}),(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"Payment Success Rate"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-white",children:[x.performance.paymentSuccessRate.toFixed(1),"%"]})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-blue-600/20 rounded-full flex items-center justify-center mx-auto mb-2",children:(0,r.jsx)(n.z1n,{className:"text-blue-400 text-2xl"})}),(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"System Uptime"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-white",children:[x.performance.systemUptime.toFixed(2),"%"]})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-purple-600/20 rounded-full flex items-center justify-center mx-auto mb-2",children:(0,r.jsx)(n.cfS,{className:"text-purple-400 text-2xl"})}),(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"User Satisfaction"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-white",children:[x.performance.userSatisfaction.toFixed(1),"/5"]})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-yellow-600/20 rounded-full flex items-center justify-center mx-auto mb-2",children:(0,r.jsx)(n.ARf,{className:"text-yellow-400 text-2xl"})}),(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"Growth Rate"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-white",children:u(x.performance.monthlyGrowthRate)})]})]})]}),(0,r.jsxs)("div",{className:"bg-gray-800 border border-gray-700 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Recent Activity Summary"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-white mb-2",children:"Top Performing Regions"}),(0,r.jsx)("div",{className:"space-y-2",children:x.regions.map((e,t)=>(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-400",children:e.name}),(0,r.jsxs)("span",{className:"text-white",children:[e.users.toLocaleString()," users"]})]},t))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-white mb-2",children:"Popular Savings Plans"}),(0,r.jsx)("div",{className:"space-y-2",children:x.popularPlans.map((e,t)=>(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-gray-400",children:e.type}),(0,r.jsxs)("span",{className:"text-white",children:[e.count," plans"]})]},t))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-white mb-2",children:"System Alerts"}),(0,r.jsx)("div",{className:"space-y-2",children:x.alerts.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("high"===e.severity?"bg-red-500/20 text-red-400":"medium"===e.severity?"bg-yellow-500/20 text-yellow-400":"bg-blue-500/20 text-blue-400"),children:e.severity}),(0,r.jsx)("span",{className:"text-gray-400 text-sm",children:e.message})]},t))})]})]})]})]})}):(0,r.jsx)("div",{className:"min-h-screen bg-gray-900 text-white p-6",children:(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsx)("p",{className:"text-gray-400",children:"Failed to load analytics data"})})})}},58829:(e,t,s)=>{"use strict";s.d(t,{G:()=>d});var r=s(6775),a=s(82885),n=s(69515),i=s(97494),l=s(8619);function o(e,t){let s=(0,l.d)(t()),r=()=>s.set(t());return r(),(0,i.E)(()=>{let t=()=>n.Gt.preRender(r,!1,!0),s=e.map(e=>e.on("change",t));return()=>{s.forEach(e=>e()),(0,n.WG)(r)}}),s}var c=s(60098);function d(e,t,s,a){if("function"==typeof e){c.bt.current=[],e();let t=o(c.bt.current,e);return c.bt.current=void 0,t}let n="function"==typeof t?t:function(...e){let t=!Array.isArray(e[0]),s=t?0:-1,a=e[0+s],n=e[1+s],i=e[2+s],l=e[3+s],o=(0,r.G)(n,i,l);return t?o(a):o}(t,s,a);return Array.isArray(e)?x(e,n):x([e],([e])=>n(e))}function x(e,t){let s=(0,a.M)(()=>[]);return o(e,()=>{s.length=0;let r=e.length;for(let t=0;t<r;t++)s[t]=e[t].get();return t(s)})}},64198:(e,t,s)=>{"use strict";s.d(t,{CustomToaster:()=>o,P0:()=>l,oR:()=>n.Ay});var r=s(95155),a=s(68289),n=s(13568),i=s(10351);let l={success:e=>{n.Ay.success(e,{duration:4e3,position:"top-right",style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}})},error:e=>{n.Ay.error(e,{duration:5e3,position:"top-right",style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}})},warning:e=>{(0,n.Ay)(e,{duration:4e3,position:"top-right",icon:"⚠️",style:{background:"#92400E",color:"#FFFBEB",border:"1px solid #F59E0B"}})},info:e=>{(0,n.Ay)(e,{duration:4e3,position:"top-right",icon:"ℹ️",style:{background:"#1E3A8A",color:"#EFF6FF",border:"1px solid #3B82F6"}})},loading:e=>n.Ay.loading(e,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"}}),dismiss:e=>{n.Ay.dismiss(e)},promise:(e,t)=>n.Ay.promise(e,t,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"},success:{style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}})};function o(){return(0,r.jsx)(n.l$,{position:"top-right",reverseOrder:!1,gutter:8,containerClassName:"",containerStyle:{},toastOptions:{className:"",duration:4e3,style:{background:"#1F2937",color:"#F9FAFB",border:"1px solid #374151",borderRadius:"8px",fontSize:"14px",fontWeight:"500",padding:"12px 16px",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"},success:{iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}},children:e=>(0,r.jsx)(n.bv,{toast:e,children:t=>{let{icon:s,message:l}=t;return(0,r.jsxs)(a.P.div,{initial:{opacity:0,y:-50,scale:.3},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.3},transition:{type:"spring",stiffness:500,damping:30,duration:.3},className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:s}),(0,r.jsx)("div",{className:"flex-1",children:l}),"loading"!==e.type&&(0,r.jsx)("button",{onClick:()=>n.Ay.dismiss(e.id),className:"flex-shrink-0 p-1 rounded-full hover:bg-gray-600 transition-colors",children:(0,r.jsx)(i.yGN,{className:"w-4 h-4"})})]})}})})}},91123:(e,t,s)=>{Promise.resolve().then(s.bind(s,58646))},96365:(e,t,s)=>{"use strict";s.d(t,{authService:()=>p});class r extends Error{static fromResponse(e){return new r(e.message||e.error,500,e.code,e.details,e.timestamp)}toJSON(){return{name:this.name,message:this.message,statusCode:this.statusCode,code:this.code,details:this.details,timestamp:this.timestamp}}constructor(e,t,s,r,a){super(e),this.statusCode=t,this.code=s,this.details=r,this.timestamp=a,this.name="ApiError",this.timestamp=a||new Date().toISOString()}}class a extends r{constructor(e,t){super(e,422,"VALIDATION_ERROR",t),this.validationErrors=t,this.name="ValidationError"}}class n extends r{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class i extends r{constructor(e="Access denied"){super(e,403,"AUTHORIZATION_ERROR"),this.name="AuthorizationError"}}class l extends r{constructor(e="Resource not found"){super(e,404,"NOT_FOUND_ERROR"),this.name="NotFoundError"}}class o extends r{constructor(e="Network error occurred"){super(e,0,"NETWORK_ERROR"),this.name="NetworkError"}}class c extends r{constructor(e="Request timeout"){super(e,408,"TIMEOUT_ERROR"),this.name="TimeoutError"}}class d extends r{constructor(e="Internal server error"){super(e,500,"SERVER_ERROR"),this.name="ServerError"}}class x extends r{constructor(e="Service temporarily unavailable"){super(e,503,"SERVICE_UNAVAILABLE"),this.name="ServiceUnavailableError"}}let m=async e=>{let t;try{t=await e.json()}catch(s){t={error:"Unknown error occurred",message:"HTTP ".concat(e.status,": ").concat(e.statusText)}}switch(e.status){case 400:if(t.errors)throw new a(t.message||"Validation failed",t.errors);throw new r(t.message||t.error||"Bad request",400,t.code,t.details);case 401:throw new n(t.message||t.error);case 403:throw new i(t.message||t.error);case 404:throw new l(t.message||t.error);case 408:throw new c(t.message||t.error);case 422:throw new a(t.message||"Validation failed",t.errors||t.details||{});case 500:throw new d(t.message||t.error);case 503:throw new x(t.message||t.error);default:throw new r(t.message||t.error||"Request failed",e.status,t.code,t.details)}},h=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e4,a=new AbortController,n=setTimeout(()=>a.abort(),s);try{let s=await fetch(e,{...t,signal:a.signal,headers:{"Content-Type":"application/json",...t.headers}});clearTimeout(n),s.ok||await m(s);let i=s.headers.get("content-type");if(!i||!i.includes("application/json"))return{};let l=await s.json();if(!1===l.success)throw r.fromResponse(l);return l.data||l}catch(e){if(clearTimeout(n),e instanceof r)throw e;throw(e=>{if("AbortError"===e.name)throw new c("Request was cancelled");if("TypeError"===e.name&&e.message.includes("fetch"))throw new o("Network connection failed");throw new o(e.message||"Network error occurred")})(e)}},g="http://localhost:8080";class u{getAuthHeaders(){let e=localStorage.getItem("auth_token");return{"Content-Type":"application/json",...e&&{Authorization:"Bearer ".concat(e)}}}async login(e){var t,s;let r=await h("".concat(g,"/api/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});console.log("LOGIN RESPONSE:",r);let a=(null==r||null==(t=r.data)?void 0:t.token)||(null==r?void 0:r.token),n=(null==r||null==(s=r.data)?void 0:s.refreshToken)||(null==r?void 0:r.refreshToken);return a?(console.log("Saving auth_token:",a),localStorage.setItem("auth_token",a)):console.warn("No token found in login response:",r),n&&localStorage.setItem("refresh_token",n),r}async signup(e){var t,s,r,a;let n=await h("".concat(g,"/api/auth/signup"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),i=(null==n||null==(t=n.data)?void 0:t.token)||(null==n?void 0:n.token),l=(null==n||null==(s=n.data)?void 0:s.refreshToken)||(null==n?void 0:n.refreshToken),o=(null==n||null==(r=n.data)?void 0:r.user)||(null==n?void 0:n.user);return i&&localStorage.setItem("auth_token",i),l&&localStorage.setItem("refresh_token",l),{user:o,token:i,refreshToken:l,expiresIn:(null==n||null==(a=n.data)?void 0:a.expiresIn)||(null==n?void 0:n.expiresIn)||3600}}async adminLogin(e){let t=await fetch("".concat(g,"/auth/admin/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Admin login failed");let s=await t.json();return localStorage.setItem("auth_token",s.token),s.refreshToken&&localStorage.setItem("refresh_token",s.refreshToken),s}async logout(){try{await fetch("".concat(g,"/auth/logout"),{method:"POST",headers:this.getAuthHeaders()})}catch(e){console.error("Logout error:",e)}finally{localStorage.removeItem("auth_token"),localStorage.removeItem("refresh_token")}}async getCurrentUser(){let e=await fetch("".concat(g,"/api/auth/user-info"),{headers:this.getAuthHeaders()});if(!e.ok)throw Error("Failed to get current user");return(await e.json()).user}async refreshToken(){let e=localStorage.getItem("refresh_token");if(!e)throw Error("No refresh token available");let t=await fetch("".concat(g,"/auth/refresh"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e})});if(!t.ok)throw Error("Token refresh failed");let s=await t.json();return localStorage.setItem("auth_token",s.data.token),s.data.refreshToken&&localStorage.setItem("refresh_token",s.data.refreshToken),{success:s.success,token:s.data.token,refreshToken:s.data.refreshToken,expiresIn:s.data.expiresIn||3600}}async requestPasswordReset(e){let t=await fetch("".concat(g,"/auth/password-reset/request"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Password reset request failed")}async confirmPasswordReset(e){let t=await fetch("".concat(g,"/auth/password-reset/confirm"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Password reset failed")}async changePassword(e){let t=await fetch("".concat(g,"/auth/change-password"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Password change failed")}getStoredToken(){return localStorage.getItem("auth_token")}isTokenExpired(e){try{let t=JSON.parse(atob(e.split(".")[1]));return 1e3*t.exp<Date.now()}catch(e){return!0}}}let p=new u}},e=>{e.O(0,[844,9268,5236,3568,5221,4630,8441,5964,7358],()=>e(e.s=91123)),_N_E=e.O()}]);