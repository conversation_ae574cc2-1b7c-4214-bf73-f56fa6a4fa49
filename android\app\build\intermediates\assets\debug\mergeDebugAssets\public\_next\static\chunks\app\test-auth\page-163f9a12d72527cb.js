(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2580],{31246:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>c,jn:()=>i,rp:()=>l});var r=s(95155);s(12115);var a=s(68289);let n=()=>(0,r.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("circle",{cx:37,cy:37,r:"35.5",stroke:"currentColor",strokeWidth:3}),(0,r.jsx)("path",{d:"M25 35.5C24.1716 35.5 23.5 36.1716 23.5 37C23.5 37.8284 24.1716 38.5 25 38.5V35.5ZM49.0607 38.0607C49.6464 37.4749 49.6464 36.5251 49.0607 35.9393L39.5147 26.3934C38.9289 25.8076 37.9792 25.8076 37.3934 26.3934C36.8076 26.9792 36.8076 27.9289 37.3934 28.5147L45.8787 37L37.3934 45.4853C36.8076 46.0711 36.8076 47.0208 37.3934 47.6066C37.9792 48.1924 38.9289 48.1924 39.5147 47.6066L49.0607 38.0607ZM25 38.5L48 38.5V35.5L25 35.5V38.5Z",fill:"currentColor"})]});function o(e){let{children:t,onClick:s,href:o,variant:i="primary",size:l="md",disabled:c=!1,className:d="",type:h="button",icon:m}=e,u="\n    cursor-pointer font-bold font-sans transition-all duration-200 \n    border-2 border-transparent flex items-center justify-center\n    rounded-full relative overflow-hidden group\n    shadow-lg hover:shadow-xl active:shadow-inner\n    transform hover:-translate-y-1 active:translate-y-0\n    ".concat(c?"opacity-50 cursor-not-allowed":"","\n  "),p="\n    ".concat(u,"\n    ").concat({primary:"\n      bg-gradient-to-r from-green-400 to-green-600 \n      hover:from-green-500 hover:to-green-700\n      text-white border-green-500\n      active:border-green-600 active:shadow-inner\n    ",secondary:"\n      bg-gradient-to-r from-blue-400 to-blue-600 \n      hover:from-blue-500 hover:to-blue-700\n      text-white border-blue-500\n      active:border-blue-600 active:shadow-inner\n    ",outline:"\n      bg-transparent border-green-400 text-green-400\n      hover:bg-green-400 hover:text-black\n      active:border-green-500 active:shadow-inner\n    "}[i],"\n    ").concat({sm:"px-3 py-1.5 text-xs min-w-[80px]",md:"px-4 py-2 text-sm min-w-[100px]",lg:"px-6 py-3 text-base min-w-[120px]"}[l],"\n    ").concat(d,"\n  "),g=()=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-400/20 to-green-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full blur-xl"}),(0,r.jsxs)("span",{className:"relative z-10 flex items-center justify-center",children:[(0,r.jsx)("span",{className:"text",children:t}),(m||"primary"===i)&&(0,r.jsx)(a.P.div,{className:"ml-2 text-current",whileHover:{x:3},transition:{type:"spring",stiffness:400,damping:10},children:m||(0,r.jsx)(n,{})})]})]});return o?(0,r.jsx)(a.P.a,{href:o,className:p,whileHover:{scale:1.02,y:-1},whileTap:{scale:.96,y:1},transition:{type:"spring",stiffness:400,damping:10},children:(0,r.jsx)(g,{})}):(0,r.jsx)(a.P.button,{type:h,onClick:s,disabled:c,className:p,whileHover:{scale:c?1:1.02,y:c?0:-1},whileTap:{scale:c?1:.96,y:+!c},transition:{type:"spring",stiffness:400,damping:10},children:(0,r.jsx)(g,{})})}function i(e){return(0,r.jsx)(o,{...e,variant:"primary"})}function l(e){return(0,r.jsx)(o,{...e,variant:"outline"})}let c=o},40855:(e,t,s)=>{Promise.resolve().then(s.bind(s,79271))},79271:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var r=s(95155),a=s(98030),n=s(6874),o=s.n(n),i=s(68289),l=s(10351),c=s(12115),d=s(31246);function h(){let{user:e,isAuthenticated:t,isLoading:s,logout:n}=(0,a.A)(),[h,m]=(0,c.useState)(null),u=async()=>{try{await n()}catch(e){console.error("Logout failed:",e)}},p=async(e,t)=>{try{await navigator.clipboard.writeText(e),m(t),setTimeout(()=>m(null),2e3)}catch(e){console.error("Failed to copy: ",e)}},g=[{name:"Main Dashboard",path:"/dashboard",icon:(0,r.jsx)(l.V5Y,{className:"w-5 h-5"}),description:"Overview of savings, goals, and recent activity"},{name:"Savings Plans",path:"/dashboard/savings-plans",icon:(0,r.jsx)(l.z8N,{className:"w-5 h-5"}),description:"Manage your savings goals and automated plans"},{name:"Investments",path:"/dashboard/investments",icon:(0,r.jsx)(l.ARf,{className:"w-5 h-5"}),description:"Portfolio management and investment tracking"},{name:"Goals",path:"/dashboard/goals",icon:(0,r.jsx)(l.x_j,{className:"w-5 h-5"}),description:"Set and track your financial goals"},{name:"Transactions",path:"/dashboard/transactions",icon:(0,r.jsx)(l.lZI,{className:"w-5 h-5"}),description:"View transaction history and manage payments"},{name:"Analytics",path:"/dashboard/analytics",icon:(0,r.jsx)(l.eXT,{className:"w-5 h-5"}),description:"Detailed financial analytics and insights"},{name:"Settings",path:"/dashboard/settings",icon:(0,r.jsx)(l.VSk,{className:"w-5 h-5"}),description:"Account settings and preferences"}];return s?(0,r.jsx)("div",{className:"min-h-screen bg-black text-white flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-green-400",children:"Loading authentication status..."})]})}):(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 text-white p-8",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gray-900/50 border border-gray-800 rounded-lg p-8 mb-8",children:[(0,r.jsx)("h1",{className:"text-4xl font-display font-bold mb-6 text-center text-shadow-green",children:"\uD83D\uDD10 BetterInterest Demo Access"}),(0,r.jsx)("p",{className:"text-center text-gray-300 mb-8 max-w-3xl mx-auto",children:"Use these demo credentials to explore all features of the BetterInterest platform. Each account type provides different levels of access and functionality."}),(0,r.jsxs)("div",{className:"mb-12",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-6 text-center",children:"\uD83C\uDFAF Demo Credentials"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[{type:"Regular User",email:"<EMAIL>",password:"Demo123!",description:"Access to user dashboard, savings plans, and personal features",color:"from-green-400 to-green-600"},{type:"Premium User",email:"<EMAIL>",password:"Premium123!",description:"Access to premium features, investment portfolios, and advanced analytics",color:"from-blue-400 to-blue-600"},{type:"Admin User",email:"<EMAIL>",password:"Admin123!",description:"Full admin access to all features, user management, and system settings",color:"from-purple-400 to-purple-600"},{type:"Test User",email:"<EMAIL>",password:"Test123!",description:"Testing account with sample data and transactions",color:"from-orange-400 to-orange-600"}].map((e,t)=>(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},className:"bg-gray-800/50 border border-gray-700 rounded-lg p-6 hover:border-green-400/50 transition-all",children:[(0,r.jsx)("div",{className:"inline-block px-3 py-1 rounded-full text-sm font-semibold mb-3 bg-gradient-to-r ".concat(e.color," text-white"),children:e.type}),(0,r.jsxs)("div",{className:"space-y-3 mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Email:"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("code",{className:"bg-gray-700 px-2 py-1 rounded text-green-400 text-sm",children:e.email}),(0,r.jsx)("button",{onClick:()=>p(e.email,"".concat(e.type,"-email")),className:"text-gray-400 hover:text-green-400 transition-colors",children:h==="".concat(e.type,"-email")?(0,r.jsx)(l.YrT,{}):(0,r.jsx)(l.nxz,{})})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Password:"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("code",{className:"bg-gray-700 px-2 py-1 rounded text-green-400 text-sm",children:e.password}),(0,r.jsx)("button",{onClick:()=>p(e.password,"".concat(e.type,"-password")),className:"text-gray-400 hover:text-green-400 transition-colors",children:h==="".concat(e.type,"-password")?(0,r.jsx)(l.YrT,{}):(0,r.jsx)(l.nxz,{})})]})]})]}),(0,r.jsx)("p",{className:"text-gray-300 text-sm mb-4",children:e.description}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(d.jn,{href:"/auth/login",size:"sm",className:"flex-1",children:"Login"}),(0,r.jsx)(d.rp,{href:"/auth/signup",size:"sm",className:"flex-1",children:"Signup"})]})]},t))})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold mb-4 flex items-center",children:[(0,r.jsx)(l.pcC,{className:"mr-2"}),"Authentication Status"]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Status:"})," ",(0,r.jsx)("span",{className:t?"text-green-400":"text-red-400",children:t?"Authenticated":"Not Authenticated"})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Loading:"})," ",(0,r.jsx)("span",{className:s?"text-yellow-400":"text-gray-300",children:s?"Yes":"No"})]})]})]}),(0,r.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold mb-4 flex items-center",children:[(0,r.jsx)(l.JXP,{className:"mr-2"}),"User Information"]}),e?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Name:"})," ",(0,r.jsxs)("span",{className:"text-white",children:[e.firstName," ",e.lastName]})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Email:"})," ",(0,r.jsx)("span",{className:"text-white",children:e.email})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Role:"})," ",(0,r.jsx)("span",{className:"ADMIN"===e.role?"text-red-400":"text-green-400",children:e.role})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Verified:"})," ",(0,r.jsx)("span",{className:e.isVerified?"text-green-400":"text-yellow-400",children:e.isVerified?"Yes":"No"})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"text-gray-400",children:"KYC Status:"})," ",(0,r.jsx)("span",{className:"APPROVED"===e.kycStatus?"text-green-400":"REJECTED"===e.kycStatus?"text-red-400":"text-yellow-400",children:e.kycStatus})]})]}):(0,r.jsx)("p",{className:"text-gray-400",children:"No user data available"})]})]}),t&&(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-6 text-center",children:"\uD83D\uDE80 Dashboard Pages"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:g.map((e,t)=>(0,r.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.05*t},children:(0,r.jsxs)(o(),{href:e.path,className:"block bg-gray-800/50 border border-gray-700 rounded-lg p-4 hover:border-green-400/50 hover:bg-gray-800/70 transition-all group",children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)("div",{className:"text-green-400 mr-3 group-hover:scale-110 transition-transform",children:e.icon}),(0,r.jsx)("h3",{className:"font-semibold text-white group-hover:text-green-400 transition-colors",children:e.name})]}),(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:e.description})]})},t))})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-4 justify-center",children:[(0,r.jsxs)(d.jn,{href:"/",children:[(0,r.jsx)(l.V5Y,{className:"mr-2"}),"Home"]}),t?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(d.jn,{href:(null==e?void 0:e.role)==="ADMIN"?"/admin/dashboard":"/dashboard",children:[(0,r.jsx)(l.JXP,{className:"mr-2"}),"Dashboard"]}),(0,r.jsxs)(d.rp,{onClick:u,children:[(0,r.jsx)(l.QeK,{className:"mr-2"}),"Logout"]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(d.jn,{href:"/auth/login",children:[(0,r.jsx)(l.JXP,{className:"mr-2"}),"Login"]}),(0,r.jsxs)(d.rp,{href:"/auth/signup",children:[(0,r.jsx)(l.JXP,{className:"mr-2"}),"Sign Up"]}),(0,r.jsxs)(d.rp,{href:"/admin/login",children:[(0,r.jsx)(l.pcC,{className:"mr-2"}),"Admin Login"]})]})]}),(0,r.jsxs)("div",{className:"mt-8 bg-gray-800/50 rounded-lg p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"API Configuration"}),(0,r.jsxs)("p",{className:"text-gray-400",children:["API Base URL: ",(0,r.jsx)("span",{className:"text-white",children:"http://localhost:8080"})]}),(0,r.jsx)("p",{className:"text-sm text-yellow-400 mt-2",children:"Note: This is a test page to verify authentication flow. The API endpoints are not implemented yet."})]})]})})})}},96365:(e,t,s)=>{"use strict";s.d(t,{authService:()=>x});class r extends Error{static fromResponse(e){return new r(e.message||e.error,500,e.code,e.details,e.timestamp)}toJSON(){return{name:this.name,message:this.message,statusCode:this.statusCode,code:this.code,details:this.details,timestamp:this.timestamp}}constructor(e,t,s,r,a){super(e),this.statusCode=t,this.code=s,this.details=r,this.timestamp=a,this.name="ApiError",this.timestamp=a||new Date().toISOString()}}class a extends r{constructor(e,t){super(e,422,"VALIDATION_ERROR",t),this.validationErrors=t,this.name="ValidationError"}}class n extends r{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class o extends r{constructor(e="Access denied"){super(e,403,"AUTHORIZATION_ERROR"),this.name="AuthorizationError"}}class i extends r{constructor(e="Resource not found"){super(e,404,"NOT_FOUND_ERROR"),this.name="NotFoundError"}}class l extends r{constructor(e="Network error occurred"){super(e,0,"NETWORK_ERROR"),this.name="NetworkError"}}class c extends r{constructor(e="Request timeout"){super(e,408,"TIMEOUT_ERROR"),this.name="TimeoutError"}}class d extends r{constructor(e="Internal server error"){super(e,500,"SERVER_ERROR"),this.name="ServerError"}}class h extends r{constructor(e="Service temporarily unavailable"){super(e,503,"SERVICE_UNAVAILABLE"),this.name="ServiceUnavailableError"}}let m=async e=>{let t;try{t=await e.json()}catch(s){t={error:"Unknown error occurred",message:"HTTP ".concat(e.status,": ").concat(e.statusText)}}switch(e.status){case 400:if(t.errors)throw new a(t.message||"Validation failed",t.errors);throw new r(t.message||t.error||"Bad request",400,t.code,t.details);case 401:throw new n(t.message||t.error);case 403:throw new o(t.message||t.error);case 404:throw new i(t.message||t.error);case 408:throw new c(t.message||t.error);case 422:throw new a(t.message||"Validation failed",t.errors||t.details||{});case 500:throw new d(t.message||t.error);case 503:throw new h(t.message||t.error);default:throw new r(t.message||t.error||"Request failed",e.status,t.code,t.details)}},u=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e4,a=new AbortController,n=setTimeout(()=>a.abort(),s);try{let s=await fetch(e,{...t,signal:a.signal,headers:{"Content-Type":"application/json",...t.headers}});clearTimeout(n),s.ok||await m(s);let o=s.headers.get("content-type");if(!o||!o.includes("application/json"))return{};let i=await s.json();if(!1===i.success)throw r.fromResponse(i);return i.data||i}catch(e){if(clearTimeout(n),e instanceof r)throw e;throw(e=>{if("AbortError"===e.name)throw new c("Request was cancelled");if("TypeError"===e.name&&e.message.includes("fetch"))throw new l("Network connection failed");throw new l(e.message||"Network error occurred")})(e)}},p="http://localhost:8080";class g{getAuthHeaders(){let e=localStorage.getItem("auth_token");return{"Content-Type":"application/json",...e&&{Authorization:"Bearer ".concat(e)}}}async login(e){var t,s;let r=await u("".concat(p,"/api/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});console.log("LOGIN RESPONSE:",r);let a=(null==r||null==(t=r.data)?void 0:t.token)||(null==r?void 0:r.token),n=(null==r||null==(s=r.data)?void 0:s.refreshToken)||(null==r?void 0:r.refreshToken);return a?(console.log("Saving auth_token:",a),localStorage.setItem("auth_token",a)):console.warn("No token found in login response:",r),n&&localStorage.setItem("refresh_token",n),r}async signup(e){var t,s,r,a;let n=await u("".concat(p,"/api/auth/signup"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),o=(null==n||null==(t=n.data)?void 0:t.token)||(null==n?void 0:n.token),i=(null==n||null==(s=n.data)?void 0:s.refreshToken)||(null==n?void 0:n.refreshToken),l=(null==n||null==(r=n.data)?void 0:r.user)||(null==n?void 0:n.user);return o&&localStorage.setItem("auth_token",o),i&&localStorage.setItem("refresh_token",i),{user:l,token:o,refreshToken:i,expiresIn:(null==n||null==(a=n.data)?void 0:a.expiresIn)||(null==n?void 0:n.expiresIn)||3600}}async adminLogin(e){let t=await fetch("".concat(p,"/auth/admin/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Admin login failed");let s=await t.json();return localStorage.setItem("auth_token",s.token),s.refreshToken&&localStorage.setItem("refresh_token",s.refreshToken),s}async logout(){try{await fetch("".concat(p,"/auth/logout"),{method:"POST",headers:this.getAuthHeaders()})}catch(e){console.error("Logout error:",e)}finally{localStorage.removeItem("auth_token"),localStorage.removeItem("refresh_token")}}async getCurrentUser(){let e=await fetch("".concat(p,"/api/auth/user-info"),{headers:this.getAuthHeaders()});if(!e.ok)throw Error("Failed to get current user");return(await e.json()).user}async refreshToken(){let e=localStorage.getItem("refresh_token");if(!e)throw Error("No refresh token available");let t=await fetch("".concat(p,"/auth/refresh"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e})});if(!t.ok)throw Error("Token refresh failed");let s=await t.json();return localStorage.setItem("auth_token",s.data.token),s.data.refreshToken&&localStorage.setItem("refresh_token",s.data.refreshToken),{success:s.success,token:s.data.token,refreshToken:s.data.refreshToken,expiresIn:s.data.expiresIn||3600}}async requestPasswordReset(e){let t=await fetch("".concat(p,"/auth/password-reset/request"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Password reset request failed")}async confirmPasswordReset(e){let t=await fetch("".concat(p,"/auth/password-reset/confirm"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Password reset failed")}async changePassword(e){let t=await fetch("".concat(p,"/auth/change-password"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Password change failed")}getStoredToken(){return localStorage.getItem("auth_token")}isTokenExpired(e){try{let t=JSON.parse(atob(e.split(".")[1]));return 1e3*t.exp<Date.now()}catch(e){return!0}}}let x=new g},98030:(e,t,s)=>{"use strict";s.d(t,{A:()=>d,AuthProvider:()=>c});var r=s(95155),a=s(12115),n=s(96365);let o={user:null,token:null,isAuthenticated:!1,isLoading:!0,error:null};function i(e,t){switch(t.type){case"AUTH_START":return{...e,isLoading:!0,error:null};case"AUTH_SUCCESS":return{...e,user:t.payload.user,token:t.payload.token,isAuthenticated:!0,isLoading:!1,error:null};case"AUTH_FAILURE":return{...e,user:null,token:null,isAuthenticated:!1,isLoading:!1,error:t.payload};case"LOGOUT":return{...e,user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null};case"UPDATE_USER":return{...e,user:t.payload};case"CLEAR_ERROR":return{...e,error:null};default:return e}}let l=(0,a.createContext)(void 0);function c(e){let{children:t}=e,[s,c]=(0,a.useReducer)(i,o);(0,a.useEffect)(()=>{d()},[]),(0,a.useEffect)(()=>{if(s.token&&s.isAuthenticated){let e=setInterval(async()=>{try{s.token&&n.authService.isTokenExpired(s.token)&&await n.authService.refreshToken()}catch(e){console.error("Token refresh failed:",e),p()}},3e5);return()=>clearInterval(e)}},[s.token,s.isAuthenticated]);let d=async()=>{try{let e=n.authService.getStoredToken();if(!e)return void c({type:"AUTH_FAILURE",payload:"No token found"});if(n.authService.isTokenExpired(e))try{await n.authService.refreshToken();let e=n.authService.getStoredToken();if(e){let t=await n.authService.getCurrentUser();c({type:"AUTH_SUCCESS",payload:{user:t,token:e}})}else throw Error("Token refresh failed")}catch(e){c({type:"AUTH_FAILURE",payload:"Session expired"});return}else{let t=await n.authService.getCurrentUser();c({type:"AUTH_SUCCESS",payload:{user:t,token:e}})}}catch(e){c({type:"AUTH_FAILURE",payload:"Authentication check failed"})}},h=async e=>{try{c({type:"AUTH_START"});let t=await n.authService.login(e);c({type:"AUTH_SUCCESS",payload:{user:t.user,token:t.token}})}catch(e){throw c({type:"AUTH_FAILURE",payload:e instanceof Error?e.message:"Login failed"}),e}},m=async e=>{try{c({type:"AUTH_START"});let t=await n.authService.signup(e);c({type:"AUTH_SUCCESS",payload:{user:t.user,token:t.token}})}catch(e){throw c({type:"AUTH_FAILURE",payload:e instanceof Error?e.message:"Signup failed"}),e}},u=async e=>{try{c({type:"AUTH_START"});let t=await n.authService.adminLogin(e);c({type:"AUTH_SUCCESS",payload:{user:t.user,token:t.token}})}catch(e){throw c({type:"AUTH_FAILURE",payload:e instanceof Error?e.message:"Admin login failed"}),e}},p=async()=>{try{await n.authService.logout()}catch(e){console.error("Logout error:",e)}finally{c({type:"LOGOUT"})}},g={...s,login:h,signup:m,adminLogin:u,logout:p,updateUser:e=>{c({type:"UPDATE_USER",payload:e})},clearError:()=>{c({type:"CLEAR_ERROR"})},checkAuth:d};return(0,r.jsx)(l.Provider,{value:g,children:t})}function d(){let e=(0,a.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}}},e=>{e.O(0,[844,5236,6874,8441,5964,7358],()=>e(e.s=40855)),_N_E=e.O()}]);