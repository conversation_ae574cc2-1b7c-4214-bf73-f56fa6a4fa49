"use client";

import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useAuth } from '../../hooks/use-auth';
import { BetterInterestLogo } from '../ui/BetterInterestLogo';

interface MobileSplashScreenProps {
  onComplete?: () => void;
  duration?: number;
}

export default function MobileSplashScreen({ 
  onComplete, 
  duration = 3000 
}: MobileSplashScreenProps) {
  const [isVisible, setIsVisible] = useState(true);
  const router = useRouter();
  const { user, isLoading } = useAuth();

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      
      // Navigate based on authentication status
      setTimeout(() => {
        if (!isLoading) {
          if (user) {
            router.push('/dashboard');
          } else {
            router.push('/auth/login');
          }
        }
        onComplete?.();
      }, 500); // Wait for fade out animation
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onComplete, router, user, isLoading]);

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
      className="fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-gray-900 via-green-900 to-black"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(34,197,94,0.1),transparent_50%)]" />
        <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(34,197,94,0.05)_50%,transparent_75%)]" />
      </div>

      {/* Logo Container */}
      <motion.div
        initial={{ scale: 0.5, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ 
          duration: 1,
          ease: "easeOut",
          delay: 0.2
        }}
        className="relative z-10 flex flex-col items-center"
      >
        {/* Main Logo */}
        <motion.div
          animate={{ 
            scale: [1, 1.05, 1],
            rotate: [0, 2, -2, 0]
          }}
          transition={{ 
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="mb-8"
        >
          <BetterInterestLogo 
            size="xl" 
            variant="gradient" 
            showIcon={true}
            className="text-6xl md:text-8xl"
          />
        </motion.div>

        {/* App Name */}
        <motion.h1
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.6 }}
          className="text-2xl md:text-3xl font-bold text-white mb-2 text-center"
        >
          Better Interest
        </motion.h1>

        {/* Tagline */}
        <motion.p
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 1, duration: 0.6 }}
          className="text-gray-300 text-center text-sm md:text-base max-w-xs"
        >
          Smart Savings, Better Returns
        </motion.p>

        {/* Loading Animation */}
        <motion.div
          initial={{ width: 0 }}
          animate={{ width: "100%" }}
          transition={{ delay: 1.5, duration: 1.5, ease: "easeInOut" }}
          className="mt-8 h-1 bg-gradient-to-r from-green-400 to-green-600 rounded-full max-w-xs w-full"
        />
      </motion.div>

      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            initial={{ 
              x: Math.random() * window.innerWidth,
              y: window.innerHeight + 100,
              opacity: 0
            }}
            animate={{ 
              y: -100,
              opacity: [0, 0.6, 0],
              x: Math.random() * window.innerWidth
            }}
            transition={{ 
              duration: 3 + Math.random() * 2,
              delay: Math.random() * 2,
              repeat: Infinity,
              ease: "linear"
            }}
            className="absolute w-2 h-2 bg-green-400 rounded-full"
          />
        ))}
      </div>
    </motion.div>
  );
}
