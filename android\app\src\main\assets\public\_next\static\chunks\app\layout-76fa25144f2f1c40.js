(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{15863:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'"},className:"__className_245e56",variable:"__variable_245e56"}},19324:()=>{},21921:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,69243,23)),Promise.resolve().then(r.t.bind(r,15863,23)),Promise.resolve().then(r.t.bind(r,19324,23)),Promise.resolve().then(r.bind(r,64198)),Promise.resolve().then(r.bind(r,57740)),Promise.resolve().then(r.bind(r,98030))},42714:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return n}});let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},o=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function a(e){return["async","defer","noModule"].includes(e)}function n(e,t){for(let[n,s]of Object.entries(t)){if(!t.hasOwnProperty(n)||o.includes(n)||void 0===s)continue;let i=r[n]||n.toLowerCase();"SCRIPT"===e.tagName&&a(i)?e[i]=!!s:e.setAttribute(i,String(s)),(!1===s||"SCRIPT"===e.tagName&&a(i)&&(!s||"false"===s))&&(e.setAttribute(i,""),e.removeAttribute(i))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},57740:(e,t,r)=>{"use strict";r.d(t,{DP:()=>i,ThemeProvider:()=>s,Yx:()=>c});var o=r(95155),a=r(12115);let n=(0,a.createContext)(void 0);function s(e){let{children:t}=e,[r,s]=(0,a.useState)("dark"),[i,l]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{l(!0);let e=localStorage.getItem("theme");e?s(e):s("dark")},[]),(0,a.useEffect)(()=>{i&&(document.documentElement.classList.remove("light","dark"),document.documentElement.classList.add(r),localStorage.setItem("theme",r))},[r,i]),i)?(0,o.jsx)(n.Provider,{value:{theme:r,toggleTheme:()=>{s(e=>"light"===e?"dark":"light")},setTheme:e=>{s(e)}},children:t}):(0,o.jsx)("div",{className:"min-h-screen bg-black",children:t})}function i(){let e=(0,a.useContext)(n);return void 0===e?(console.warn("useTheme called outside of ThemeProvider, using fallback values"),{theme:"dark",toggleTheme:()=>{},setTheme:()=>{}}):e}let l={light:{bg:{primary:"bg-white",secondary:"bg-gray-50",tertiary:"bg-gray-100",card:"bg-white",sidebar:"bg-white",header:"bg-white/80"},text:{primary:"text-gray-900",secondary:"text-gray-600",tertiary:"text-gray-500",inverse:"text-white"},border:{primary:"border-gray-200",secondary:"border-gray-300",accent:"border-green-200"},button:{primary:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white hover:shadow-green-500/25",secondary:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white hover:shadow-blue-500/25",outline:"bg-transparent border-2 border-green-500 text-green-600 hover:bg-green-500 hover:text-white"},accent:{green:"text-green-600",blue:"text-blue-600",purple:"text-purple-600",yellow:"text-yellow-600"}},dark:{bg:{primary:"bg-black",secondary:"bg-gray-900",tertiary:"bg-gray-800",card:"bg-gray-900",sidebar:"bg-gray-900",header:"bg-gray-900/80"},text:{primary:"text-white",secondary:"text-gray-300",tertiary:"text-gray-400",inverse:"text-black"},border:{primary:"border-gray-800",secondary:"border-gray-700",accent:"border-green-500/30"},button:{primary:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white hover:shadow-green-500/25",secondary:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white hover:shadow-blue-500/25",outline:"bg-transparent border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-black"},accent:{green:"text-green-400",blue:"text-blue-400",purple:"text-purple-400",yellow:"text-yellow-400"}}};function c(e){return l[e]}},64198:(e,t,r)=>{"use strict";r.d(t,{CustomToaster:()=>l,P0:()=>i,oR:()=>n.Ay});var o=r(95155),a=r(68289),n=r(13568),s=r(10351);let i={success:e=>{n.Ay.success(e,{duration:4e3,position:"top-right",style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}})},error:e=>{n.Ay.error(e,{duration:5e3,position:"top-right",style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}})},warning:e=>{(0,n.Ay)(e,{duration:4e3,position:"top-right",icon:"⚠️",style:{background:"#92400E",color:"#FFFBEB",border:"1px solid #F59E0B"}})},info:e=>{(0,n.Ay)(e,{duration:4e3,position:"top-right",icon:"ℹ️",style:{background:"#1E3A8A",color:"#EFF6FF",border:"1px solid #3B82F6"}})},loading:e=>n.Ay.loading(e,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"}}),dismiss:e=>{n.Ay.dismiss(e)},promise:(e,t)=>n.Ay.promise(e,t,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"},success:{style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}})};function l(){return(0,o.jsx)(n.l$,{position:"top-right",reverseOrder:!1,gutter:8,containerClassName:"",containerStyle:{},toastOptions:{className:"",duration:4e3,style:{background:"#1F2937",color:"#F9FAFB",border:"1px solid #374151",borderRadius:"8px",fontSize:"14px",fontWeight:"500",padding:"12px 16px",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"},success:{iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}},children:e=>(0,o.jsx)(n.bv,{toast:e,children:t=>{let{icon:r,message:i}=t;return(0,o.jsxs)(a.P.div,{initial:{opacity:0,y:-50,scale:.3},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.3},transition:{type:"spring",stiffness:500,damping:30,duration:.3},className:"flex items-center space-x-3",children:[(0,o.jsx)("div",{className:"flex-shrink-0",children:r}),(0,o.jsx)("div",{className:"flex-1",children:i}),"loading"!==e.type&&(0,o.jsx)("button",{onClick:()=>n.Ay.dismiss(e.id),className:"flex-shrink-0 p-1 rounded-full hover:bg-gray-600 transition-colors",children:(0,o.jsx)(s.yGN,{className:"w-4 h-4"})})]})}})})}},69243:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return m},handleClientScriptLoad:function(){return g},initScriptLoader:function(){return p}});let o=r(88229),a=r(6966),n=r(95155),s=o._(r(47650)),i=a._(r(12115)),l=r(82830),c=r(42714),d=r(92374),u=new Map,h=new Set,f=e=>{let{src:t,id:r,onLoad:o=()=>{},onReady:a=null,dangerouslySetInnerHTML:n,children:i="",strategy:l="afterInteractive",onError:d,stylesheets:f}=e,g=r||t;if(g&&h.has(g))return;if(u.has(t)){h.add(g),u.get(t).then(o,d);return}let p=()=>{a&&a(),h.add(g)},y=document.createElement("script"),m=new Promise((e,t)=>{y.addEventListener("load",function(t){e(),o&&o.call(this,t),p()}),y.addEventListener("error",function(e){t(e)})}).catch(function(e){d&&d(e)});n?(y.innerHTML=n.__html||"",p()):i?(y.textContent="string"==typeof i?i:Array.isArray(i)?i.join(""):"",p()):t&&(y.src=t,u.set(t,m)),(0,c.setAttributesFromProps)(y,e),"worker"===l&&y.setAttribute("type","text/partytown"),y.setAttribute("data-nscript",l),f&&(e=>{if(s.default.preinit)return e.forEach(e=>{s.default.preinit(e,{as:"style"})});{let t=document.head;e.forEach(e=>{let r=document.createElement("link");r.type="text/css",r.rel="stylesheet",r.href=e,t.appendChild(r)})}})(f),document.body.appendChild(y)};function g(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,d.requestIdleCallback)(()=>f(e))}):f(e)}function p(e){e.forEach(g),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");h.add(t)})}function y(e){let{id:t,src:r="",onLoad:o=()=>{},onReady:a=null,strategy:c="afterInteractive",onError:u,stylesheets:g,...p}=e,{updateScripts:y,scripts:m,getIsSsr:b,appDir:w,nonce:v}=(0,i.useContext)(l.HeadManagerContext);v=p.nonce||v;let k=(0,i.useRef)(!1);(0,i.useEffect)(()=>{let e=t||r;k.current||(a&&e&&h.has(e)&&a(),k.current=!0)},[a,t,r]);let E=(0,i.useRef)(!1);if((0,i.useEffect)(()=>{if(!E.current){if("afterInteractive"===c)f(e);else"lazyOnload"===c&&("complete"===document.readyState?(0,d.requestIdleCallback)(()=>f(e)):window.addEventListener("load",()=>{(0,d.requestIdleCallback)(()=>f(e))}));E.current=!0}},[e,c]),("beforeInteractive"===c||"worker"===c)&&(y?(m[c]=(m[c]||[]).concat([{id:t,src:r,onLoad:o,onReady:a,onError:u,...p,nonce:v}]),y(m)):b&&b()?h.add(t||r):b&&!b()&&f({...e,nonce:v})),w){if(g&&g.forEach(e=>{s.default.preinit(e,{as:"style"})}),"beforeInteractive"===c)if(!r)return p.dangerouslySetInnerHTML&&(p.children=p.dangerouslySetInnerHTML.__html,delete p.dangerouslySetInnerHTML),(0,n.jsx)("script",{nonce:v,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...p,id:t}])+")"}});else return s.default.preload(r,p.integrity?{as:"script",integrity:p.integrity,nonce:v,crossOrigin:p.crossOrigin}:{as:"script",nonce:v,crossOrigin:p.crossOrigin}),(0,n.jsx)("script",{nonce:v,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([r,{...p,id:t}])+")"}});"afterInteractive"===c&&r&&s.default.preload(r,p.integrity?{as:"script",integrity:p.integrity,nonce:v,crossOrigin:p.crossOrigin}:{as:"script",nonce:v,crossOrigin:p.crossOrigin})}return null}Object.defineProperty(y,"__nextScript",{value:!0});let m=y;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},92374:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return o},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},o="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96365:(e,t,r)=>{"use strict";r.d(t,{authService:()=>y});class o extends Error{static fromResponse(e){return new o(e.message||e.error,500,e.code,e.details,e.timestamp)}toJSON(){return{name:this.name,message:this.message,statusCode:this.statusCode,code:this.code,details:this.details,timestamp:this.timestamp}}constructor(e,t,r,o,a){super(e),this.statusCode=t,this.code=r,this.details=o,this.timestamp=a,this.name="ApiError",this.timestamp=a||new Date().toISOString()}}class a extends o{constructor(e,t){super(e,422,"VALIDATION_ERROR",t),this.validationErrors=t,this.name="ValidationError"}}class n extends o{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class s extends o{constructor(e="Access denied"){super(e,403,"AUTHORIZATION_ERROR"),this.name="AuthorizationError"}}class i extends o{constructor(e="Resource not found"){super(e,404,"NOT_FOUND_ERROR"),this.name="NotFoundError"}}class l extends o{constructor(e="Network error occurred"){super(e,0,"NETWORK_ERROR"),this.name="NetworkError"}}class c extends o{constructor(e="Request timeout"){super(e,408,"TIMEOUT_ERROR"),this.name="TimeoutError"}}class d extends o{constructor(e="Internal server error"){super(e,500,"SERVER_ERROR"),this.name="ServerError"}}class u extends o{constructor(e="Service temporarily unavailable"){super(e,503,"SERVICE_UNAVAILABLE"),this.name="ServiceUnavailableError"}}let h=async e=>{let t;try{t=await e.json()}catch(r){t={error:"Unknown error occurred",message:"HTTP ".concat(e.status,": ").concat(e.statusText)}}switch(e.status){case 400:if(t.errors)throw new a(t.message||"Validation failed",t.errors);throw new o(t.message||t.error||"Bad request",400,t.code,t.details);case 401:throw new n(t.message||t.error);case 403:throw new s(t.message||t.error);case 404:throw new i(t.message||t.error);case 408:throw new c(t.message||t.error);case 422:throw new a(t.message||"Validation failed",t.errors||t.details||{});case 500:throw new d(t.message||t.error);case 503:throw new u(t.message||t.error);default:throw new o(t.message||t.error||"Request failed",e.status,t.code,t.details)}},f=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e4,a=new AbortController,n=setTimeout(()=>a.abort(),r);try{let r=await fetch(e,{...t,signal:a.signal,headers:{"Content-Type":"application/json",...t.headers}});clearTimeout(n),r.ok||await h(r);let s=r.headers.get("content-type");if(!s||!s.includes("application/json"))return{};let i=await r.json();if(!1===i.success)throw o.fromResponse(i);return i.data||i}catch(e){if(clearTimeout(n),e instanceof o)throw e;throw(e=>{if("AbortError"===e.name)throw new c("Request was cancelled");if("TypeError"===e.name&&e.message.includes("fetch"))throw new l("Network connection failed");throw new l(e.message||"Network error occurred")})(e)}},g="http://localhost:8080";class p{getAuthHeaders(){let e=localStorage.getItem("auth_token");return{"Content-Type":"application/json",...e&&{Authorization:"Bearer ".concat(e)}}}async login(e){var t,r;let o=await f("".concat(g,"/api/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});console.log("LOGIN RESPONSE:",o);let a=(null==o||null==(t=o.data)?void 0:t.token)||(null==o?void 0:o.token),n=(null==o||null==(r=o.data)?void 0:r.refreshToken)||(null==o?void 0:o.refreshToken);return a?(console.log("Saving auth_token:",a),localStorage.setItem("auth_token",a)):console.warn("No token found in login response:",o),n&&localStorage.setItem("refresh_token",n),o}async signup(e){var t,r,o,a;let n=await f("".concat(g,"/api/auth/signup"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),s=(null==n||null==(t=n.data)?void 0:t.token)||(null==n?void 0:n.token),i=(null==n||null==(r=n.data)?void 0:r.refreshToken)||(null==n?void 0:n.refreshToken),l=(null==n||null==(o=n.data)?void 0:o.user)||(null==n?void 0:n.user);return s&&localStorage.setItem("auth_token",s),i&&localStorage.setItem("refresh_token",i),{user:l,token:s,refreshToken:i,expiresIn:(null==n||null==(a=n.data)?void 0:a.expiresIn)||(null==n?void 0:n.expiresIn)||3600}}async adminLogin(e){let t=await fetch("".concat(g,"/auth/admin/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Admin login failed");let r=await t.json();return localStorage.setItem("auth_token",r.token),r.refreshToken&&localStorage.setItem("refresh_token",r.refreshToken),r}async logout(){try{await fetch("".concat(g,"/auth/logout"),{method:"POST",headers:this.getAuthHeaders()})}catch(e){console.error("Logout error:",e)}finally{localStorage.removeItem("auth_token"),localStorage.removeItem("refresh_token")}}async getCurrentUser(){let e=await fetch("".concat(g,"/api/auth/user-info"),{headers:this.getAuthHeaders()});if(!e.ok)throw Error("Failed to get current user");return(await e.json()).user}async refreshToken(){let e=localStorage.getItem("refresh_token");if(!e)throw Error("No refresh token available");let t=await fetch("".concat(g,"/auth/refresh"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e})});if(!t.ok)throw Error("Token refresh failed");let r=await t.json();return localStorage.setItem("auth_token",r.data.token),r.data.refreshToken&&localStorage.setItem("refresh_token",r.data.refreshToken),{success:r.success,token:r.data.token,refreshToken:r.data.refreshToken,expiresIn:r.data.expiresIn||3600}}async requestPasswordReset(e){let t=await fetch("".concat(g,"/auth/password-reset/request"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Password reset request failed")}async confirmPasswordReset(e){let t=await fetch("".concat(g,"/auth/password-reset/confirm"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Password reset failed")}async changePassword(e){let t=await fetch("".concat(g,"/auth/change-password"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Password change failed")}getStoredToken(){return localStorage.getItem("auth_token")}isTokenExpired(e){try{let t=JSON.parse(atob(e.split(".")[1]));return 1e3*t.exp<Date.now()}catch(e){return!0}}}let y=new p},98030:(e,t,r)=>{"use strict";r.d(t,{A:()=>d,AuthProvider:()=>c});var o=r(95155),a=r(12115),n=r(96365);let s={user:null,token:null,isAuthenticated:!1,isLoading:!0,error:null};function i(e,t){switch(t.type){case"AUTH_START":return{...e,isLoading:!0,error:null};case"AUTH_SUCCESS":return{...e,user:t.payload.user,token:t.payload.token,isAuthenticated:!0,isLoading:!1,error:null};case"AUTH_FAILURE":return{...e,user:null,token:null,isAuthenticated:!1,isLoading:!1,error:t.payload};case"LOGOUT":return{...e,user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null};case"UPDATE_USER":return{...e,user:t.payload};case"CLEAR_ERROR":return{...e,error:null};default:return e}}let l=(0,a.createContext)(void 0);function c(e){let{children:t}=e,[r,c]=(0,a.useReducer)(i,s);(0,a.useEffect)(()=>{d()},[]),(0,a.useEffect)(()=>{if(r.token&&r.isAuthenticated){let e=setInterval(async()=>{try{r.token&&n.authService.isTokenExpired(r.token)&&await n.authService.refreshToken()}catch(e){console.error("Token refresh failed:",e),g()}},3e5);return()=>clearInterval(e)}},[r.token,r.isAuthenticated]);let d=async()=>{try{let e=n.authService.getStoredToken();if(!e)return void c({type:"AUTH_FAILURE",payload:"No token found"});if(n.authService.isTokenExpired(e))try{await n.authService.refreshToken();let e=n.authService.getStoredToken();if(e){let t=await n.authService.getCurrentUser();c({type:"AUTH_SUCCESS",payload:{user:t,token:e}})}else throw Error("Token refresh failed")}catch(e){c({type:"AUTH_FAILURE",payload:"Session expired"});return}else{let t=await n.authService.getCurrentUser();c({type:"AUTH_SUCCESS",payload:{user:t,token:e}})}}catch(e){c({type:"AUTH_FAILURE",payload:"Authentication check failed"})}},u=async e=>{try{c({type:"AUTH_START"});let t=await n.authService.login(e);c({type:"AUTH_SUCCESS",payload:{user:t.user,token:t.token}})}catch(e){throw c({type:"AUTH_FAILURE",payload:e instanceof Error?e.message:"Login failed"}),e}},h=async e=>{try{c({type:"AUTH_START"});let t=await n.authService.signup(e);c({type:"AUTH_SUCCESS",payload:{user:t.user,token:t.token}})}catch(e){throw c({type:"AUTH_FAILURE",payload:e instanceof Error?e.message:"Signup failed"}),e}},f=async e=>{try{c({type:"AUTH_START"});let t=await n.authService.adminLogin(e);c({type:"AUTH_SUCCESS",payload:{user:t.user,token:t.token}})}catch(e){throw c({type:"AUTH_FAILURE",payload:e instanceof Error?e.message:"Admin login failed"}),e}},g=async()=>{try{await n.authService.logout()}catch(e){console.error("Logout error:",e)}finally{c({type:"LOGOUT"})}},p={...r,login:u,signup:h,adminLogin:f,logout:g,updateUser:e=>{c({type:"UPDATE_USER",payload:e})},clearError:()=>{c({type:"CLEAR_ERROR"})},checkAuth:d};return(0,o.jsx)(l.Provider,{value:p,children:t})}function d(){let e=(0,a.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}}},e=>{e.O(0,[354,2533,844,5236,3568,8441,5964,7358],()=>e(e.s=21921)),_N_E=e.O()}]);