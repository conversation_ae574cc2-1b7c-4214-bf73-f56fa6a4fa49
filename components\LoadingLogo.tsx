"use client";

import React from 'react';
import { BetterInterestIcon } from '../src/components/ui/BetterInterestLogo';

interface LoadingLogoProps {
  size?: number;
  className?: string;
  showPulse?: boolean;
}

export const LoadingLogo: React.FC<LoadingLogoProps> = ({ 
  size = 48, 
  className = "",
  showPulse = true
}) => {
  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div className={`${showPulse ? 'animate-pulse' : ''}`}>
        <BetterInterestIcon
          size="md"
          className="text-green-400 drop-shadow-lg"
        />
      </div>
    </div>
  );
};

export default LoadingLogo;
