"use client";

import { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../lib/utils';

interface MobileCardProps {
  children: ReactNode;
  className?: string;
  padding?: 'sm' | 'md' | 'lg';
  shadow?: 'none' | 'sm' | 'md' | 'lg';
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  onClick?: () => void;
  hoverable?: boolean;
}

export default function MobileCard({
  children,
  className,
  padding = 'md',
  shadow = 'sm',
  rounded = 'lg',
  onClick,
  hoverable = false
}: MobileCardProps) {
  const baseClasses = "bg-white border border-gray-200 transition-all duration-200";
  
  const paddingClasses = {
    sm: "p-3",
    md: "p-4",
    lg: "p-6"
  };

  const shadowClasses = {
    none: "",
    sm: "shadow-sm",
    md: "shadow-md",
    lg: "shadow-lg"
  };

  const roundedClasses = {
    none: "",
    sm: "rounded-sm",
    md: "rounded-md",
    lg: "rounded-lg",
    xl: "rounded-xl"
  };

  const Component = onClick ? motion.button : motion.div;

  return (
    <Component
      whileTap={onClick ? { scale: 0.98 } : undefined}
      className={cn(
        baseClasses,
        paddingClasses[padding],
        shadowClasses[shadow],
        roundedClasses[rounded],
        onClick && "cursor-pointer",
        hoverable && "hover:shadow-md hover:border-gray-300",
        onClick && "active:scale-98",
        className
      )}
      onClick={onClick}
    >
      {children}
    </Component>
  );
}
