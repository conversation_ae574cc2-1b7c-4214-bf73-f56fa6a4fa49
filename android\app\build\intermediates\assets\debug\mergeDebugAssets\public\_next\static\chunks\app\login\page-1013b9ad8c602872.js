(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{22547:(e,t,s)=>{Promise.resolve().then(s.bind(s,78007))},35695:(e,t,s)=>{"use strict";var n=s(18999);s.o(n,"usePathname")&&s.d(t,{usePathname:function(){return n.usePathname}}),s.o(n,"useRouter")&&s.d(t,{useRouter:function(){return n.useRouter}})},78007:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var n=s(95155),u=s(35695),r=s(12115);function c(){let e=(0,u.useRouter)();return(0,r.useEffect)(()=>{e.push("/auth/login")},[e]),(0,n.jsx)("div",{className:"min-h-screen bg-black flex items-center justify-center",children:(0,n.jsx)("div",{className:"text-center",children:(0,n.jsx)("p",{className:"text-green-400 text-lg",children:"Redirecting to Login..."})})})}}},e=>{e.O(0,[8441,5964,7358],()=>e(e.s=22547)),_N_E=e.O()}]);