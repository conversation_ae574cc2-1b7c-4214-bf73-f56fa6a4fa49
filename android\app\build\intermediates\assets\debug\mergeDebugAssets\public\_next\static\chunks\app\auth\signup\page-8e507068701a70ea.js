(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2778],{585:(e,t,r)=>{"use strict";r.d(t,{L$:()=>i});var s=r(95155);r(12115);var a=r(68289);let n=[{left:10,top:20},{left:25,top:15},{left:40,top:30},{left:55,top:10},{left:70,top:25},{left:85,top:35},{left:15,top:45},{left:30,top:55},{left:45,top:40},{left:60,top:50},{left:75,top:60},{left:90,top:45},{left:5,top:70},{left:20,top:80},{left:35,top:65},{left:50,top:75},{left:65,top:85},{left:80,top:70},{left:95,top:80},{left:10,top:90},{left:25,top:5},{left:40,top:85},{left:55,top:95},{left:70,top:5},{left:85,top:15},{left:15,top:25},{left:30,top:35},{left:45,top:45},{left:60,top:55},{left:75,top:65},{left:90,top:75},{left:5,top:85},{left:20,top:95},{left:35,top:5},{left:50,top:15},{left:65,top:25},{left:80,top:35},{left:95,top:45}];function i(e){let{variant:t="default",className:r=""}=e,i={default:{gradient:"bg-gradient-to-br from-gray-900 via-green-900 to-black",particles:30,colors:["bg-green-400","bg-orange-400","bg-yellow-400"]},auth:{gradient:"bg-gradient-to-br from-gray-900 via-black to-gray-900",particles:20,colors:["bg-green-500","bg-orange-500","bg-yellow-500"]},dashboard:{gradient:"bg-gradient-to-br from-gray-900 via-gray-800 to-black",particles:15,colors:["bg-green-400","bg-orange-400","bg-yellow-400"]}}[t];return(0,s.jsxs)("div",{className:"fixed inset-0 z-0 ".concat(r),children:[(0,s.jsx)("div",{className:"absolute inset-0 ".concat(i.gradient," animate-gradient")}),(0,s.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,s.jsx)(a.P.div,{className:"absolute -top-40 -right-40 w-80 h-80 bg-orange-500 rounded-full mix-blend-multiply filter blur-xl opacity-20",animate:{x:[0,30,-20,0],y:[0,-50,20,0],scale:[1,1.1,.9,1]},transition:{duration:7,repeat:1/0,ease:"easeInOut"}}),(0,s.jsx)(a.P.div,{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20",animate:{x:[0,-30,20,0],y:[0,50,-20,0],scale:[1,.9,1.1,1]},transition:{duration:7,repeat:1/0,ease:"easeInOut",delay:2}}),(0,s.jsx)(a.P.div,{className:"absolute top-40 left-40 w-80 h-80 bg-amber-500 rounded-full mix-blend-multiply filter blur-xl opacity-20",animate:{x:[0,20,-30,0],y:[0,-30,40,0],scale:[1,1.2,.8,1]},transition:{duration:7,repeat:1/0,ease:"easeInOut",delay:4}})]}),(0,s.jsx)("div",{className:"absolute inset-0 opacity-20",children:[...Array(i.particles)].map((e,t)=>{let r=n[t%n.length];return(0,s.jsx)(a.P.div,{className:"absolute w-1 h-1 ".concat(i.colors[t%i.colors.length]," rounded-full"),style:{left:"".concat(r.left,"%"),top:"".concat(r.top,"%")},animate:{y:[0,-20,0],opacity:[.2,.8,.2],scale:[1,1.5,1]},transition:{duration:3+t%3,repeat:1/0,delay:t%4*.5,ease:"easeInOut"}},t)})}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-black/30"})]})}},9609:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var s=r(95155),a=r(11518),n=r.n(a),i=r(68289),o=r(6874),l=r.n(o),c=r(35695),d=r(12115),u=r(10351),f=r(31246),h=r(585),p=r(49697),m=r(64198);function x(){(0,c.useRouter)();let[e,t]=(0,d.useState)(!1),[r,a]=(0,d.useState)(!1),[o,x]=(0,d.useState)(!1),[g,y]=(0,d.useState)(1),[v,b]=(0,d.useState)({firstName:"",lastName:"",email:"",phoneNumber:"",password:"",confirmPassword:"",agreeToTerms:!1}),[j,w]=(0,d.useState)({score:0,feedback:[]}),N=(e,t)=>{b({...v,[e]:t}),"password"===e&&(e=>{let t=0,r=[];e.length>=8?t++:r.push("At least 8 characters"),/[A-Z]/.test(e)?t++:r.push("One uppercase letter"),/[a-z]/.test(e)?t++:r.push("One lowercase letter"),/\d/.test(e)?t++:r.push("One number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)?t++:r.push("One special character"),w({score:t,feedback:r})})(t)},S=async e=>{if(e.preventDefault(),v.password&&v.confirmPassword?v.password!==v.confirmPassword?(m.P0.error("Passwords do not match"),!1):j.score<3?(m.P0.error("Password is too weak. Please choose a stronger password."),!1):!!v.agreeToTerms||(m.P0.error("Please agree to the terms and conditions"),!1):(m.P0.error("Please fill in all password fields"),!1)){t(!0);try{console.log("API_BASE_URL:","http://localhost:8080"),console.log("Signup formData:",v);let e=await fetch("".concat("http://localhost:8080","/api/auth/signup"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(v)}),r=await e.json();if(console.log("Signup API response:",r),!e.ok||!r.data||!r.data.token){m.P0.error(r.error||r.message||"Signup failed"),t(!1);return}localStorage.setItem("auth_token",r.data.token),localStorage.setItem("user_data",JSON.stringify(r.data.user)),window.location.href="/dashboard"}catch(e){console.error("Signup error:",e),m.P0.error("Signup failed. Please try again.")}finally{t(!1)}}};return(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726 min-h-screen flex items-center justify-center p-4 relative",children:[(0,s.jsx)(h.L$,{variant:"auth"}),(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726 relative z-10 w-full max-w-6xl grid grid-cols-1 lg:grid-cols-2 gap-8 items-center",children:[(0,s.jsx)(i.P.div,{initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8},className:"hidden lg:flex justify-center",children:(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726 relative",children:[(0,s.jsx)(p.XT,{src:"/ChatGPT Image Jul 8, 2025, 03_09_42 PM.png",alt:"Join BetterInterest - Start Your Savings Journey",width:600,height:400,intensity:"strong",className:"w-full h-auto mx-auto"}),(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726 absolute bottom-6 left-6 bg-black/70 backdrop-blur-sm rounded-lg p-4 z-20",children:[(0,s.jsx)("h3",{className:"jsx-24f01c58ae8ac726 text-white font-semibold mb-1",children:"Start Your Journey"}),(0,s.jsx)("p",{className:"jsx-24f01c58ae8ac726 text-green-400 text-sm",children:"Better interest rates await you"})]})]})}),(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726 w-full max-w-md mx-auto lg:mx-0",children:[(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"text-center mb-8",children:[(0,s.jsx)("div",{className:"jsx-24f01c58ae8ac726 inline-flex items-center justify-center mb-4",children:(0,s.jsx)("span",{className:"jsx-24f01c58ae8ac726 text-3xl font-bold bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent",children:"BetterInterest"})}),(0,s.jsx)("h1",{className:"jsx-24f01c58ae8ac726 text-3xl font-display font-bold text-white mb-2 text-shadow",children:"Create Account"}),(0,s.jsx)("p",{className:"jsx-24f01c58ae8ac726 text-gray-400",children:"Join thousands saving for their future"})]}),(0,s.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"flex items-center justify-center mb-6",children:(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726 flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"jsx-24f01c58ae8ac726 "+"w-8 h-8 rounded-full flex items-center justify-center ".concat(g>=1?"bg-green-600 text-white":"bg-gray-700 text-gray-400"),children:g>1?(0,s.jsx)(u.YrT,{}):"1"}),(0,s.jsx)("div",{className:"jsx-24f01c58ae8ac726 "+"w-16 h-1 ".concat(g>=2?"bg-green-600":"bg-gray-700")}),(0,s.jsx)("div",{className:"jsx-24f01c58ae8ac726 "+"w-8 h-8 rounded-full flex items-center justify-center ".concat(g>=2?"bg-green-600 text-white":"bg-gray-700 text-gray-400"),children:"2"})]})}),(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"max-w-md mx-auto",children:[(0,s.jsxs)("form",{onSubmit:1===g?e=>{e.preventDefault(),1!==g||(v.firstName&&v.lastName&&v.email&&v.phoneNumber?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v.email)?!/^(\+234|234|0)?[789][01]\d{8}$/.test(v.phoneNumber)&&(m.P0.error("Please enter a valid Nigerian phone number"),1):(m.P0.error("Please enter a valid email address"),1):(m.P0.error("Please fill in all fields"),1))||y(2)}:S,className:"jsx-24f01c58ae8ac726 space-y-6",children:[1===g&&(0,s.jsxs)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"space-y-6",children:[(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726 grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726",children:[(0,s.jsx)("label",{className:"jsx-24f01c58ae8ac726 block text-sm font-medium text-gray-300 mb-2",children:"First Name"}),(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726 relative",children:[(0,s.jsx)(u.JXP,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,s.jsx)("input",{type:"text",value:v.firstName,onChange:e=>N("firstName",e.target.value),placeholder:"John",required:!0,className:"jsx-24f01c58ae8ac726 w-full pl-10 pr-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"})]})]}),(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726",children:[(0,s.jsx)("label",{className:"jsx-24f01c58ae8ac726 block text-sm font-medium text-gray-300 mb-2",children:"Last Name"}),(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726 relative",children:[(0,s.jsx)(u.JXP,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,s.jsx)("input",{type:"text",value:v.lastName,onChange:e=>N("lastName",e.target.value),placeholder:"Doe",required:!0,className:"jsx-24f01c58ae8ac726 w-full pl-10 pr-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"})]})]})]}),(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726",children:[(0,s.jsx)("label",{className:"jsx-24f01c58ae8ac726 block text-sm font-medium text-gray-300 mb-2",children:"Email Address"}),(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726 relative",children:[(0,s.jsx)(u.pHD,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,s.jsx)("input",{type:"email",value:v.email,onChange:e=>N("email",e.target.value),placeholder:"<EMAIL>",required:!0,className:"jsx-24f01c58ae8ac726 w-full pl-10 pr-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"})]})]}),(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726",children:[(0,s.jsx)("label",{className:"jsx-24f01c58ae8ac726 block text-sm font-medium text-gray-300 mb-2",children:"Phone Number"}),(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726 relative",children:[(0,s.jsx)(u.QFc,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,s.jsx)("input",{type:"tel",value:v.phoneNumber,onChange:e=>N("phoneNumber",e.target.value),placeholder:"+234 ************",required:!0,className:"jsx-24f01c58ae8ac726 w-full pl-10 pr-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"})]})]}),(0,s.jsx)(f.jn,{type:"submit",className:"w-full",children:"Continue"})]}),2===g&&(0,s.jsxs)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"space-y-6",children:[(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726",children:[(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726",children:[(0,s.jsx)("label",{className:"jsx-24f01c58ae8ac726 block text-sm font-medium text-gray-300 mb-2",children:"Password"}),(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726 relative",children:[(0,s.jsx)(u.F5$,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,s.jsx)("input",{type:r?"text":"password",value:v.password,onChange:e=>N("password",e.target.value),placeholder:"Create a strong password",required:!0,className:"jsx-24f01c58ae8ac726 w-full pl-10 pr-12 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"}),(0,s.jsx)("button",{type:"button",onClick:()=>a(!r),className:"jsx-24f01c58ae8ac726 absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white",children:r?(0,s.jsx)(u._NO,{}):(0,s.jsx)(u.Vap,{})})]})]}),v.password&&(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726 mt-2",children:[(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726 flex items-center justify-between text-sm mb-1",children:[(0,s.jsx)("span",{className:"jsx-24f01c58ae8ac726 text-gray-400",children:"Password strength:"}),(0,s.jsx)("span",{className:"jsx-24f01c58ae8ac726 "+"font-medium ".concat(j.score<=1?"text-red-400":j.score<=3?"text-yellow-400":"text-green-400"),children:j.score<=1?"Weak":j.score<=3?"Medium":"Strong"})]}),(0,s.jsx)("div",{className:"jsx-24f01c58ae8ac726 w-full bg-gray-700 rounded-full h-2",children:(0,s.jsx)("div",{style:{width:"".concat(j.score/5*100,"%")},className:"jsx-24f01c58ae8ac726 "+"h-2 rounded-full transition-all duration-300 ".concat(j.score<=1?"bg-red-500":j.score<=3?"bg-yellow-500":"bg-green-500")})}),j.feedback.length>0&&(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726 mt-2 text-xs text-gray-400",children:["Missing: ",j.feedback.join(", ")]})]})]}),(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726",children:[(0,s.jsx)("label",{className:"jsx-24f01c58ae8ac726 block text-sm font-medium text-gray-300 mb-2",children:"Confirm Password"}),(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726 relative",children:[(0,s.jsx)(u.F5$,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,s.jsx)("input",{type:o?"text":"password",value:v.confirmPassword,onChange:e=>N("confirmPassword",e.target.value),placeholder:"Confirm your password",required:!0,className:"jsx-24f01c58ae8ac726 w-full pl-10 pr-12 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"}),(0,s.jsx)("button",{type:"button",onClick:()=>x(!o),className:"jsx-24f01c58ae8ac726 absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white",children:o?(0,s.jsx)(u._NO,{}):(0,s.jsx)(u.Vap,{})})]})]}),(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726 flex items-start space-x-2",children:[(0,s.jsx)("input",{type:"checkbox",id:"agreeToTerms",checked:v.agreeToTerms,onChange:e=>N("agreeToTerms",e.target.checked),required:!0,className:"jsx-24f01c58ae8ac726 mt-1 rounded border-gray-600 bg-gray-700 text-green-600 focus:ring-green-500"}),(0,s.jsxs)("label",{htmlFor:"agreeToTerms",className:"jsx-24f01c58ae8ac726 text-sm text-gray-400",children:["I agree to the"," ",(0,s.jsx)(l(),{href:"/terms",className:"text-green-400 hover:text-green-300",children:"Terms of Service"})," ","and"," ",(0,s.jsx)(l(),{href:"/privacy",className:"text-green-400 hover:text-green-300",children:"Privacy Policy"})]})]}),(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726 flex space-x-3",children:[(0,s.jsx)(f.rp,{type:"button",onClick:()=>{2===g&&y(1)},className:"flex-1",children:"Back"}),(0,s.jsx)(f.jn,{type:"submit",className:"flex-1",disabled:e,children:e?(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726 flex items-center justify-center",children:[(0,s.jsx)("div",{className:"jsx-24f01c58ae8ac726 animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Creating..."]}):"Create Account"})]})]})]}),(0,s.jsx)("div",{className:"jsx-24f01c58ae8ac726 mt-6 text-center",children:(0,s.jsxs)("p",{className:"jsx-24f01c58ae8ac726 text-gray-400",children:["Already have an account?"," ",(0,s.jsx)(l(),{href:"/auth/login",className:"text-green-400 hover:text-green-300 font-medium",children:"Sign in here"})]})})]}),(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"mt-8 grid grid-cols-3 gap-4 text-center",children:[(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726 text-gray-400",children:[(0,s.jsx)("div",{className:"jsx-24f01c58ae8ac726 w-8 h-8 bg-green-600/20 rounded-lg flex items-center justify-center mx-auto mb-2",children:(0,s.jsx)("span",{className:"jsx-24f01c58ae8ac726 text-green-400 text-sm",children:"\uD83D\uDD12"})}),(0,s.jsx)("p",{className:"jsx-24f01c58ae8ac726 text-xs",children:"Bank-level Security"})]}),(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726 text-gray-400",children:[(0,s.jsx)("div",{className:"jsx-24f01c58ae8ac726 w-8 h-8 bg-green-600/20 rounded-lg flex items-center justify-center mx-auto mb-2",children:(0,s.jsx)("span",{className:"jsx-24f01c58ae8ac726 text-green-400 text-sm",children:"\uD83D\uDCB0"})}),(0,s.jsx)("p",{className:"jsx-24f01c58ae8ac726 text-xs",children:"High Interest Rates"})]}),(0,s.jsxs)("div",{className:"jsx-24f01c58ae8ac726 text-gray-400",children:[(0,s.jsx)("div",{className:"jsx-24f01c58ae8ac726 w-8 h-8 bg-green-600/20 rounded-lg flex items-center justify-center mx-auto mb-2",children:(0,s.jsx)("span",{className:"jsx-24f01c58ae8ac726 text-green-400 text-sm",children:"\uD83D\uDCF1"})}),(0,s.jsx)("p",{className:"jsx-24f01c58ae8ac726 text-xs",children:"Mobile First"})]})]})]})]}),(0,s.jsx)(n(),{id:"24f01c58ae8ac726",children:"@keyframes blob{0%{transform:translate(0px,0px)scale(1)}33%{transform:translate(30px,-50px)scale(1.1)}66%{transform:translate(-20px,20px)scale(.9)}100%{transform:translate(0px,0px)scale(1)}}.animate-blob.jsx-24f01c58ae8ac726{animation:blob 7s infinite}.animation-delay-2000.jsx-24f01c58ae8ac726{animation-delay:2s}.animation-delay-4000.jsx-24f01c58ae8ac726{animation-delay:4s}"})]})}},11518:(e,t,r)=>{"use strict";e.exports=r(82269).style},18980:(e,t,r)=>{Promise.resolve().then(r.bind(r,9609))},31246:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>c,jn:()=>o,rp:()=>l});var s=r(95155);r(12115);var a=r(68289);let n=()=>(0,s.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("circle",{cx:37,cy:37,r:"35.5",stroke:"currentColor",strokeWidth:3}),(0,s.jsx)("path",{d:"M25 35.5C24.1716 35.5 23.5 36.1716 23.5 37C23.5 37.8284 24.1716 38.5 25 38.5V35.5ZM49.0607 38.0607C49.6464 37.4749 49.6464 36.5251 49.0607 35.9393L39.5147 26.3934C38.9289 25.8076 37.9792 25.8076 37.3934 26.3934C36.8076 26.9792 36.8076 27.9289 37.3934 28.5147L45.8787 37L37.3934 45.4853C36.8076 46.0711 36.8076 47.0208 37.3934 47.6066C37.9792 48.1924 38.9289 48.1924 39.5147 47.6066L49.0607 38.0607ZM25 38.5L48 38.5V35.5L25 35.5V38.5Z",fill:"currentColor"})]});function i(e){let{children:t,onClick:r,href:i,variant:o="primary",size:l="md",disabled:c=!1,className:d="",type:u="button",icon:f}=e,h="\n    cursor-pointer font-bold font-sans transition-all duration-200 \n    border-2 border-transparent flex items-center justify-center\n    rounded-full relative overflow-hidden group\n    shadow-lg hover:shadow-xl active:shadow-inner\n    transform hover:-translate-y-1 active:translate-y-0\n    ".concat(c?"opacity-50 cursor-not-allowed":"","\n  "),p="\n    ".concat(h,"\n    ").concat({primary:"\n      bg-gradient-to-r from-green-400 to-green-600 \n      hover:from-green-500 hover:to-green-700\n      text-white border-green-500\n      active:border-green-600 active:shadow-inner\n    ",secondary:"\n      bg-gradient-to-r from-blue-400 to-blue-600 \n      hover:from-blue-500 hover:to-blue-700\n      text-white border-blue-500\n      active:border-blue-600 active:shadow-inner\n    ",outline:"\n      bg-transparent border-green-400 text-green-400\n      hover:bg-green-400 hover:text-black\n      active:border-green-500 active:shadow-inner\n    "}[o],"\n    ").concat({sm:"px-3 py-1.5 text-xs min-w-[80px]",md:"px-4 py-2 text-sm min-w-[100px]",lg:"px-6 py-3 text-base min-w-[120px]"}[l],"\n    ").concat(d,"\n  "),m=()=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-400/20 to-green-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full blur-xl"}),(0,s.jsxs)("span",{className:"relative z-10 flex items-center justify-center",children:[(0,s.jsx)("span",{className:"text",children:t}),(f||"primary"===o)&&(0,s.jsx)(a.P.div,{className:"ml-2 text-current",whileHover:{x:3},transition:{type:"spring",stiffness:400,damping:10},children:f||(0,s.jsx)(n,{})})]})]});return i?(0,s.jsx)(a.P.a,{href:i,className:p,whileHover:{scale:1.02,y:-1},whileTap:{scale:.96,y:1},transition:{type:"spring",stiffness:400,damping:10},children:(0,s.jsx)(m,{})}):(0,s.jsx)(a.P.button,{type:u,onClick:r,disabled:c,className:p,whileHover:{scale:c?1:1.02,y:c?0:-1},whileTap:{scale:c?1:.96,y:+!c},transition:{type:"spring",stiffness:400,damping:10},children:(0,s.jsx)(m,{})})}function o(e){return(0,s.jsx)(i,{...e,variant:"primary"})}function l(e){return(0,s.jsx)(i,{...e,variant:"outline"})}let c=i},35695:(e,t,r)=>{"use strict";var s=r(18999);r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},49697:(e,t,r)=>{"use strict";r.d(t,{HC:()=>u,XT:()=>h,co:()=>f,nv:()=>d});var s=r(95155),a=r(12115),n=r(66766),i=r(68289);let o={default:{container:"relative inline-block",wrapper:"relative overflow-hidden rounded-lg",transform:"none",hoverTransform:"scale(1.05)",shadow:"rgba(0, 0, 0, 0.24) 0px 8px 20px"},hero:{container:"relative inline-block",wrapper:"relative overflow-hidden rounded-xl",transform:"perspective(600px) rotateX(20deg) rotateZ(-8deg)",hoverTransform:"perspective(600px) rotateX(8deg) rotateY(15deg) rotateZ(-3deg)",shadow:"rgba(0, 0, 0, 0.3) -15px 25px 30px"},card:{container:"relative inline-block",wrapper:"relative overflow-hidden rounded-lg",transform:"none",hoverTransform:"scale(1.03)",shadow:"rgba(0, 0, 0, 0.2) 0px 6px 15px"},testimonial:{container:"relative inline-block",wrapper:"relative overflow-hidden rounded-full",transform:"none",hoverTransform:"scale(1.1)",shadow:"rgba(0, 0, 0, 0.15) 0px 4px 12px"},feature:{container:"relative inline-block",wrapper:"relative overflow-hidden rounded-lg",transform:"none",hoverTransform:"scale(1.02)",shadow:"rgba(0, 0, 0, 0.25) 0px 8px 25px"}},l={light:.5,medium:1,strong:1.5};function c(e){let{src:t,alt:r,width:c,height:d,className:u="",priority:f=!1,fill:h=!1,sizes:p,quality:m=75,objectFit:x="cover",variant:g="default",intensity:y="medium",...v}=e,[b,j]=(0,a.useState)(!1),[w,N]=(0,a.useState)(!0),[S,_]=(0,a.useState)(!1),F=o[g];return(l[y],S)?(0,s.jsx)("div",{className:"bg-gray-800 border border-gray-700 flex items-center justify-center ".concat(F.wrapper," ").concat(u),style:{width:h?"100%":c,height:h?"100%":d,transform:F.transform,boxShadow:F.shadow,transformStyle:"preserve-3d",transition:"transform 0.6s ease-out"},children:(0,s.jsxs)("div",{className:"text-center text-gray-400",children:[(0,s.jsx)("svg",{className:"w-8 h-8 mx-auto mb-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z",clipRule:"evenodd"})}),(0,s.jsx)("p",{className:"text-xs",children:"Image not found"})]})}):(0,s.jsxs)("div",{className:F.container,children:[w&&(0,s.jsx)("div",{className:"absolute inset-0 bg-gray-800 animate-pulse ".concat(F.wrapper),style:{width:h?"100%":c,height:h?"100%":d,transform:F.transform,boxShadow:F.shadow,transformStyle:"preserve-3d",zIndex:10},children:(0,s.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,s.jsx)("div",{className:"w-8 h-8 border-2 border-green-400 border-t-transparent rounded-full animate-spin"})})}),(0,s.jsxs)(i.P.div,{className:"".concat(F.wrapper," ").concat(u),style:{transformStyle:"preserve-3d",transform:F.transform,boxShadow:F.shadow,transition:"transform 0.6s ease-out, box-shadow 0.6s ease-out"},animate:{transform:b?F.hoverTransform:F.transform,boxShadow:b?F.shadow.replace(/rgba\(0, 0, 0, ([\d.]+)\)/,(e,t)=>"rgba(0, 0, 0, ".concat(1.3*parseFloat(t),")")):F.shadow},transition:{type:"spring",stiffness:300,damping:30},onMouseEnter:()=>j(!0),onMouseLeave:()=>j(!1),children:[(0,s.jsx)("div",{className:"absolute inset-0 z-10 pointer-events-none",style:{background:"linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.05) 50%, rgba(0, 0, 0, 0.1) 100%)",opacity:b?.8:.4,transition:"opacity 0.3s ease"}}),(0,s.jsx)(n.default,{src:t,alt:r,width:h?void 0:c,height:h?void 0:d,fill:h,priority:f,quality:m,sizes:p,className:"\n            ".concat("cover"===x?"object-cover":"","\n            ").concat("contain"===x?"object-contain":"","\n            ").concat("fill"===x?"object-fill":"","\n            ").concat("none"===x?"object-none":"","\n            ").concat("scale-down"===x?"object-scale-down":"","\n            transition-transform duration-300\n          "),style:{opacity:+!w,transition:"opacity 0.3s ease"},onLoad:()=>{N(!1)},onError:()=>{N(!1),_(!0)},...v}),(0,s.jsx)(i.P.div,{className:"absolute inset-0 pointer-events-none",style:{background:"linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%)",transform:"translateX(-100%)"},animate:{transform:b?"translateX(100%)":"translateX(-100%)"},transition:{duration:.6,ease:"easeInOut"}})]})]})}function d(e){return(0,s.jsx)(c,{...e,variant:"hero"})}function u(e){return(0,s.jsx)(c,{...e,variant:"card"})}function f(e){return(0,s.jsx)(c,{...e,variant:"testimonial"})}function h(e){return(0,s.jsx)(c,{...e,variant:"feature"})}},64198:(e,t,r)=>{"use strict";r.d(t,{CustomToaster:()=>l,P0:()=>o,oR:()=>n.Ay});var s=r(95155),a=r(68289),n=r(13568),i=r(10351);let o={success:e=>{n.Ay.success(e,{duration:4e3,position:"top-right",style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}})},error:e=>{n.Ay.error(e,{duration:5e3,position:"top-right",style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}})},warning:e=>{(0,n.Ay)(e,{duration:4e3,position:"top-right",icon:"⚠️",style:{background:"#92400E",color:"#FFFBEB",border:"1px solid #F59E0B"}})},info:e=>{(0,n.Ay)(e,{duration:4e3,position:"top-right",icon:"ℹ️",style:{background:"#1E3A8A",color:"#EFF6FF",border:"1px solid #3B82F6"}})},loading:e=>n.Ay.loading(e,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"}}),dismiss:e=>{n.Ay.dismiss(e)},promise:(e,t)=>n.Ay.promise(e,t,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"},success:{style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}})};function l(){return(0,s.jsx)(n.l$,{position:"top-right",reverseOrder:!1,gutter:8,containerClassName:"",containerStyle:{},toastOptions:{className:"",duration:4e3,style:{background:"#1F2937",color:"#F9FAFB",border:"1px solid #374151",borderRadius:"8px",fontSize:"14px",fontWeight:"500",padding:"12px 16px",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"},success:{iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}},children:e=>(0,s.jsx)(n.bv,{toast:e,children:t=>{let{icon:r,message:o}=t;return(0,s.jsxs)(a.P.div,{initial:{opacity:0,y:-50,scale:.3},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.3},transition:{type:"spring",stiffness:500,damping:30,duration:.3},className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:r}),(0,s.jsx)("div",{className:"flex-1",children:o}),"loading"!==e.type&&(0,s.jsx)("button",{onClick:()=>n.Ay.dismiss(e.id),className:"flex-shrink-0 p-1 rounded-full hover:bg-gray-600 transition-colors",children:(0,s.jsx)(i.yGN,{className:"w-4 h-4"})})]})}})})}},68375:()=>{},82269:(e,t,r)=>{"use strict";var s=r(49509);r(68375);var a=r(12115),n=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(a),i=void 0!==s&&s.env&&!0,o=function(e){return"[object String]"===Object.prototype.toString.call(e)},l=function(){function e(e){var t=void 0===e?{}:e,r=t.name,s=void 0===r?"stylesheet":r,a=t.optimizeForSpeed,n=void 0===a?i:a;c(o(s),"`name` must be a string"),this._name=s,this._deletedRulePlaceholder="#"+s+"-deleted-rule____{}",c("boolean"==typeof n,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=n,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var l="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=l?l.getAttribute("content"):null}var t,r=e.prototype;return r.setOptimizeForSpeed=function(e){c("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),c(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},r.isOptimizeForSpeed=function(){return this._optimizeForSpeed},r.inject=function(){var e=this;if(c(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(i||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},r.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},r.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},r.insertRule=function(e,t){if(c(o(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var r=this.getSheet();"number"!=typeof t&&(t=r.cssRules.length);try{r.insertRule(e,t)}catch(t){return i||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var s=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,s))}return this._rulesCount++},r.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var r="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(s){i||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}}else{var s=this._tags[e];c(s,"old rule at index `"+e+"` not found"),s.textContent=t}return e},r.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];c(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},r.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},r.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,r){return r?t=t.concat(Array.prototype.map.call(e.getSheetForTag(r).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},r.makeStyleTag=function(e,t,r){t&&c(o(t),"makeStyleTag accepts only strings as second parameter");var s=document.createElement("style");this._nonce&&s.setAttribute("nonce",this._nonce),s.type="text/css",s.setAttribute("data-"+e,""),t&&s.appendChild(document.createTextNode(t));var a=document.head||document.getElementsByTagName("head")[0];return r?a.insertBefore(s,r):a.appendChild(s),s},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}(e.prototype,t),e}();function c(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var d=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},u={};function f(e,t){if(!t)return"jsx-"+e;var r=String(t),s=e+r;return u[s]||(u[s]="jsx-"+d(e+"-"+r)),u[s]}function h(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var r=e+t;return u[r]||(u[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),u[r]}var p=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,s=void 0===r?null:r,a=t.optimizeForSpeed,n=void 0!==a&&a;this._sheet=s||new l({name:"styled-jsx",optimizeForSpeed:n}),this._sheet.inject(),s&&"boolean"==typeof n&&(this._sheet.setOptimizeForSpeed(n),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var r=this.getIdAndRules(e),s=r.styleId,a=r.rules;if(s in this._instancesCounts){this._instancesCounts[s]+=1;return}var n=a.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[s]=n,this._instancesCounts[s]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var s=this._fromServer&&this._fromServer[r];s?(s.parentNode.removeChild(s),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],s=e[1];return n.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:s}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,s=e.id;if(r){var a=f(s,r);return{styleId:a,rules:Array.isArray(t)?t.map(function(e){return h(a,e)}):[h(a,t)]}}return{styleId:f(s),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=a.createContext(null);m.displayName="StyleSheetContext";var x=n.default.useInsertionEffect||n.default.useLayoutEffect,g="undefined"!=typeof window?new p:void 0;function y(e){var t=g||a.useContext(m);return t&&("undefined"==typeof window?t.add(e):x(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}y.dynamic=function(e){return e.map(function(e){return f(e[0],e[1])}).join(" ")},t.style=y}},e=>{e.O(0,[844,5236,6874,6766,3568,8441,5964,7358],()=>e(e.s=18980)),_N_E=e.O()}]);