"use client";

import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';
import {
    FiArrowDownRight,
    FiArrowUpRight,
    FiDollarSign,
    FiEye,
    FiEyeOff,
    FiPlus,
    FiTarget,
    FiTrendingUp
} from 'react-icons/fi';
import { useAuth } from '../../hooks/use-auth';
import { isMobileApp } from '../../utils/mobileDetection';
import MobileCard from './MobileCard';

export default function MobileDashboard() {
  const [showBalance, setShowBalance] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const { user } = useAuth();

  useEffect(() => {
    setIsMobile(isMobileApp());
  }, []);

  if (!isMobile) {
    return null; // Don't render for web users
  }

  const stats = [
    {
      title: "Total Savings",
      value: "₦125,450.00",
      change: "+12.5%",
      changeType: "positive" as 'positive' | 'negative' | 'neutral',
      icon: FiTrendingUp
    },
    {
      title: "Monthly Goal",
      value: "₦50,000.00",
      change: "75% complete",
      changeType: "neutral" as 'positive' | 'negative' | 'neutral',
      icon: FiTarget
    },
    {
      title: "Interest Earned",
      value: "₦8,750.00",
      change: "****%",
      changeType: "positive" as 'positive' | 'negative' | 'neutral',
      icon: FiDollarSign
    }
  ];

  const quickActions = [
    { title: "Add Money", icon: FiPlus, color: "bg-green-500", href: "/dashboard/deposits" },
    { title: "Withdraw", icon: FiArrowUpRight, color: "bg-blue-500", href: "/dashboard/withdrawals" },
    { title: "Transfer", icon: FiArrowDownRight, color: "bg-purple-500", href: "/dashboard/transfers" },
    { title: "Savings Plan", icon: FiTarget, color: "bg-orange-500", href: "/dashboard/savings-plans" }
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="text-center py-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Welcome back, {user?.firstName || 'User'}!
        </h1>
        <p className="text-gray-600">
          Your financial journey continues
        </p>
      </div>

      {/* Balance Card */}
      <MobileCard className="bg-gradient-to-r from-green-600 to-green-700 text-white border-0">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Total Balance</h2>
          <button
            onClick={() => setShowBalance(!showBalance)}
            className="p-2 hover:bg-white/10 rounded-full transition-colors"
          >
            {showBalance ? <FiEye size={20} /> : <FiEyeOff size={20} />}
          </button>
        </div>
        <div className="text-3xl font-bold mb-2">
          {showBalance ? "₦125,450.00" : "₦••••••••"}
        </div>
        <p className="text-green-100">
          +₦12,500 this month
        </p>
      </MobileCard>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-4">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <MobileCard hoverable>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-gray-100 rounded-lg">
                    <stat.icon className="w-5 h-5 text-gray-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">{stat.title}</p>
                    <p className="text-lg font-semibold text-gray-900">{stat.value}</p>
                  </div>
                </div>
                <div className={`text-sm font-medium ${
                  stat.changeType === 'positive' ? 'text-green-600' : 
                  stat.changeType === 'negative' ? 'text-red-600' : 'text-gray-600'
                }`}>
                  {stat.change}
                </div>
              </div>
            </MobileCard>
          </motion.div>
        ))}
      </div>

      {/* Quick Actions */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-2 gap-4">
          {quickActions.map((action, index) => (
            <motion.div
              key={action.title}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.1 }}
            >
              <MobileCard 
                onClick={() => window.location.href = action.href}
                hoverable
                className="text-center"
              >
                <div className={`w-12 h-12 ${action.color} rounded-full flex items-center justify-center mx-auto mb-3`}>
                  <action.icon className="w-6 h-6 text-white" />
                </div>
                <p className="text-sm font-medium text-gray-900">{action.title}</p>
              </MobileCard>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Recent Transactions */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
          <button className="text-green-600 text-sm font-medium">View All</button>
        </div>
        <div className="space-y-3">
          {[1, 2, 3].map((_, index) => (
            <MobileCard key={index} padding="sm">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <FiArrowUpRight className="w-5 h-5 text-green-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Savings Deposit</p>
                    <p className="text-sm text-gray-600">Today, 2:30 PM</p>
                  </div>
                </div>
                <p className="font-semibold text-green-600">+₦5,000</p>
              </div>
            </MobileCard>
          ))}
        </div>
      </div>

      {/* Bottom Spacing for Mobile Navigation */}
      <div className="h-20" />
    </div>
  );
}
