(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4029],{20354:(e,s,t)=>{Promise.resolve().then(t.bind(t,94276))},94276:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var i=t(95155),r=t(68289),l=t(6874),n=t.n(l),a=t(10351);t(12115);let c=e=>{let{size:s=32,className:t="",showText:r=!0,textClassName:l=""}=e;return(0,i.jsxs)("div",{className:"flex items-center space-x-2 ".concat(t),children:[(0,i.jsx)("div",{className:"relative flex items-center justify-center",style:{width:s,height:s},children:(0,i.jsxs)("svg",{width:s,height:s,viewBox:"0 0 100 100",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"drop-shadow-lg",children:[(0,i.jsx)("path",{d:"M30 20 C30 15, 35 10, 40 10 L60 10 C65 10, 70 15, 70 20 L70 25 L30 25 Z",fill:"currentColor",className:"text-green-400",stroke:"currentColor",strokeWidth:"3",strokeLinejoin:"round"}),(0,i.jsx)("rect",{x:"15",y:"25",width:"70",height:"50",rx:"8",ry:"8",fill:"currentColor",className:"text-green-400",stroke:"currentColor",strokeWidth:"3",strokeLinejoin:"round"}),(0,i.jsx)("rect",{x:"45",y:"20",width:"10",height:"8",rx:"2",fill:"currentColor",className:"text-green-400"}),(0,i.jsx)("rect",{x:"22",y:"32",width:"56",height:"36",rx:"4",fill:"rgba(255, 255, 255, 0.9)",stroke:"currentColor",className:"text-green-500",strokeWidth:"2"}),(0,i.jsxs)("g",{className:"text-green-600",fill:"currentColor",children:[(0,i.jsx)("rect",{x:"35",y:"40",width:"3",height:"20"}),(0,i.jsx)("rect",{x:"62",y:"40",width:"3",height:"20"}),(0,i.jsx)("path",{d:"M35 58 L65 42",stroke:"currentColor",strokeWidth:"3",strokeLinecap:"round"}),(0,i.jsx)("rect",{x:"30",y:"45",width:"40",height:"2"}),(0,i.jsx)("rect",{x:"30",y:"53",width:"40",height:"2"})]}),(0,i.jsx)("circle",{cx:"25",cy:"50",r:"2",fill:"currentColor",className:"text-green-500"}),(0,i.jsx)("circle",{cx:"75",cy:"50",r:"2",fill:"currentColor",className:"text-green-500"}),(0,i.jsx)("rect",{x:"47",y:"12",width:"1",height:"6",fill:"rgba(255, 255, 255, 0.6)"}),(0,i.jsx)("rect",{x:"49",y:"12",width:"1",height:"6",fill:"rgba(255, 255, 255, 0.6)"}),(0,i.jsx)("rect",{x:"51",y:"12",width:"1",height:"6",fill:"rgba(255, 255, 255, 0.6)"})]})}),r&&(0,i.jsx)("span",{className:"font-bold text-gradient-animate ".concat(l),children:"Better Interest"})]})};function d(){return(0,i.jsx)("div",{className:"min-h-screen bg-black text-white p-6",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)(c,{size:40,showText:!0}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-2xl font-bold",children:"Savings Platform Test"}),(0,i.jsx)("p",{className:"text-gray-400",children:"Test the savings functionality"})]})]}),(0,i.jsxs)(n(),{href:"/",className:"flex items-center space-x-2 text-gray-400 hover:text-white transition-colors",children:[(0,i.jsx)(a.kRp,{}),(0,i.jsx)("span",{children:"Back to Home"})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,i.jsxs)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6",children:[(0,i.jsx)(a.x_j,{className:"text-green-400 text-3xl mb-4"}),(0,i.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"User Dashboard"}),(0,i.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:"Test the user dashboard with savings overview and quick actions"}),(0,i.jsx)(n(),{href:"/dashboard",className:"inline-block bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg text-sm transition-colors",children:"View Dashboard"})]}),(0,i.jsxs)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6",children:[(0,i.jsx)(a.z8N,{className:"text-blue-400 text-3xl mb-4"}),(0,i.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Savings Plans"}),(0,i.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:"Create and manage individual savings plans with goals and targets"}),(0,i.jsx)(n(),{href:"/dashboard/savings-plans",className:"inline-block bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-sm transition-colors",children:"Manage Plans"})]}),(0,i.jsxs)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6",children:[(0,i.jsx)(a.cfS,{className:"text-purple-400 text-3xl mb-4"}),(0,i.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Authentication"}),(0,i.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:"Test user signup, login, and admin authentication"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(n(),{href:"/signup",className:"block bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-lg text-sm transition-colors text-center",children:"User Signup"}),(0,i.jsx)(n(),{href:"/auth/login",className:"block bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded-lg text-sm transition-colors text-center",children:"User Login"}),(0,i.jsx)(n(),{href:"/admin/login",className:"block bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg text-sm transition-colors text-center",children:"Admin Login"})]})]})]}),(0,i.jsxs)("div",{className:"mt-12 bg-gray-900/50 border border-gray-800 rounded-lg p-6",children:[(0,i.jsx)("h2",{className:"text-xl font-bold mb-4",children:"Implementation Status"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-green-400 font-semibold mb-2",children:"✅ Completed Features"}),(0,i.jsxs)("ul",{className:"space-y-1 text-sm text-gray-300",children:[(0,i.jsx)("li",{children:"• JWT-based Authentication System"}),(0,i.jsx)("li",{children:"• User Signup with Validation"}),(0,i.jsx)("li",{children:"• Admin Login Interface"}),(0,i.jsx)("li",{children:"• Private Route Protection"}),(0,i.jsx)("li",{children:"• User Dashboard with Stats"}),(0,i.jsx)("li",{children:"• Savings Plans Management"}),(0,i.jsx)("li",{children:"• Savings Plan Creation Form"}),(0,i.jsx)("li",{children:"• Savings Goal Modal"}),(0,i.jsx)("li",{children:"• Target Savings Calculator"}),(0,i.jsx)("li",{children:"• User Navigation & Layout"}),(0,i.jsx)("li",{children:"• Interest Calculation Service"}),(0,i.jsx)("li",{children:"• Responsive Design"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-yellow-400 font-semibold mb-2",children:"\uD83D\uDEA7 Next Features"}),(0,i.jsxs)("ul",{className:"space-y-1 text-sm text-gray-300",children:[(0,i.jsx)("li",{children:"• Group Savings System"}),(0,i.jsx)("li",{children:"• Rotational Savings"}),(0,i.jsx)("li",{children:"• Payment Integration"}),(0,i.jsx)("li",{children:"• KYC Verification"}),(0,i.jsx)("li",{children:"• Analytics Dashboard"}),(0,i.jsx)("li",{children:"• Notification System"}),(0,i.jsx)("li",{children:"• Admin Management"}),(0,i.jsx)("li",{children:"• Settings & Profile"}),(0,i.jsx)("li",{children:"• Transaction History"}),(0,i.jsx)("li",{children:"• Real-time Updates"})]})]})]})]}),(0,i.jsxs)("div",{className:"mt-8 bg-blue-500/10 border border-blue-500/20 rounded-lg p-6",children:[(0,i.jsx)("h3",{className:"text-blue-400 font-semibold mb-2",children:"Technical Implementation"}),(0,i.jsxs)("div",{className:"text-sm text-gray-300 space-y-2",children:[(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"Frontend:"})," Next.js 14 with TypeScript, Framer Motion animations, Tailwind CSS styling"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"Authentication:"})," JWT tokens with refresh mechanism, role-based access control"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"State Management:"})," React Context API for global auth state"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"Forms:"})," Comprehensive validation with real-time error handling"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"API Integration:"})," Service layer architecture with error handling"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"UI/UX:"})," Professional design with Koja Save branding and responsive layout"]})]})]})]})})}}},e=>{e.O(0,[844,5236,6874,8441,5964,7358],()=>e(e.s=20354)),_N_E=e.O()}]);