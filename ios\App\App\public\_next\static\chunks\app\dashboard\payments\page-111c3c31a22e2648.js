(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[889],{58111:(e,t,a)=>{"use strict";a.d(t,{Lz:()=>d,gz:()=>u,hI:()=>c});var r=a(95155),s=a(12115),o=a(68289),n=a(57740);let i={green:{bg:"bg-green-500/20",text:"text-green-400",border:"border-green-500/30",icon:"text-green-400"},blue:{bg:"bg-blue-500/20",text:"text-blue-400",border:"border-blue-500/30",icon:"text-blue-400"},red:{bg:"bg-red-500/20",text:"text-red-400",border:"border-red-500/30",icon:"text-red-400"},yellow:{bg:"bg-yellow-500/20",text:"text-yellow-400",border:"border-yellow-500/30",icon:"text-yellow-400"},purple:{bg:"bg-purple-500/20",text:"text-purple-400",border:"border-purple-500/30",icon:"text-purple-400"}};function l(e){var t,a,l,c;let{title:d,value:u,subtitle:x,icon:p,trend:g,color:m="green",onClick:b,className:y=""}=e,h=i[m]||i.green,{theme:f}=(0,n.DP)(),v=(0,n.Yx)(f),N=(0,s.useRef)(null),j=e=>{let t={1:"light"===f?"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)":"0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.5)",2:"light"===f?"0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)":"0 3px 6px rgba(0, 0, 0, 0.4), 0 3px 6px rgba(0, 0, 0, 0.6)",3:"light"===f?"0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)":"0 10px 20px rgba(0, 0, 0, 0.5), 0 6px 6px rgba(0, 0, 0, 0.7)"};return t[e]||t[1]};return(0,r.jsxs)(o.P.div,{ref:N,initial:{opacity:0,y:20},animate:{opacity:1,y:0},whileHover:{y:-4,boxShadow:j(3),transition:{duration:.2,ease:"easeOut"}},className:"\n        relative overflow-hidden rounded-xl p-6 transition-all duration-300 group\n        ".concat(b?"cursor-pointer":"","\n        ").concat("light"===f?"bg-white border border-gray-200 hover:border-gray-300":"bg-gray-900/80 border border-gray-700 hover:border-gray-600","\n        ").concat(y,"\n      "),style:{boxShadow:j(1)},onClick:b,children:[b&&(0,r.jsx)(o.P.div,{className:"absolute inset-0 bg-green-400 opacity-0 group-hover:opacity-5 transition-opacity duration-300",initial:{scale:0,opacity:0},whileHover:{scale:1,opacity:.05},transition:{duration:.3}}),(0,r.jsx)("div",{className:"absolute top-0 left-0 w-full h-1 bg-gradient-to-r ".concat((null==h||null==(t=h.bg)?void 0:t.replace("bg-","from-"))||"from-brand"," to-transparent opacity-60")}),(0,r.jsxs)("div",{className:"flex items-start justify-between relative z-10",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium mb-3 ".concat(v.text.secondary),children:d}),(0,r.jsx)("p",{className:"text-3xl font-bold mb-2 ".concat(v.text.primary),style:{fontFamily:"Inter, system-ui, sans-serif"},children:u}),x&&(0,r.jsx)("p",{className:"text-sm ".concat(v.text.tertiary," mb-2"),children:x}),g&&(0,r.jsxs)(o.P.div,{className:"flex items-center mt-3",initial:{opacity:0,x:-10},animate:{opacity:1,x:0},transition:{delay:.2},children:[(0,r.jsxs)("span",{className:"text-sm font-semibold px-2 py-1 rounded-full ".concat(g.isPositive?"text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/30":"text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/30"),children:[g.isPositive?"↗":"↘"," ",g.isPositive?"+":"",g.value,"%"]}),(0,r.jsx)("span",{className:"text-xs ml-2 ".concat(v.text.tertiary),children:"vs last month"})]})]}),p&&(0,r.jsx)(o.P.div,{className:"p-4 rounded-xl ".concat((null==h?void 0:h.bg)||"bg-brand/20"," ").concat((null==h?void 0:h.border)||"border-brand"," border-2 shadow-lg"),whileHover:{scale:1.05,rotate:5},transition:{duration:.2},style:{boxShadow:"0 4px 12px ".concat((null==h||null==(a=h.bg)?void 0:a.includes("green"))?"rgba(34, 197, 94, 0.3)":(null==h||null==(l=h.bg)?void 0:l.includes("blue"))?"rgba(59, 130, 246, 0.3)":(null==h||null==(c=h.bg)?void 0:c.includes("purple"))?"rgba(147, 51, 234, 0.3)":"rgba(34, 197, 94, 0.3)")},children:(0,r.jsx)(p,{className:"w-7 h-7 ".concat((null==h?void 0:h.icon)||"text-brand")})})]})]})}function c(e){let{title:t,value:a,subtitle:s,icon:o,color:n="green",trend:i}=e;return(0,r.jsx)(l,{title:t,value:a,subtitle:s,icon:o,color:n,trend:i})}function d(e){var t,a,s,l;let{title:c,subtitle:d,icon:u,color:x="blue",onClick:p}=e,g=i[x]||i.blue,{theme:m}=(0,n.DP)(),b=(0,n.Yx)(m);return(0,r.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},whileHover:{y:-6,scale:1.02,boxShadow:"light"===m?"0 8px 25px rgba(0, 0, 0, 0.15), 0 16px 40px rgba(0, 0, 0, 0.1)":"0 8px 25px rgba(0, 0, 0, 0.4), 0 16px 40px rgba(0, 0, 0, 0.3)",transition:{duration:.2,ease:"easeOut"}},whileTap:{scale:.98},className:"\n        relative overflow-hidden rounded-xl p-6 cursor-pointer transition-all duration-300 group\n        ".concat("light"===m?"bg-gradient-to-br from-white to-gray-50 border border-gray-200 hover:border-gray-300":"bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-700 hover:border-gray-600","\n      "),style:{boxShadow:"light"===m?"0 2px 8px rgba(0, 0, 0, 0.1), 0 4px 16px rgba(0, 0, 0, 0.05)":"0 2px 8px rgba(0, 0, 0, 0.3), 0 4px 16px rgba(0, 0, 0, 0.2)"},onClick:p,children:[(0,r.jsx)(o.P.div,{className:"absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300 ".concat((null==g?void 0:g.bg)||"bg-brand/10"),initial:{scale:0},whileHover:{scale:1},transition:{duration:.3}}),(0,r.jsx)("div",{className:"absolute top-0 left-0 w-full h-1 bg-gradient-to-r ".concat((null==g||null==(t=g.bg)?void 0:t.replace("bg-","from-"))||"from-brand"," to-transparent")}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 relative z-10",children:[(0,r.jsx)(o.P.div,{className:"p-4 rounded-xl ".concat((null==g?void 0:g.bg)||"bg-brand/20"," shadow-lg"),whileHover:{scale:1.1,rotate:10},transition:{duration:.2},style:{boxShadow:"0 4px 12px ".concat((null==g||null==(a=g.bg)?void 0:a.includes("green"))?"rgba(34, 197, 94, 0.3)":(null==g||null==(s=g.bg)?void 0:s.includes("blue"))?"rgba(59, 130, 246, 0.3)":(null==g||null==(l=g.bg)?void 0:l.includes("purple"))?"rgba(147, 51, 234, 0.3)":"rgba(34, 197, 94, 0.3)")},children:(0,r.jsx)(u,{className:"w-6 h-6 ".concat((null==g?void 0:g.icon)||"text-brand")})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"font-semibold text-lg ".concat(b.text.primary," mb-1"),children:c}),(0,r.jsx)("p",{className:"text-sm ".concat(b.text.secondary),children:d})]}),(0,r.jsx)(o.P.div,{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat((null==g?void 0:g.bg)||"bg-brand/20"," opacity-70"),whileHover:{scale:1.2,opacity:1},transition:{duration:.2},children:(0,r.jsx)("svg",{className:"w-4 h-4 ".concat((null==g?void 0:g.icon)||"text-brand"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]})]})}function u(e){let{title:t,current:a,target:s,unit:n="",color:l="green"}=e,c=Math.min(a/s*100,100),d=i[l]||i.green;return(0,r.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-white font-semibold mb-4",children:t}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Progress"}),(0,r.jsxs)("span",{className:(null==d?void 0:d.text)||"text-brand",children:[c.toFixed(1),"%"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-800 rounded-full h-2",children:(0,r.jsx)(o.P.div,{initial:{width:0},animate:{width:"".concat(c,"%")},transition:{duration:1,ease:"easeOut"},className:"h-2 rounded-full bg-gradient-to-r from-".concat(l,"-400 to-").concat(l,"-600")})}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsxs)("span",{className:"text-gray-400",children:[a.toLocaleString(),n," / ",s.toLocaleString(),n]}),(0,r.jsxs)("span",{className:"text-gray-400",children:[(s-a).toLocaleString(),n," remaining"]})]})]})]})}},63739:(e,t,a)=>{Promise.resolve().then(a.bind(a,99988))},64198:(e,t,a)=>{"use strict";a.d(t,{CustomToaster:()=>l,P0:()=>i,oR:()=>o.Ay});var r=a(95155),s=a(68289),o=a(13568),n=a(10351);let i={success:e=>{o.Ay.success(e,{duration:4e3,position:"top-right",style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}})},error:e=>{o.Ay.error(e,{duration:5e3,position:"top-right",style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}})},warning:e=>{(0,o.Ay)(e,{duration:4e3,position:"top-right",icon:"⚠️",style:{background:"#92400E",color:"#FFFBEB",border:"1px solid #F59E0B"}})},info:e=>{(0,o.Ay)(e,{duration:4e3,position:"top-right",icon:"ℹ️",style:{background:"#1E3A8A",color:"#EFF6FF",border:"1px solid #3B82F6"}})},loading:e=>o.Ay.loading(e,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"}}),dismiss:e=>{o.Ay.dismiss(e)},promise:(e,t)=>o.Ay.promise(e,t,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"},success:{style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}})};function l(){return(0,r.jsx)(o.l$,{position:"top-right",reverseOrder:!1,gutter:8,containerClassName:"",containerStyle:{},toastOptions:{className:"",duration:4e3,style:{background:"#1F2937",color:"#F9FAFB",border:"1px solid #374151",borderRadius:"8px",fontSize:"14px",fontWeight:"500",padding:"12px 16px",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"},success:{iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}},children:e=>(0,r.jsx)(o.bv,{toast:e,children:t=>{let{icon:a,message:i}=t;return(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:-50,scale:.3},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.3},transition:{type:"spring",stiffness:500,damping:30,duration:.3},className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:a}),(0,r.jsx)("div",{className:"flex-1",children:i}),"loading"!==e.type&&(0,r.jsx)("button",{onClick:()=>o.Ay.dismiss(e.id),className:"flex-shrink-0 p-1 rounded-full hover:bg-gray-600 transition-colors",children:(0,r.jsx)(n.yGN,{className:"w-4 h-4"})})]})}})})}},75399:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>d,Wh:()=>l,XI:()=>i,rA:()=>c});var r=a(95155),s=a(68289),o=a(12115),n=a(10351);function i(e){let{data:t,columns:a,loading:i=!1,searchable:l=!1,searchPlaceholder:c="Search...",onSearch:d,emptyMessage:u="No data available",className:x=""}=e,[p,g]=o.useState({key:null,direction:"asc"}),[m,b]=o.useState(""),y=o.useMemo(()=>p.key?[...t].sort((e,t)=>{let a=e[p.key],r=t[p.key];return a<r?"asc"===p.direction?-1:1:a>r?"asc"===p.direction?1:-1:0}):t,[t,p]);return i?(0,r.jsxs)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg ".concat(x),children:[l&&(0,r.jsx)("div",{className:"p-4 border-b border-gray-800",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(n.CKj,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,r.jsx)("input",{type:"text",placeholder:c,className:"w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-green-500",disabled:!0})]})}),(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto"}),(0,r.jsx)("p",{className:"text-gray-400 mt-2",children:"Loading..."})]})]}):(0,r.jsxs)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg overflow-hidden ".concat(x),children:[l&&(0,r.jsx)("div",{className:"p-4 border-b border-gray-800",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(n.CKj,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,r.jsx)("input",{type:"text",placeholder:c,value:m,onChange:e=>{var t;b(t=e.target.value),null==d||d(t)},className:"w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-green-500"})]})}),0===y.length?(0,r.jsx)("div",{className:"p-8 text-center",children:(0,r.jsx)("p",{className:"text-gray-400",children:u})}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{className:"bg-gray-800/50",children:(0,r.jsx)("tr",{children:a.map(e=>(0,r.jsx)("th",{className:"\n                      px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\n                      ".concat(e.sortable?"cursor-pointer hover:text-white":"","\n                      ").concat(e.width?e.width:"","\n                    "),onClick:()=>{var t;let a;return e.sortable&&(t=e.key,a="asc",void(p.key===t&&"asc"===p.direction&&(a="desc"),g({key:t,direction:a})))},children:(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("span",{children:e.title}),e.sortable&&(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)(n.wAb,{className:"w-3 h-3 ".concat(p.key===e.key&&"asc"===p.direction?"text-green-400":"text-gray-500")}),(0,r.jsx)(n.fK4,{className:"w-3 h-3 -mt-1 ".concat(p.key===e.key&&"desc"===p.direction?"text-green-400":"text-gray-500")})]})]})},String(e.key)))})}),(0,r.jsx)("tbody",{className:"divide-y divide-gray-800",children:y.map((e,t)=>(0,r.jsx)(s.P.tr,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.05*t},className:"hover:bg-gray-800/30 transition-colors",children:a.map(t=>(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-300",children:t.render?t.render(e[t.key],e):String(e[t.key]||"-")},String(t.key)))},t))})]})})]})}function l(e){let{status:t,variant:a="default"}=e;return(0,r.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-medium rounded-full ".concat({default:"bg-gray-500/20 text-gray-400",success:"bg-green-500/20 text-green-400",warning:"bg-yellow-500/20 text-yellow-400",danger:"bg-red-500/20 text-red-400",info:"bg-blue-500/20 text-blue-400"}[a]),children:t})}function c(e){let{onClick:t,children:a,variant:s="default",size:o="sm"}=e;return(0,r.jsx)("button",{onClick:t,className:"rounded transition-colors ".concat({default:"text-gray-400 hover:text-white",primary:"text-green-400 hover:text-green-300",danger:"text-red-400 hover:text-red-300"}[s]," ").concat({sm:"p-1",md:"p-2"}[o]),children:a})}let d=i},99988:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>h});var r=a(95155),s=a(14915),o=a.n(s);a(49537);var n=a(68289),i=a(35695),l=a(12115),c=a(10351),d=a(58111),u=a(11846),x=a(14585),p=a(66440),g=a(75399),m=a(64198),b=a(98030),y=a(24630);function h(){console.log("[PaymentsPage] Rendered");let{user:e,isAuthenticated:t,isLoading:a}=(0,b.A)();console.log("[PaymentsPage] useAuth:",{user:e,isAuthenticated:t,isLoading:a});let s=(0,i.useRouter)(),[h,f]=(0,l.useState)([]),[v,N]=(0,l.useState)(null),[j,w]=(0,l.useState)(0),P=async()=>{if(!(null==e?void 0:e.id))return void console.log("[PaymentsPage] No user _id, cannot fetch transactions");try{console.log("[PaymentsPage] Fetching transactions for user:",e.id);let t=await y.lD.getUserTransactions(e.id);console.log("[PaymentsPage] Transactions API response:",t),f(t.transactions||[])}catch(e){console.error("[PaymentsPage] Failed to fetch transactions:",e),m.P0.error("Failed to fetch transactions")}},k=async()=>{if(null==e?void 0:e.id)try{var t;let a=await y.Dv.getUserBalance(e.id);N(null!=(t=a.availableBalance)?t:0)}catch(e){m.P0.error("Failed to fetch balance")}},F=async()=>{if(null==e?void 0:e.id)try{let t=await y.Pd.getUserTotalDeposits(e.id);w(t)}catch(e){m.P0.error("Failed to fetch total deposits")}},[D,A]=(0,l.useState)(!1),[C,E]=(0,l.useState)(!1),[S,T]=(0,l.useState)(!1),[B,_]=(0,l.useState)({amount:"",method:"BANK_TRANSFER",bankCode:"",accountNumber:"",accountName:""}),[L,R]=(0,l.useState)([]);(0,l.useEffect)(()=>{!async function(){try{let e=await y.KT.getBankList();e&&Array.isArray(e.data)?R(e.data.map(e=>({name:e.name,code:e.code}))):R([])}catch(e){console.error("[Withdraw] Failed to fetch banks from Paystack:",e),R([])}}()},[]);let O=async()=>{if(10===B.accountNumber.length&&B.bankCode)try{let e=await y.KT.verifyAccountNumber(B.accountNumber,B.bankCode);e&&e.status&&e.data&&e.data.account_name?(_(t=>({...t,accountName:e.data.account_name})),m.P0.success("Account verified!")):(_(e=>({...e,accountName:""})),m.P0.error("Could not verify account. Please check details."))}catch(e){_(e=>({...e,accountName:""})),m.P0.error("Error verifying account. Please try again.")}else m.P0.error("Enter a valid 10-digit account number and select a bank.")};(0,l.useEffect)(()=>{console.log("[PaymentsPage] useEffect triggered. user:",e),o().init({duration:1e3,once:!1,easing:"ease-out-cubic",offset:50}),o().refresh(),(null==e?void 0:e.id)?(console.log("[PaymentsPage] user.id present, fetching transactions, balance, and total deposits:",e.id),P(),k(),F()):console.log("[PaymentsPage] No user.id in useEffect")},[e]),(0,l.useEffect)(()=>a||t?a||(null==e?void 0:e.role)!=="ADMIN"?void 0:void s.push("/admin/dashboard"):void s.push("/auth/login"),[t,a,e,s]);let W=(0,l.useRef)(!1);(0,l.useEffect)(()=>{if(!W.current){let e=document.createElement("script");e.src="https://js.paystack.co/v1/inline.js",e.async=!0,document.body.appendChild(e),W.current=!0}},[]);let I=async t=>{t.preventDefault(),T(!0);try{var a;let t=parseInt(B.amount);if(console.log("[Deposit] User:",e),console.log("[Deposit] Amount:",t),isNaN(t)||t<1){m.P0.error("Please enter a valid amount (minimum ₦1)"),T(!1);return}let r=(null==e?void 0:e.email)||"",s="pk_test_44f6dbb6159b73b84fff2fc441dcdd997e15c10d";if(console.log("[Deposit] Paystack Key:",s),!s||!s.startsWith("pk_")){m.P0.error("Paystack public key is not set or invalid."),T(!1);return}if(!r||!r.includes("@")){m.P0.error("A valid user email is required for payment."),T(!1);return}console.log("[Deposit] Calling depositsService.initiateDeposit",{amount:t,paymentMethod:"CARD"});let o=await y.Pd.initiateDeposit({amount:t,paymentMethod:"CARD"});console.log("[Deposit] initiateDeposit response:",o);let n=null==(a=o.paymentData)?void 0:a.reference;if(console.log("[Deposit] Paystack reference:",n),!n){m.P0.error("Failed to get Paystack reference from backend."),T(!1);return}if(!window.PaystackPop){m.P0.error("Paystack script not loaded"),T(!1);return}window.PaystackPop.setup({key:s,email:r,amount:100*t,currency:"NGN",ref:n,callback:function(e){(async()=>{try{m.P0.success("Verifying deposit with backend...");let e=await fetch("".concat("http://localhost:8080","/api/paystack/deposit/verify/").concat(n)),t=await e.json();e.ok&&t.status&&t.data&&"success"===t.data.status?(m.P0.success("Deposit successful! Your balance has been updated."),A(!1),_({amount:"",method:"BANK_TRANSFER",bankCode:"",accountNumber:"",accountName:""}),"number"==typeof t.totalDeposits&&w(t.totalDeposits),setTimeout(async()=>{console.log("[Deposit] Refetching after Paystack verify...");let e=await k(),t=await P(),a=await F();console.log("[Deposit] Refetched balance:",e),console.log("[Deposit] Refetched transactions:",t),console.log("[Deposit] Refetched total deposits:",a)},2e3)):m.P0.error("Deposit verification failed. Please contact support.")}catch(e){m.P0.error("Error verifying deposit. Please contact support."),console.error("[Deposit] Error verifying deposit:",e)}T(!1)})()},onClose:function(){m.P0.error("Deposit cancelled"),T(!1)}}).openIframe()}catch(e){console.error("[Deposit] Failed to process deposit:",e),m.P0.error("Failed to process deposit"),T(!1)}},M=async t=>{t.preventDefault(),T(!0);try{let t=parseInt(B.amount);if(isNaN(t)||t<1){m.P0.error("Please enter a valid amount (minimum ₦1)"),T(!1);return}let a={amount:t,bank_code:B.bankCode,account_number:B.accountNumber,account_name:B.accountName,user_id:null==e?void 0:e.id,type:"EXTERNAL_PAYOUT"};if(!a.bank_code||!a.account_number||!a.account_name||!a.user_id){m.P0.error("Please fill all withdrawal details and verify account."),T(!1);return}console.log("[Withdraw] Calling withdrawalsService.initiateWithdrawal",a);let r=await y.uX.initiateWithdrawal(a);console.log("[Withdraw] initiateWithdrawal response:",r),m.P0.success("Withdrawal initiated successfully!"),E(!1),_({amount:"",method:"BANK_TRANSFER",bankCode:"",accountNumber:"",accountName:""}),setTimeout(async()=>{await k(),await P(),await F()},2e3),T(!1)}catch(e){console.error("[Withdraw] Failed to process withdrawal:",e),m.P0.error((null==e?void 0:e.message)||"Failed to process withdrawal"),T(!1)}},K=e=>{let t=e.toLowerCase();return t.includes("deposit")?{color:"text-green-400",icon:(0,r.jsx)(c.OgA,{className:"w-4 h-4 text-green-400"}),sign:"+"}:t.includes("withdrawal")?{color:"text-red-400",icon:(0,r.jsx)(c.qdV,{className:"w-4 h-4 text-red-400"}),sign:"-"}:t.includes("interest")?{color:"text-purple-400",icon:(0,r.jsx)(c.z8N,{className:"w-4 h-4 text-purple-400"}),sign:"+"}:t.includes("penalty")?{color:"text-yellow-400",icon:(0,r.jsx)(c.jTZ,{className:"w-4 h-4 text-yellow-400"}),sign:"-"}:t.includes("fee")?{color:"text-yellow-400",icon:(0,r.jsx)(c.lZI,{className:"w-4 h-4 text-yellow-400"}),sign:"-"}:t.includes("bonus")?{color:"text-green-400",icon:(0,r.jsx)(c.GGD,{className:"w-4 h-4 text-green-400"}),sign:"+"}:t.includes("refund")?{color:"text-blue-400",icon:(0,r.jsx)(c.PCV,{className:"w-4 h-4 text-blue-400"}),sign:"+"}:{color:"text-gray-400",icon:(0,r.jsx)(c.PCV,{className:"w-4 h-4 text-gray-400"}),sign:""}},V=[{key:"type",title:"Type",render:e=>{let t=K(e);return(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[t.icon,(0,r.jsx)("span",{className:"capitalize",children:e.replace(/_/g," ").toLowerCase()})]})}},{key:"amount",title:"Amount",render:(e,t)=>{let a=K(t.type);return(0,r.jsxs)("div",{className:"font-medium ".concat(a.color),children:[a.sign,"₦",e.toLocaleString()]})}},{key:"status",title:"Status",render:e=>{let t=e&&e.trim()?e.toUpperCase():"COMPLETED",a="success";return"PENDING"===t?a="warning":"COMPLETED"!==t&&(a="danger"),(0,r.jsx)(g.Wh,{status:t,variant:a})}},{key:"date",title:"Date",render:e=>new Date(e).toLocaleDateString()},{key:"reference",title:"Reference",render:e=>(0,r.jsx)("span",{className:"font-mono text-sm text-gray-400",children:e})},{key:"id",title:"Actions",render:e=>(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(g.rA,{onClick:()=>console.log("View",e),variant:"default",children:(0,r.jsx)(c.Vap,{className:"w-4 h-4"})}),(0,r.jsx)(g.rA,{onClick:()=>console.log("Download",e),variant:"primary",children:(0,r.jsx)(c.a4x,{className:"w-4 h-4"})})]})}],q=h.filter(e=>"WITHDRAWAL"===e.type&&"COMPLETED"===e.status).reduce((e,t)=>e+t.amount,0);return(h.filter(e=>"PENDING"===e.status).length,a)?(0,r.jsx)(u.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsxs)("div",{className:"text-center","data-aos":"fade-in",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Loading payments..."})]})})}):t&&(null==e?void 0:e.role)!=="ADMIN"?(0,r.jsx)(u.A,{title:"Payments",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0","data-aos":"fade-down",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Payments & Transactions"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Manage your deposits, withdrawals, and payment methods"})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsxs)("button",{onClick:()=>A(!0),className:"flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors","data-aos":"fade-left","data-aos-delay":"100",children:[(0,r.jsx)(c.OgA,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Deposit"})]}),(0,r.jsxs)("button",{onClick:()=>E(!0),className:"flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors","data-aos":"fade-left","data-aos-delay":"200",children:[(0,r.jsx)(c.qdV,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Withdraw"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsx)("div",{"data-aos":"fade-up","data-aos-delay":"50",children:(0,r.jsx)(d.hI,{title:"Available Balance",value:null!==v?"₦".concat(v.toLocaleString()):"—",subtitle:"Current wallet balance",icon:c.z8N,color:"blue"})}),(0,r.jsx)("div",{"data-aos":"fade-up","data-aos-delay":"100",children:(0,r.jsx)(d.hI,{title:"Total Deposits",value:"₦".concat(j.toLocaleString()),subtitle:"All time",icon:c.OgA,color:"green"})}),(0,r.jsx)("div",{"data-aos":"fade-up","data-aos-delay":"200",children:(0,r.jsx)(d.hI,{title:"Total Withdrawals",value:"₦".concat(q.toLocaleString()),subtitle:"All time",icon:c.qdV,color:"red"})})]}),(0,r.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},"data-aos":"fade-up","data-aos-delay":"400",children:(0,r.jsx)(g.Ay,{data:h,columns:V,searchable:!0,searchPlaceholder:"Search transactions...",emptyMessage:"No transactions found"})}),(0,r.jsx)(p.k5,{isOpen:D,onClose:()=>A(!1),title:"Make Deposit",onSubmit:I,submitText:"Deposit Funds",isLoading:S,children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(x.ZQ,{label:"Amount",name:"amount",type:"number",value:B.amount,onChange:e=>_(t=>({...t,amount:e.target.value})),placeholder:"100000",required:!0}),(0,r.jsx)(x.ZQ,{label:"Payment Method",name:"method",value:"Paystack (Card/Bank/USSD)",readOnly:!0,disabled:!0})]})}),(0,r.jsx)(p.k5,{isOpen:C,onClose:()=>E(!1),title:"Withdraw Funds",onSubmit:M,submitText:"Withdraw",isLoading:S,children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(x.ZQ,{label:"Amount",name:"amount",type:"number",value:B.amount,onChange:e=>_(t=>({...t,amount:e.target.value})),placeholder:"100000",required:!0}),(0,r.jsx)(x.pp,{label:"Bank",name:"bankCode",value:B.bankCode,onChange:e=>_(t=>({...t,bankCode:e.target.value})),required:!0,options:L.map(e=>({label:e.name,value:e.code,key:"".concat(e.code,"-").concat(e.name)}))}),(0,r.jsx)(x.ZQ,{label:"Account Number",name:"accountNumber",type:"text",value:B.accountNumber,onChange:e=>_(t=>({...t,accountNumber:e.target.value.replace(/\D/g,"").slice(0,10)})),placeholder:"**********",required:!0}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(x.ZQ,{label:"Account Name",name:"accountName",type:"text",value:B.accountName,readOnly:!0,disabled:!0}),(0,r.jsx)("button",{type:"button",className:"px-3 py-2 bg-blue-600 text-white rounded-lg",onClick:O,children:"Verify"})]}),(0,r.jsx)(x.ZQ,{label:"Payment Method",name:"method",value:"Paystack (Card/Bank/USSD)",readOnly:!0,disabled:!0})]})})]})}):null}}},e=>{e.O(0,[9096,844,9268,5236,6874,6766,3568,5221,100,3289,4630,1846,6560,8441,5964,7358],()=>e(e.s=63739)),_N_E=e.O()}]);