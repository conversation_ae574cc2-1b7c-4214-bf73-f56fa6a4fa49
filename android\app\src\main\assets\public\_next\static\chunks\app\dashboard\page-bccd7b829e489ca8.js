(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5105],{13428:(e,t,r)=>{"use strict";r.d(t,{r:()=>l});var a=r(98030),s=r(35695),n=r(12115);function l(){let{redirect:e=!0}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{user:t,isLoading:r,isAuthenticated:l}=(0,a.A)(),o=(0,s.useRouter)();return(0,n.useEffect)(()=>{!r&&l&&(null==t?void 0:t.kycStatus)!=="APPROVED"&&e&&o.push("/dashboard/kyc")},[t,r,l,e,o]),(null==t?void 0:t.kycStatus)==="APPROVED"}},16108:(e,t,r)=>{"use strict";let a,s,n,l,o,i,c,d,x,u,g,h,m,b;r.r(t),r.d(t,{default:()=>H});var p=r(95155),v=r(12115),f=r(10351);let j=e=>{let{size:t=24,className:r=""}=e;return(0,p.jsx)("div",{className:"relative flex items-center justify-center ".concat(r),style:{width:t,height:t},children:(0,p.jsxs)("svg",{width:t,height:t,viewBox:"0 0 100 100",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"drop-shadow-lg",children:[(0,p.jsx)("path",{d:"M30 20 C30 15, 35 10, 40 10 L60 10 C65 10, 70 15, 70 20 L70 25 L30 25 Z",fill:"currentColor",className:"text-green-400",stroke:"currentColor",strokeWidth:"3",strokeLinejoin:"round"}),(0,p.jsx)("rect",{x:"15",y:"25",width:"70",height:"50",rx:"8",ry:"8",fill:"currentColor",className:"text-green-400",stroke:"currentColor",strokeWidth:"3",strokeLinejoin:"round"}),(0,p.jsx)("rect",{x:"45",y:"20",width:"10",height:"8",rx:"2",fill:"currentColor",className:"text-green-400"}),(0,p.jsx)("rect",{x:"22",y:"32",width:"56",height:"36",rx:"4",fill:"rgba(255, 255, 255, 0.9)",stroke:"currentColor",className:"text-green-500",strokeWidth:"2"}),(0,p.jsxs)("g",{className:"text-green-600",fill:"currentColor",children:[(0,p.jsx)("rect",{x:"35",y:"40",width:"3",height:"20"}),(0,p.jsx)("rect",{x:"62",y:"40",width:"3",height:"20"}),(0,p.jsx)("path",{d:"M35 58 L65 42",stroke:"currentColor",strokeWidth:"3",strokeLinecap:"round"}),(0,p.jsx)("rect",{x:"30",y:"45",width:"40",height:"2"}),(0,p.jsx)("rect",{x:"30",y:"53",width:"40",height:"2"})]}),(0,p.jsx)("circle",{cx:"25",cy:"50",r:"2",fill:"currentColor",className:"text-green-500"}),(0,p.jsx)("circle",{cx:"75",cy:"50",r:"2",fill:"currentColor",className:"text-green-500"}),(0,p.jsx)("rect",{x:"47",y:"12",width:"1",height:"6",fill:"rgba(255, 255, 255, 0.6)"}),(0,p.jsx)("rect",{x:"49",y:"12",width:"1",height:"6",fill:"rgba(255, 255, 255, 0.6)"}),(0,p.jsx)("rect",{x:"51",y:"12",width:"1",height:"6",fill:"rgba(255, 255, 255, 0.6)"})]})})},y=e=>{let{size:t=48,className:r="",showPulse:a=!0}=e;return(0,p.jsx)("div",{className:"flex items-center justify-center ".concat(r),children:(0,p.jsx)("div",{className:"".concat(a?"animate-pulse":""),children:(0,p.jsx)(j,{size:t,className:"text-green-400 drop-shadow-lg"})})})};var w=r(68289);try{let e=r(32417);({LineChart:a,Line:s,XAxis:n,YAxis:l,CartesianGrid:o,Tooltip:i,ResponsiveContainer:c,AreaChart:d,Area:x,PieChart:u,Pie:g,Cell:h,BarChart:m,Bar:b}=e)}catch(e){console.warn("Recharts not available:",e)}let N=e=>{let{active:t,payload:r,label:a}=e;return t&&r&&r.length?(0,p.jsxs)("div",{className:"bg-gray-900 border border-gray-700 rounded-lg p-3 shadow-lg",children:[(0,p.jsx)("p",{className:"text-gray-300 text-sm",children:a}),r.map((e,t)=>{var r;return(0,p.jsxs)("p",{className:"text-green-400 font-medium",children:[e.name,": ₦",null==(r=e.value)?void 0:r.toLocaleString()]},t)})]}):null},S=e=>{let{title:t,data:r}=e;return(0,p.jsx)("div",{className:"bg-gray-800/50 rounded-lg p-6 h-64 flex items-center justify-center",children:(0,p.jsxs)("div",{className:"text-center",children:[(0,p.jsx)("div",{className:"text-gray-400 mb-2 text-2xl",children:"\uD83D\uDCCA"}),(0,p.jsx)("h3",{className:"text-white font-semibold mb-2",children:t}),(0,p.jsx)("p",{className:"text-gray-400 text-sm",children:"Chart visualization unavailable"}),(0,p.jsxs)("div",{className:"mt-4 text-xs text-gray-500",children:[r.length," data points available"]})]})})};function D(e){var t;let{data:r,title:d,color:x="#10B981",height:u=300}=e;return a&&c?(0,p.jsxs)(w.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6",children:[d&&(0,p.jsx)("h3",{className:"text-white font-semibold mb-4",children:d}),(0,p.jsx)(c,{width:"100%",height:u,children:(0,p.jsxs)(a,{data:r,children:[(0,p.jsx)(o,{strokeDasharray:"3 3",stroke:"#374151"}),(0,p.jsx)(n,{dataKey:"name",stroke:"#9CA3AF",fontSize:12}),(0,p.jsx)(l,{stroke:"#9CA3AF",fontSize:12,tickFormatter:e=>"₦".concat(e.toLocaleString())}),(0,p.jsx)(i,{content:(0,p.jsx)(N,{})}),(0,p.jsx)(s,{type:"monotone",dataKey:"value",stroke:x,strokeWidth:3,dot:{fill:x,strokeWidth:2,r:4},activeDot:{r:6,stroke:x,strokeWidth:2}}),(null==(t=r[0])?void 0:t.target)&&(0,p.jsx)(s,{type:"monotone",dataKey:"target",stroke:"#6B7280",strokeWidth:2,strokeDasharray:"5 5",dot:!1})]})})]}):(0,p.jsx)(S,{data:r,title:d})}var A=r(58111),k=r(11846),P=r(87101),C=r(49697),R=r(13428),B=r(24630);function H(){let[e,t]=(0,v.useState)(null),[r,a]=(0,v.useState)(null),[s,n]=(0,v.useState)([]),[l,o]=(0,v.useState)([]),[i,c]=(0,v.useState)([]),[d,x]=(0,v.useState)(!0),u=(0,R.r)({redirect:!1});return((0,v.useEffect)(()=>{!async function(){x(!0);try{console.log("[DASHBOARD] Fetching user profile...");let e=await B.Dv.getCurrentUserProfile();console.log("[DASHBOARD] userService.getCurrentUserProfile:",e),e?(console.log("[DASHBOARD][DEBUG] user fields:",Object.keys(e),e),console.log("[DASHBOARD][DEBUG] user.balance:",e.balance)):console.log("[DASHBOARD][DEBUG] userRes is null or undefined"),t(e),e&&console.log("[DASHBOARD][DEBUG] user fields:",Object.keys(e),e),console.log("[DASHBOARD] Fetching savings summary...");let r=await fetch("".concat("http://localhost:8080","/api/savings/summary"),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("auth_token"))}}),s=null;r.ok?s=await r.json():console.error("[DASHBOARD] Failed to fetch savings summary:",r.status,await r.text()),console.log("[DASHBOARD] savings summary:",s),a(s),console.log("[DASHBOARD] Fetching user savings plans...");let l=await B.TA.getUserSavingsPlans();console.log("[DASHBOARD] savingsService.getUserSavingsPlans:",l),n(l||[]),console.log("[DASHBOARD] Fetching group savings plans...");let i=await B.Iu.getUserGroups();console.log("[DASHBOARD] groupSavingsService.getUserGroups:",i),o(i||[]),console.log("[DASHBOARD] Fetching user transactions...");let d=null;e&&e.id?(d=await B.lD.getUserTransactions(e.id),console.log("[DASHBOARD] transactionsService.getUserTransactions:",d),c(d&&d.transactions||[])):c([])}catch(e){console.error("[DASHBOARD] fetchData error:",e)}x(!1)}()},[]),d)?(0,p.jsx)("div",{className:"flex justify-center items-center min-h-[60vh]",children:(0,p.jsx)(y,{})}):(0,p.jsx)(k.A,{title:"Dashboard",children:(0,p.jsxs)("div",{className:"space-y-6",children:[!u&&(0,p.jsx)(P.I,{}),(0,p.jsxs)("div",{className:"bg-gradient-to-r from-brand/10 to-brand/5 border border-brand/30 rounded-xl p-8 relative overflow-hidden shadow-xl",children:[(0,p.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[(0,p.jsx)("div",{className:"absolute top-4 right-4 w-32 h-32 bg-brand rounded-full blur-3xl"}),(0,p.jsx)("div",{className:"absolute bottom-4 left-4 w-24 h-24 bg-brand rounded-full blur-2xl"})]}),(0,p.jsxs)("div",{className:"flex items-center justify-between relative z-10",children:[(0,p.jsxs)("div",{className:"flex-1",children:[(0,p.jsxs)("h1",{className:"text-4xl font-inter font-bold text-theme mb-3 tracking-tight",children:["Welcome back, ",(0,p.jsx)("span",{className:"text-brand",children:null==e?void 0:e.firstName}),"! \uD83D\uDC4B"]}),(0,p.jsx)("p",{className:"text-theme-secondary text-lg mb-6 font-inter",children:"Here's an overview of your savings journey and financial progress."}),(0,p.jsxs)("div",{className:"flex items-center space-x-6 text-sm",children:[(0,p.jsxs)("span",{className:"flex items-center space-x-2 text-brand font-medium",children:[(0,p.jsx)("span",{children:"\uD83D\uDCB0"}),(0,p.jsx)("span",{children:"Better Interest Rates"})]}),(0,p.jsxs)("span",{className:"flex items-center space-x-2 text-brand font-medium",children:[(0,p.jsx)("span",{children:"\uD83D\uDCC8"}),(0,p.jsx)("span",{children:"Growing Savings"})]}),(0,p.jsxs)("span",{className:"flex items-center space-x-2 text-brand font-medium",children:[(0,p.jsx)("span",{children:"\uD83C\uDFAF"}),(0,p.jsx)("span",{children:"Goals Achieved"})]})]})]}),(0,p.jsx)("div",{className:"hidden md:flex relative ml-6 justify-center",children:(0,p.jsx)(C.co,{src:"/Celebrating with her iPhone 14 Pro.png",alt:"Happy user celebrating savings success",width:128,height:128,intensity:"light",className:"border-2 border-green-400/50 mx-auto"})})]})]}),(0,p.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6",children:[(()=>{let t=e&&void 0!==e.balance&&null!==e.balance&&!isNaN(Number(e.balance))?Number(e.balance):0;return console.log("[DASHBOARD][RENDER] Wallet Balance value for StatCard:",t),(0,p.jsx)(A.hI,{title:"Wallet Balance",value:"₦".concat(t.toLocaleString()),subtitle:"Available balance",icon:f.z8N,color:"green",trend:{value:0,isPositive:!0}})})(),(0,p.jsx)(A.hI,{title:"Total Savings",value:r&&"number"==typeof r.totalTargetAmount?"₦".concat(r.totalTargetAmount.toLocaleString()):"₦0",subtitle:"All plan targets",icon:f.ARf,color:"blue",trend:{value:0,isPositive:!0}}),(0,p.jsx)(A.hI,{title:"Total Saved",value:r&&"number"==typeof r.totalSaved?"₦".concat(r.totalSaved.toLocaleString()):"₦0",subtitle:"Deposited so far",icon:f.eXT,color:"purple"}),(0,p.jsx)(A.hI,{title:"Total Earnings",value:r&&"number"==typeof r.totalInterestEarned?"₦".concat(r.totalInterestEarned.toLocaleString()):"₦0",subtitle:"Interest & bonuses",icon:f.eXT,color:"purple"}),(0,p.jsx)(A.hI,{title:"Active Plans",value:r&&"number"==typeof r.activePlans?r.activePlans:0,subtitle:"Individual & Group",icon:f.eXT,color:"yellow"})]}),(0,p.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,p.jsx)(A.Lz,{title:"Create Savings Plan",subtitle:"Start a new individual savings plan",icon:f.GGD,color:"green",onClick:()=>window.location.href="/dashboard/savings-plans"}),(0,p.jsx)(A.Lz,{title:"Join Group Savings",subtitle:"Find and join group savings plans",icon:f.cfS,color:"blue",onClick:()=>window.location.href="/dashboard/group-savings"}),(0,p.jsx)(A.Lz,{title:"Make Payment",subtitle:"Add funds to your savings",icon:f.lZI,color:"purple",onClick:()=>window.location.href="/dashboard/payments"})]}),(0,p.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(console.log("[DASHBOARD][RENDER] savingsPlans for chart:",s),null),(0,p.jsx)(D,{data:s.length>0?s.map(e=>({name:e.name||"Untitled",value:"number"==typeof e.currentAmount?e.currentAmount:0})):[{name:"No Plans",value:0}],title:"Savings Progress",height:300}),(0,p.jsxs)("div",{className:"space-y-4",children:[(0,p.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Goals Progress"}),s.map((e,t)=>(0,p.jsx)(A.gz,{title:e.name,current:e.currentAmount,target:e.targetAmount,unit:"₦",color:"green"},e.id||"plan-".concat(t)))]})]}),(0,p.jsxs)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6",children:[(0,p.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,p.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Recent Activity"})}),(0,p.jsx)("div",{className:"space-y-3",children:i.slice(0,5).map((e,t)=>{var r;return(0,p.jsxs)("div",{className:"flex items-center justify-between py-3 border-b border-gray-800 last:border-b-0",children:[(0,p.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,p.jsxs)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat("DEPOSIT"===e.type?"bg-green-500/20":"INTEREST"===e.type?"bg-purple-500/20":"WITHDRAWAL"===e.type?"bg-blue-500/20":"bg-yellow-500/20"),children:["DEPOSIT"===e.type&&(0,p.jsx)(f.z8N,{className:"w-4 h-4 text-green-400"}),"INTEREST"===e.type&&(0,p.jsx)(f.ARf,{className:"w-4 h-4 text-purple-400"}),"WITHDRAWAL"===e.type&&(0,p.jsx)(f.lZI,{className:"w-4 h-4 text-blue-400"}),"PENALTY"===e.type&&(0,p.jsx)(f.x_j,{className:"w-4 h-4 text-yellow-400"})]}),(0,p.jsxs)("div",{children:[(0,p.jsx)("p",{className:"text-white text-sm font-medium",children:e.type}),(0,p.jsx)("p",{className:"text-gray-400 text-xs",children:e.description||e.planId||e.goalId})]})]}),(0,p.jsxs)("div",{className:"text-right",children:[(0,p.jsxs)("p",{className:"text-green-400 text-sm font-medium",children:["₦",null==(r=e.amount)?void 0:r.toLocaleString()]}),(0,p.jsx)("p",{className:"text-gray-500 text-xs",children:new Date(e.createdAt).toLocaleString()})]})]},t)})})]})]})})}},49697:(e,t,r)=>{"use strict";r.d(t,{HC:()=>x,XT:()=>g,co:()=>u,nv:()=>d});var a=r(95155),s=r(12115),n=r(66766),l=r(68289);let o={default:{container:"relative inline-block",wrapper:"relative overflow-hidden rounded-lg",transform:"none",hoverTransform:"scale(1.05)",shadow:"rgba(0, 0, 0, 0.24) 0px 8px 20px"},hero:{container:"relative inline-block",wrapper:"relative overflow-hidden rounded-xl",transform:"perspective(600px) rotateX(20deg) rotateZ(-8deg)",hoverTransform:"perspective(600px) rotateX(8deg) rotateY(15deg) rotateZ(-3deg)",shadow:"rgba(0, 0, 0, 0.3) -15px 25px 30px"},card:{container:"relative inline-block",wrapper:"relative overflow-hidden rounded-lg",transform:"none",hoverTransform:"scale(1.03)",shadow:"rgba(0, 0, 0, 0.2) 0px 6px 15px"},testimonial:{container:"relative inline-block",wrapper:"relative overflow-hidden rounded-full",transform:"none",hoverTransform:"scale(1.1)",shadow:"rgba(0, 0, 0, 0.15) 0px 4px 12px"},feature:{container:"relative inline-block",wrapper:"relative overflow-hidden rounded-lg",transform:"none",hoverTransform:"scale(1.02)",shadow:"rgba(0, 0, 0, 0.25) 0px 8px 25px"}},i={light:.5,medium:1,strong:1.5};function c(e){let{src:t,alt:r,width:c,height:d,className:x="",priority:u=!1,fill:g=!1,sizes:h,quality:m=75,objectFit:b="cover",variant:p="default",intensity:v="medium",...f}=e,[j,y]=(0,s.useState)(!1),[w,N]=(0,s.useState)(!0),[S,D]=(0,s.useState)(!1),A=o[p];return(i[v],S)?(0,a.jsx)("div",{className:"bg-gray-800 border border-gray-700 flex items-center justify-center ".concat(A.wrapper," ").concat(x),style:{width:g?"100%":c,height:g?"100%":d,transform:A.transform,boxShadow:A.shadow,transformStyle:"preserve-3d",transition:"transform 0.6s ease-out"},children:(0,a.jsxs)("div",{className:"text-center text-gray-400",children:[(0,a.jsx)("svg",{className:"w-8 h-8 mx-auto mb-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z",clipRule:"evenodd"})}),(0,a.jsx)("p",{className:"text-xs",children:"Image not found"})]})}):(0,a.jsxs)("div",{className:A.container,children:[w&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gray-800 animate-pulse ".concat(A.wrapper),style:{width:g?"100%":c,height:g?"100%":d,transform:A.transform,boxShadow:A.shadow,transformStyle:"preserve-3d",zIndex:10},children:(0,a.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,a.jsx)("div",{className:"w-8 h-8 border-2 border-green-400 border-t-transparent rounded-full animate-spin"})})}),(0,a.jsxs)(l.P.div,{className:"".concat(A.wrapper," ").concat(x),style:{transformStyle:"preserve-3d",transform:A.transform,boxShadow:A.shadow,transition:"transform 0.6s ease-out, box-shadow 0.6s ease-out"},animate:{transform:j?A.hoverTransform:A.transform,boxShadow:j?A.shadow.replace(/rgba\(0, 0, 0, ([\d.]+)\)/,(e,t)=>"rgba(0, 0, 0, ".concat(1.3*parseFloat(t),")")):A.shadow},transition:{type:"spring",stiffness:300,damping:30},onMouseEnter:()=>y(!0),onMouseLeave:()=>y(!1),children:[(0,a.jsx)("div",{className:"absolute inset-0 z-10 pointer-events-none",style:{background:"linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.05) 50%, rgba(0, 0, 0, 0.1) 100%)",opacity:j?.8:.4,transition:"opacity 0.3s ease"}}),(0,a.jsx)(n.default,{src:t,alt:r,width:g?void 0:c,height:g?void 0:d,fill:g,priority:u,quality:m,sizes:h,className:"\n            ".concat("cover"===b?"object-cover":"","\n            ").concat("contain"===b?"object-contain":"","\n            ").concat("fill"===b?"object-fill":"","\n            ").concat("none"===b?"object-none":"","\n            ").concat("scale-down"===b?"object-scale-down":"","\n            transition-transform duration-300\n          "),style:{opacity:+!w,transition:"opacity 0.3s ease"},onLoad:()=>{N(!1)},onError:()=>{N(!1),D(!0)},...f}),(0,a.jsx)(l.P.div,{className:"absolute inset-0 pointer-events-none",style:{background:"linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%)",transform:"translateX(-100%)"},animate:{transform:j?"translateX(100%)":"translateX(-100%)"},transition:{duration:.6,ease:"easeInOut"}})]})]})}function d(e){return(0,a.jsx)(c,{...e,variant:"hero"})}function x(e){return(0,a.jsx)(c,{...e,variant:"card"})}function u(e){return(0,a.jsx)(c,{...e,variant:"testimonial"})}function g(e){return(0,a.jsx)(c,{...e,variant:"feature"})}},58111:(e,t,r)=>{"use strict";r.d(t,{Lz:()=>d,gz:()=>x,hI:()=>c});var a=r(95155),s=r(12115),n=r(68289),l=r(57740);let o={green:{bg:"bg-green-500/20",text:"text-green-400",border:"border-green-500/30",icon:"text-green-400"},blue:{bg:"bg-blue-500/20",text:"text-blue-400",border:"border-blue-500/30",icon:"text-blue-400"},red:{bg:"bg-red-500/20",text:"text-red-400",border:"border-red-500/30",icon:"text-red-400"},yellow:{bg:"bg-yellow-500/20",text:"text-yellow-400",border:"border-yellow-500/30",icon:"text-yellow-400"},purple:{bg:"bg-purple-500/20",text:"text-purple-400",border:"border-purple-500/30",icon:"text-purple-400"}};function i(e){var t,r,i,c;let{title:d,value:x,subtitle:u,icon:g,trend:h,color:m="green",onClick:b,className:p=""}=e,v=o[m]||o.green,{theme:f}=(0,l.DP)(),j=(0,l.Yx)(f),y=(0,s.useRef)(null),w=e=>{let t={1:"light"===f?"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)":"0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.5)",2:"light"===f?"0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)":"0 3px 6px rgba(0, 0, 0, 0.4), 0 3px 6px rgba(0, 0, 0, 0.6)",3:"light"===f?"0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)":"0 10px 20px rgba(0, 0, 0, 0.5), 0 6px 6px rgba(0, 0, 0, 0.7)"};return t[e]||t[1]};return(0,a.jsxs)(n.P.div,{ref:y,initial:{opacity:0,y:20},animate:{opacity:1,y:0},whileHover:{y:-4,boxShadow:w(3),transition:{duration:.2,ease:"easeOut"}},className:"\n        relative overflow-hidden rounded-xl p-6 transition-all duration-300 group\n        ".concat(b?"cursor-pointer":"","\n        ").concat("light"===f?"bg-white border border-gray-200 hover:border-gray-300":"bg-gray-900/80 border border-gray-700 hover:border-gray-600","\n        ").concat(p,"\n      "),style:{boxShadow:w(1)},onClick:b,children:[b&&(0,a.jsx)(n.P.div,{className:"absolute inset-0 bg-green-400 opacity-0 group-hover:opacity-5 transition-opacity duration-300",initial:{scale:0,opacity:0},whileHover:{scale:1,opacity:.05},transition:{duration:.3}}),(0,a.jsx)("div",{className:"absolute top-0 left-0 w-full h-1 bg-gradient-to-r ".concat((null==v||null==(t=v.bg)?void 0:t.replace("bg-","from-"))||"from-brand"," to-transparent opacity-60")}),(0,a.jsxs)("div",{className:"flex items-start justify-between relative z-10",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium mb-3 ".concat(j.text.secondary),children:d}),(0,a.jsx)("p",{className:"text-3xl font-bold mb-2 ".concat(j.text.primary),style:{fontFamily:"Inter, system-ui, sans-serif"},children:x}),u&&(0,a.jsx)("p",{className:"text-sm ".concat(j.text.tertiary," mb-2"),children:u}),h&&(0,a.jsxs)(n.P.div,{className:"flex items-center mt-3",initial:{opacity:0,x:-10},animate:{opacity:1,x:0},transition:{delay:.2},children:[(0,a.jsxs)("span",{className:"text-sm font-semibold px-2 py-1 rounded-full ".concat(h.isPositive?"text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/30":"text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/30"),children:[h.isPositive?"↗":"↘"," ",h.isPositive?"+":"",h.value,"%"]}),(0,a.jsx)("span",{className:"text-xs ml-2 ".concat(j.text.tertiary),children:"vs last month"})]})]}),g&&(0,a.jsx)(n.P.div,{className:"p-4 rounded-xl ".concat((null==v?void 0:v.bg)||"bg-brand/20"," ").concat((null==v?void 0:v.border)||"border-brand"," border-2 shadow-lg"),whileHover:{scale:1.05,rotate:5},transition:{duration:.2},style:{boxShadow:"0 4px 12px ".concat((null==v||null==(r=v.bg)?void 0:r.includes("green"))?"rgba(34, 197, 94, 0.3)":(null==v||null==(i=v.bg)?void 0:i.includes("blue"))?"rgba(59, 130, 246, 0.3)":(null==v||null==(c=v.bg)?void 0:c.includes("purple"))?"rgba(147, 51, 234, 0.3)":"rgba(34, 197, 94, 0.3)")},children:(0,a.jsx)(g,{className:"w-7 h-7 ".concat((null==v?void 0:v.icon)||"text-brand")})})]})]})}function c(e){let{title:t,value:r,subtitle:s,icon:n,color:l="green",trend:o}=e;return(0,a.jsx)(i,{title:t,value:r,subtitle:s,icon:n,color:l,trend:o})}function d(e){var t,r,s,i;let{title:c,subtitle:d,icon:x,color:u="blue",onClick:g}=e,h=o[u]||o.blue,{theme:m}=(0,l.DP)(),b=(0,l.Yx)(m);return(0,a.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},whileHover:{y:-6,scale:1.02,boxShadow:"light"===m?"0 8px 25px rgba(0, 0, 0, 0.15), 0 16px 40px rgba(0, 0, 0, 0.1)":"0 8px 25px rgba(0, 0, 0, 0.4), 0 16px 40px rgba(0, 0, 0, 0.3)",transition:{duration:.2,ease:"easeOut"}},whileTap:{scale:.98},className:"\n        relative overflow-hidden rounded-xl p-6 cursor-pointer transition-all duration-300 group\n        ".concat("light"===m?"bg-gradient-to-br from-white to-gray-50 border border-gray-200 hover:border-gray-300":"bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-700 hover:border-gray-600","\n      "),style:{boxShadow:"light"===m?"0 2px 8px rgba(0, 0, 0, 0.1), 0 4px 16px rgba(0, 0, 0, 0.05)":"0 2px 8px rgba(0, 0, 0, 0.3), 0 4px 16px rgba(0, 0, 0, 0.2)"},onClick:g,children:[(0,a.jsx)(n.P.div,{className:"absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300 ".concat((null==h?void 0:h.bg)||"bg-brand/10"),initial:{scale:0},whileHover:{scale:1},transition:{duration:.3}}),(0,a.jsx)("div",{className:"absolute top-0 left-0 w-full h-1 bg-gradient-to-r ".concat((null==h||null==(t=h.bg)?void 0:t.replace("bg-","from-"))||"from-brand"," to-transparent")}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 relative z-10",children:[(0,a.jsx)(n.P.div,{className:"p-4 rounded-xl ".concat((null==h?void 0:h.bg)||"bg-brand/20"," shadow-lg"),whileHover:{scale:1.1,rotate:10},transition:{duration:.2},style:{boxShadow:"0 4px 12px ".concat((null==h||null==(r=h.bg)?void 0:r.includes("green"))?"rgba(34, 197, 94, 0.3)":(null==h||null==(s=h.bg)?void 0:s.includes("blue"))?"rgba(59, 130, 246, 0.3)":(null==h||null==(i=h.bg)?void 0:i.includes("purple"))?"rgba(147, 51, 234, 0.3)":"rgba(34, 197, 94, 0.3)")},children:(0,a.jsx)(x,{className:"w-6 h-6 ".concat((null==h?void 0:h.icon)||"text-brand")})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"font-semibold text-lg ".concat(b.text.primary," mb-1"),children:c}),(0,a.jsx)("p",{className:"text-sm ".concat(b.text.secondary),children:d})]}),(0,a.jsx)(n.P.div,{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat((null==h?void 0:h.bg)||"bg-brand/20"," opacity-70"),whileHover:{scale:1.2,opacity:1},transition:{duration:.2},children:(0,a.jsx)("svg",{className:"w-4 h-4 ".concat((null==h?void 0:h.icon)||"text-brand"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]})]})}function x(e){let{title:t,current:r,target:s,unit:l="",color:i="green"}=e,c=Math.min(r/s*100,100),d=o[i]||o.green;return(0,a.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-white font-semibold mb-4",children:t}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Progress"}),(0,a.jsxs)("span",{className:(null==d?void 0:d.text)||"text-brand",children:[c.toFixed(1),"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-800 rounded-full h-2",children:(0,a.jsx)(n.P.div,{initial:{width:0},animate:{width:"".concat(c,"%")},transition:{duration:1,ease:"easeOut"},className:"h-2 rounded-full bg-gradient-to-r from-".concat(i,"-400 to-").concat(i,"-600")})}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsxs)("span",{className:"text-gray-400",children:[r.toLocaleString(),l," / ",s.toLocaleString(),l]}),(0,a.jsxs)("span",{className:"text-gray-400",children:[(s-r).toLocaleString(),l," remaining"]})]})]})]})}},84158:(e,t,r)=>{Promise.resolve().then(r.bind(r,16108))},87101:(e,t,r)=>{"use strict";r.d(t,{I:()=>l});var a=r(95155);r(12115);var s=r(10351),n=r(35695);function l(e){let{className:t=""}=e,r=(0,n.useRouter)();return(0,a.jsxs)("div",{className:"bg-red-600/10 border border-red-600 rounded-lg p-6 mb-6 flex items-center space-x-3 ".concat(t),children:[(0,a.jsx)(s.p45,{className:"w-6 h-6 text-red-500"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-red-500 font-semibold",children:"Complete Your KYC Verification"}),(0,a.jsx)("p",{className:"text-red-300 text-sm",children:"Verify your identity to unlock higher savings limits and additional features."})]}),(0,a.jsx)("button",{onClick:()=>r.push("/dashboard/kyc"),className:"px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors",children:"Verify Now"})]})}}},e=>{e.O(0,[844,9268,5236,6874,6766,5221,2210,3289,4630,1846,8441,5964,7358],()=>e(e.s=84158)),_N_E=e.O()}]);