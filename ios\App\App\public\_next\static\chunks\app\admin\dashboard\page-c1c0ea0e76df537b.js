(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5957],{17703:(e,s,t)=>{"use strict";t.d(s,{A:()=>i,Z:()=>l});var a=t(95155);t(12115);var r=t(68289);function l(e){let{children:s,className:t="",hover:l=!1,onClick:i,variant:c="default"}=e,n="\n    rounded-xl border transition-all duration-300\n    ".concat(l?"hover:shadow-lg hover:scale-[1.02] cursor-pointer":"","\n    ").concat(i?"cursor-pointer":"","\n  "),d="".concat(n," ").concat({default:"bg-gray-800 border-gray-700",gradient:"bg-gradient-to-br from-gray-800 to-gray-900 border-gray-700",glass:"bg-gray-800/50 backdrop-blur-lg border-gray-700/50"}[c]," ").concat(t);return l||i?(0,a.jsx)(r.P.div,{className:d,onClick:i,whileHover:l?{scale:1.02}:void 0,whileTap:i?{scale:.98}:void 0,transition:{type:"spring",stiffness:400,damping:10},children:s}):(0,a.jsx)("div",{className:d,children:s})}let i=l},35695:(e,s,t)=>{"use strict";var a=t(18999);t.o(a,"usePathname")&&t.d(s,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}})},38955:(e,s,t)=>{Promise.resolve().then(t.bind(t,51502))},51502:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var a=t(95155),r=t(12115),l=t(10351),i=t(26071),c=t(24630),n=t(13741),d=t(17703),o=t(52814);function x(){let[e,s]=(0,r.useState)(null),[t,x]=(0,r.useState)(!0),[m,h]=(0,r.useState)("month"),[g,p]=(0,r.useState)([]);(0,r.useEffect)(()=>{j(),y()},[m]);let y=async()=>{try{let e=await fetch("/api/admin/dashboard/logs",{headers:{Authorization:"Bearer ".concat(localStorage.getItem("auth_token"))}});if(e.ok){let s=await e.json();p(s.logs||[])}else p([])}catch(e){p([])}};(0,r.useEffect)(()=>{j()},[m]);let j=async()=>{try{x(!0);let e=await c.ZJ.getDashboardStats({period:m});s(e)}catch(e){console.error("Failed to load dashboard stats:",e)}finally{x(!1)}},u=e=>new Intl.NumberFormat("en-NG",{style:"currency",currency:"NGN"}).format(e),b=e=>"".concat(e>=0?"+":"").concat(e.toFixed(1),"%");return t?(0,a.jsx)(i.A,{title:"Dashboard",children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"})})}):e?(0,a.jsx)(i.A,{title:"Dashboard",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white",children:"Admin Dashboard"}),(0,a.jsx)("p",{className:"text-gray-400 mt-2",children:"Overview of platform performance and metrics"})]}),(0,a.jsx)("div",{className:"flex space-x-2",children:["week","month","year"].map(e=>(0,a.jsx)(n.Ay,{variant:m===e?"primary":"outline",onClick:()=>h(e),size:"sm",children:e.charAt(0).toUpperCase()+e.slice(1)},e))})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)(d.A,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Users"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:e.users.total.toLocaleString()}),(0,a.jsxs)("p",{className:"text-green-400 text-sm",children:["+",e.users.newThisMonth," this month"]})]}),(0,a.jsx)(l.cfS,{className:"text-blue-500 text-3xl"})]})}),(0,a.jsx)(d.A,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Savings"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:u(e.savings.totalSavingsAmount)}),(0,a.jsxs)("p",{className:"text-green-400 text-sm",children:[e.savings.activePlans," active plans"]})]}),(0,a.jsx)(l.z8N,{className:"text-green-500 text-3xl"})]})}),(0,a.jsx)(d.A,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Transaction Volume"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:u(e.transactions.totalVolume)}),(0,a.jsxs)("p",{className:"text-green-400 text-sm",children:[b(e.transactions.successRate)," success rate"]})]}),(0,a.jsx)(l.z1n,{className:"text-purple-500 text-3xl"})]})}),(0,a.jsx)(d.A,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Pending Withdrawals"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:e.withdrawals.pendingWithdrawals}),(0,a.jsx)("p",{className:"text-yellow-400 text-sm",children:u(e.withdrawals.pendingAmount)})]}),(0,a.jsx)(l.lZI,{className:"text-yellow-500 text-3xl"})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(d.A,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"User Growth"}),(0,a.jsx)(l.ARf,{className:"text-green-500"})]}),(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)("div",{className:"w-full h-full bg-gray-700 rounded-lg flex items-center justify-center",children:(0,a.jsx)("p",{className:"text-gray-400",children:"User Growth Chart"})})})]}),(0,a.jsxs)(d.A,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Transaction Volume"}),(0,a.jsx)(l.hht,{className:"text-blue-500"})]}),(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)("div",{className:"w-full h-full bg-gray-700 rounded-lg flex items-center justify-center",children:(0,a.jsx)("p",{className:"text-gray-400",children:"Transaction Volume Chart"})})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)(d.A,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[(0,a.jsx)(l.cfS,{className:"mr-2"}),"User Statistics"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Active Users"}),(0,a.jsx)("span",{className:"text-white font-semibold",children:e.users.active.toLocaleString()})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"KYC Approved"}),(0,a.jsx)("span",{className:"text-green-400 font-semibold",children:e.users.kycApproved.toLocaleString()})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"KYC Pending"}),(0,a.jsx)("span",{className:"text-yellow-400 font-semibold",children:e.users.kycPending.toLocaleString()})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"KYC Rejected"}),(0,a.jsx)("span",{className:"text-red-400 font-semibold",children:e.users.kycRejected.toLocaleString()})]})]})]}),(0,a.jsxs)(d.A,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[(0,a.jsx)(l.z8N,{className:"mr-2"}),"Savings Overview"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Total Plans"}),(0,a.jsx)("span",{className:"text-white font-semibold",children:e.savings.totalPlans.toLocaleString()})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Active Plans"}),(0,a.jsx)("span",{className:"text-green-400 font-semibold",children:e.savings.activePlans.toLocaleString()})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Completed Plans"}),(0,a.jsx)("span",{className:"text-blue-400 font-semibold",children:e.savings.completedPlans.toLocaleString()})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Interest Paid"}),(0,a.jsx)("span",{className:"text-purple-400 font-semibold",children:u(e.savings.totalInterestPaid)})]})]})]}),(0,a.jsxs)(d.A,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[(0,a.jsx)(l.pcC,{className:"mr-2"}),"System Health"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Deposit Success Rate"}),(0,a.jsx)(o.A,{variant:"success",children:b(e.deposits.successRate)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Transaction Success Rate"}),(0,a.jsx)(o.A,{variant:"success",children:b(e.transactions.successRate)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Avg Processing Time"}),(0,a.jsxs)("span",{className:"text-white font-semibold",children:[e.withdrawals.averageProcessingTime,"h"]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Notification Delivery"}),(0,a.jsx)(o.A,{variant:"success",children:b(e.notifications.deliveryRate)})]})]})]})]}),(0,a.jsxs)(d.A,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[(0,a.jsx)(l.z1n,{className:"mr-2"}),"Recent Activity"]}),(0,a.jsx)("div",{className:"space-y-3",children:0===g.length?(0,a.jsx)("div",{className:"text-gray-400 text-sm",children:"No recent activity."}):g.map((e,s)=>{let t={FiUsers:l.cfS,FiCreditCard:l.lZI,FiShield:l.pcC,FiCheckCircle:l.A3x,FiAlertTriangle:l.eHT,FiActivity:l.z1n}[e.icon]||l.z1n,r={user:"text-blue-500",withdrawal:"text-yellow-500",kyc:"text-green-500",system:"text-green-500",alert:"text-orange-500"}[e.type]||"text-gray-400";return(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-gray-700 rounded-lg",children:[(0,a.jsx)(t,{className:"".concat(r," text-lg")}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-white text-sm",children:e.message}),(0,a.jsx)("p",{className:"text-gray-400 text-xs",children:e.time})]})]},s)})})]}),(0,a.jsxs)(d.A,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Quick Actions"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)(n.Ay,{className:"bg-blue-600 hover:bg-blue-700 h-16 flex flex-col items-center justify-center",children:[(0,a.jsx)(l.cfS,{className:"mb-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Manage Users"})]}),(0,a.jsxs)(n.Ay,{className:"bg-green-600 hover:bg-green-700 h-16 flex flex-col items-center justify-center",children:[(0,a.jsx)(l.lZI,{className:"mb-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Review Withdrawals"})]}),(0,a.jsxs)(n.Ay,{className:"bg-purple-600 hover:bg-purple-700 h-16 flex flex-col items-center justify-center",children:[(0,a.jsx)(l.pcC,{className:"mb-1"}),(0,a.jsx)("span",{className:"text-sm",children:"KYC Reviews"})]}),(0,a.jsxs)(n.Ay,{className:"bg-orange-600 hover:bg-orange-700 h-16 flex flex-col items-center justify-center",children:[(0,a.jsx)(l.zd,{className:"mb-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Send Notification"})]})]})]})]})}):(0,a.jsx)(i.A,{title:"Dashboard",children:(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsx)("p",{className:"text-gray-400",children:"Failed to load dashboard data"})})})}},64198:(e,s,t)=>{"use strict";t.d(s,{CustomToaster:()=>n,P0:()=>c,oR:()=>l.Ay});var a=t(95155),r=t(68289),l=t(13568),i=t(10351);let c={success:e=>{l.Ay.success(e,{duration:4e3,position:"top-right",style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}})},error:e=>{l.Ay.error(e,{duration:5e3,position:"top-right",style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}})},warning:e=>{(0,l.Ay)(e,{duration:4e3,position:"top-right",icon:"⚠️",style:{background:"#92400E",color:"#FFFBEB",border:"1px solid #F59E0B"}})},info:e=>{(0,l.Ay)(e,{duration:4e3,position:"top-right",icon:"ℹ️",style:{background:"#1E3A8A",color:"#EFF6FF",border:"1px solid #3B82F6"}})},loading:e=>l.Ay.loading(e,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"}}),dismiss:e=>{l.Ay.dismiss(e)},promise:(e,s)=>l.Ay.promise(e,s,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"},success:{style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}})};function n(){return(0,a.jsx)(l.l$,{position:"top-right",reverseOrder:!1,gutter:8,containerClassName:"",containerStyle:{},toastOptions:{className:"",duration:4e3,style:{background:"#1F2937",color:"#F9FAFB",border:"1px solid #374151",borderRadius:"8px",fontSize:"14px",fontWeight:"500",padding:"12px 16px",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"},success:{iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}},children:e=>(0,a.jsx)(l.bv,{toast:e,children:s=>{let{icon:t,message:c}=s;return(0,a.jsxs)(r.P.div,{initial:{opacity:0,y:-50,scale:.3},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.3},transition:{type:"spring",stiffness:500,damping:30,duration:.3},className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:t}),(0,a.jsx)("div",{className:"flex-1",children:c}),"loading"!==e.type&&(0,a.jsx)("button",{onClick:()=>l.Ay.dismiss(e.id),className:"flex-shrink-0 p-1 rounded-full hover:bg-gray-600 transition-colors",children:(0,a.jsx)(i.yGN,{className:"w-4 h-4"})})]})}})})}},93915:(e,s,t)=>{"use strict";t.d(s,{A:()=>l,p:()=>r});var a=t(95155);let r=(0,t(12115).forwardRef)((e,s)=>{let{label:t,error:r,helperText:l,leftIcon:i,rightIcon:c,variant:n="default",className:d="",...o}=e,x="\n    w-full px-4 py-3 rounded-lg transition-all duration-200\n    focus:outline-none focus:ring-2 focus:ring-green-500/50\n    disabled:opacity-50 disabled:cursor-not-allowed\n    ".concat(i?"pl-12":"","\n    ").concat(c?"pr-12":"","\n  "),m="".concat(x," ").concat({default:"\n      bg-gray-800 border border-gray-700 text-white\n      placeholder-gray-400 hover:border-gray-600\n      focus:border-green-500\n    ",filled:"\n      bg-gray-700 border-0 text-white\n      placeholder-gray-400 hover:bg-gray-600\n    ",outline:"\n      bg-transparent border-2 border-gray-600 text-white\n      placeholder-gray-400 hover:border-gray-500\n      focus:border-green-500\n    "}[n]," ").concat(d," ").concat(r?"border-red-500 focus:border-red-500 focus:ring-red-500/50":"");return(0,a.jsxs)("div",{className:"w-full",children:[t&&(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:t}),(0,a.jsxs)("div",{className:"relative",children:[i&&(0,a.jsx)("div",{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400",children:i}),(0,a.jsx)("input",{ref:s,className:m,...o}),c&&(0,a.jsx)("div",{className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400",children:c})]}),r&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-400",children:r}),l&&!r&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:l})]})});r.displayName="Input";let l=r}},e=>{e.O(0,[844,9268,5236,6874,6766,3568,5221,3289,4630,6071,8441,5964,7358],()=>e(e.s=38955)),_N_E=e.O()}]);