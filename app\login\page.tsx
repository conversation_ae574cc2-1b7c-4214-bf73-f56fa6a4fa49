"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function LoginRedirectPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the correct login page
    router.push("/auth/login");
  }, [router]);

  return (
    <div className="min-h-screen bg-black flex items-center justify-center">
      <div className="text-center">
        <p className="text-green-400 text-lg">
          Redirecting to Login...
        </p>
      </div>
    </div>
  );
} 