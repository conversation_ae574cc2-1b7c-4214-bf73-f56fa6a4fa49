"use client";

import { motion } from 'framer-motion';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';
import {
    FiBell,
    FiCreditCard,
    FiHome,
    FiLogOut,
    FiMenu,
    FiSettings,
    FiTrendingUp,
    FiUser,
    FiX
} from 'react-icons/fi';
import { useAuth } from '../../hooks/use-auth';

const navigationItems = [
  { name: 'Dashboard', href: '/dashboard', icon: FiHome },
  { name: 'Savings', href: '/dashboard/savings-plans', icon: FiTrendingUp },
  { name: 'Payments', href: '/dashboard/payments', icon: FiCreditCard },
  { name: 'Profile', href: '/dashboard/profile', icon: FiUser },
];

export default function MobileNavigation() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();
  const { user, logout } = useAuth();

  const bottomTabItems = [
    { name: 'Home', href: '/dashboard', icon: FiHome },
    { name: 'Savings', href: '/dashboard/savings-plans', icon: FiTrendingUp },
    { name: 'Payments', href: '/dashboard/payments', icon: FiCreditCard },
    { name: 'Profile', href: '/dashboard/profile', icon: FiUser },
  ];

  const isActive = (href: string) => {
    if (!pathname) return false;
    if (href === '/dashboard') {
      return pathname === '/dashboard';
    }
    return pathname.startsWith(href);
  };

  const handleLogout = async () => {
    await logout();
    setIsMobileMenuOpen(false);
  };

  return (
    <>
      {/* Mobile Header - Clean without logo */}
      <div className="lg:hidden bg-gray-900 border-b border-gray-800 px-4 py-3 flex items-center justify-between">
        {/* Left side - Menu button */}
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="text-gray-400 hover:text-white transition-colors"
        >
          {isMobileMenuOpen ? <FiX size={24} /> : <FiMenu size={24} />}
        </button>

        {/* Center - Page title or empty */}
        <div className="flex-1 text-center">
          <h1 className="text-white font-medium text-lg">
            {pathname === '/dashboard' && 'Dashboard'}
            {pathname?.includes('/savings') && 'Savings'}
            {pathname?.includes('/payments') && 'Payments'}
            {pathname?.includes('/profile') && 'Profile'}
            {pathname?.includes('/settings') && 'Settings'}
          </h1>
        </div>

        {/* Right side - Notifications */}
        <Link
          href="/dashboard/notifications"
          className="text-gray-400 hover:text-white transition-colors relative"
        >
          <FiBell size={20} />
          {/* Notification badge */}
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
            3
          </span>
        </Link>
      </div>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="lg:hidden fixed inset-0 z-50 bg-gray-900"
        >
          {/* Header */}
          <div className="border-b border-gray-800 px-4 py-3 flex items-center justify-between">
            <h2 className="text-white font-semibold text-lg">Menu</h2>
            <button
              onClick={() => setIsMobileMenuOpen(false)}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <FiX size={24} />
            </button>
          </div>

          {/* User Info */}
          {user && (
            <div className="px-4 py-6 border-b border-gray-800">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-semibold text-lg">
                    {user.firstName?.charAt(0) || user.email?.charAt(0) || 'U'}
                  </span>
                </div>
                <div>
                  <p className="text-white font-medium">
                    {user.firstName} {user.lastName}
                  </p>
                  <p className="text-gray-400 text-sm">{user.email}</p>
                </div>
              </div>
            </div>
          )}

          {/* Navigation Items */}
          <div className="px-2 py-4 space-y-1">
            {navigationItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                onClick={() => setIsMobileMenuOpen(false)}
                className={`group flex items-center px-3 py-3 text-base font-medium rounded-lg transition-colors ${
                  isActive(item.href)
                    ? "bg-green-600 text-white"
                    : "text-gray-300 hover:bg-gray-800 hover:text-white"
                }`}
              >
                <item.icon
                  className={`mr-4 flex-shrink-0 h-6 w-6 ${
                    isActive(item.href)
                      ? "text-white"
                      : "text-gray-400 group-hover:text-white"
                  }`}
                />
                {item.name}
              </Link>
            ))}

            {/* Settings */}
            <Link
              href="/dashboard/settings"
              onClick={() => setIsMobileMenuOpen(false)}
              className="group flex items-center px-3 py-3 text-base font-medium rounded-lg transition-colors text-gray-300 hover:bg-gray-800 hover:text-white"
            >
              <FiSettings className="mr-4 flex-shrink-0 h-6 w-6 text-gray-400 group-hover:text-white" />
              Settings
            </Link>

            {/* Logout */}
            <button
              onClick={handleLogout}
              className="w-full group flex items-center px-3 py-3 text-base font-medium rounded-lg transition-colors text-red-400 hover:bg-red-600 hover:text-white"
            >
              <FiLogOut className="mr-4 flex-shrink-0 h-6 w-6" />
              Logout
            </button>
          </div>
        </motion.div>
      )}

      {/* Bottom Tab Bar */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-2 py-1 z-40">
        <div className="flex items-center justify-around">
          {bottomTabItems.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              className={`flex flex-col items-center py-2 px-3 rounded-lg transition-colors ${
                isActive(item.href)
                  ? "text-green-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              <item.icon
                className={`w-5 h-5 mb-1 ${
                  isActive(item.href) ? "text-green-600" : "text-gray-500"
                }`}
              />
              <span className={`text-xs font-medium ${
                isActive(item.href) ? "text-green-600" : "text-gray-500"
              }`}>
                {item.name}
              </span>
            </Link>
          ))}
        </div>
      </div>
    </>
  );
}
