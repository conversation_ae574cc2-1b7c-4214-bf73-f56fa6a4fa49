(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4859],{9598:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>u});var t=s(95155),a=s(66766),n=s(6874),o=s.n(n),i=s(35695),l=s(12115),c=s(10351),d=s(31246),m=s(64198);function x(){return(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-16 h-16 flex items-center justify-center",children:(0,t.jsx)(a.default,{src:"/images/logo-text.png",alt:"BetterInterest Logo",width:64,height:64,className:"w-16 h-16 object-contain",priority:!0})}),(0,t.jsx)("span",{className:"text-2xl font-bold bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent",children:"BetterInterest"})]})}function u(){let e=(0,i.useRouter)(),[r,s]=(0,l.useState)({email:"",password:""}),[a,n]=(0,l.useState)(!1),[u,p]=(0,l.useState)(!1),[h,g]=(0,l.useState)(""),b=e=>{let{name:r,value:t}=e.target;s(e=>({...e,[r]:t})),h&&g("")},y=async s=>{s.preventDefault(),p(!0),g("");try{let s=await fetch("".concat("http://localhost:8080","/api/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:r.email,password:r.password})}),t=await s.json();t.success?(localStorage.setItem("auth_token",t.data.token),localStorage.setItem("user_data",JSON.stringify({id:t.data.user.id,firstName:t.data.user.firstName,lastName:t.data.user.lastName,email:t.data.user.email,role:t.data.user.role,balance:t.data.user.balance})),m.P0.success("Login successful!"),"admin"===t.data.user.role?e.push("/admin/dashboard"):e.push("/dashboard")):g(t.message||"Invalid email or password")}catch(e){console.error("Login error:",e),g("Login failed. Please check your connection and try again.")}finally{p(!1)}};return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-green-900 to-black text-white",children:[(0,t.jsxs)("div",{className:"absolute inset-0 opacity-10",children:[(0,t.jsx)("div",{className:"absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_50%,rgba(34,197,94,0.1),transparent_50%)]"}),(0,t.jsx)("div",{className:"absolute top-1/4 left-1/4 w-64 h-64 bg-green-400/5 rounded-full blur-3xl"}),(0,t.jsx)("div",{className:"absolute bottom-1/4 right-1/4 w-48 h-48 bg-orange-400/5 rounded-full blur-3xl"})]}),(0,t.jsx)("nav",{className:"relative z-10 px-6 py-4",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto flex items-center justify-between",children:[(0,t.jsx)(o(),{href:"/",className:"flex items-center",children:(0,t.jsx)(x,{})}),(0,t.jsxs)(o(),{href:"/",className:"inline-flex items-center text-gray-400 hover:text-white transition-colors",children:[(0,t.jsx)(c.kRp,{className:"mr-2"}),"Back to Home"]})]})}),(0,t.jsx)("div",{className:"relative z-10 flex items-center justify-center min-h-[calc(100vh-80px)] p-8",children:(0,t.jsxs)("div",{className:"w-full max-w-md bg-gray-900/80 backdrop-blur-lg border border-orange-400 rounded-2xl p-8 shadow-2xl shadow-orange-500/20",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,t.jsx)(x,{})}),(0,t.jsx)("h1",{className:"text-2xl font-bold mb-2",children:"Welcome Back"}),(0,t.jsx)("p",{className:"text-gray-400",children:"Sign in to your savings account"})]}),h&&(0,t.jsx)("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-6",children:(0,t.jsx)("p",{className:"text-red-400 text-sm",children:h})}),(0,t.jsxs)("form",{onSubmit:y,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Email"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(c.pHD,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,t.jsx)("input",{type:"email",name:"email",value:r.email,onChange:b,className:"w-full pl-10 pr-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg focus:border-green-500 focus:outline-none transition-colors text-white placeholder-gray-400",placeholder:"<EMAIL>",required:!0})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(c.F5$,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,t.jsx)("input",{type:a?"text":"password",name:"password",value:r.password,onChange:b,className:"w-full pl-10 pr-12 py-3 bg-gray-900/50 border border-gray-700 rounded-lg focus:border-green-500 focus:outline-none transition-colors text-white placeholder-gray-400",placeholder:"••••••••",required:!0}),(0,t.jsx)("button",{type:"button",onClick:()=>n(!a),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors",children:a?(0,t.jsx)(c._NO,{}):(0,t.jsx)(c.Vap,{})})]})]}),(0,t.jsx)(d.jn,{type:"submit",disabled:u,className:"w-full",children:u?(0,t.jsxs)("div",{className:"flex items-center justify-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Signing in..."]}):"Sign In"})]}),(0,t.jsxs)("div",{className:"text-center mt-6 space-y-4",children:[(0,t.jsxs)("p",{className:"text-gray-400",children:["Don't have an account?"," ",(0,t.jsx)(o(),{href:"/auth/signup",className:"text-green-400 hover:text-green-300 font-medium transition-colors",children:"Sign up"})]}),(0,t.jsxs)(o(),{href:"/admin/login",className:"inline-flex items-center text-gray-400 hover:text-white transition-colors text-sm",children:[(0,t.jsx)(c.pcC,{className:"mr-2"}),"Admin Login"]})]}),(0,t.jsx)("div",{className:"text-center mt-4",children:(0,t.jsxs)(o(),{href:"/",className:"inline-flex items-center text-gray-400 hover:text-white transition-colors",children:[(0,t.jsx)(c.kRp,{className:"mr-2"}),"Back to Home"]})})]})})]})}},31246:(e,r,s)=>{"use strict";s.d(r,{jn:()=>i,rp:()=>l});var t=s(95155);s(12115);var a=s(68289);let n=()=>(0,t.jsxs)("svg",{width:34,height:34,viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,t.jsx)("circle",{cx:37,cy:37,r:"35.5",stroke:"currentColor",strokeWidth:3}),(0,t.jsx)("path",{d:"M25 35.5C24.1716 35.5 23.5 36.1716 23.5 37C23.5 37.8284 24.1716 38.5 25 38.5V35.5ZM49.0607 38.0607C49.6464 37.4749 49.6464 36.5251 49.0607 35.9393L39.5147 26.3934C38.9289 25.8076 37.9792 25.8076 37.3934 26.3934C36.8076 26.9792 36.8076 27.9289 37.3934 28.5147L45.8787 37L37.3934 45.4853C36.8076 46.0711 36.8076 47.0208 37.3934 47.6066C37.9792 48.1924 38.9289 48.1924 39.5147 47.6066L49.0607 38.0607ZM25 38.5L48 38.5V35.5L25 35.5V38.5Z",fill:"currentColor"})]});function o(e){let{children:r,onClick:s,href:o,variant:i="primary",size:l="md",disabled:c=!1,className:d="",type:m="button",icon:x}=e,u="\n    cursor-pointer font-bold font-sans transition-all duration-200 \n    border border-transparent flex items-center justify-center\n    rounded-full relative overflow-hidden group\n    ".concat(c?"opacity-50 cursor-not-allowed":"","\n  "),p="\n    ".concat(u,"\n    ").concat({primary:"\n      bg-gradient-to-r from-green-400 to-green-600 \n      hover:from-green-500 hover:to-green-700\n      text-white border-green-500\n    ",secondary:"\n      bg-gradient-to-r from-blue-400 to-blue-600 \n      hover:from-blue-500 hover:to-blue-700\n      text-white border-blue-500\n    ",outline:"\n      bg-transparent border-green-400 text-green-400\n      hover:bg-green-400 hover:text-black\n    "}[i],"\n    ").concat({sm:"px-4 py-2 text-sm",md:"px-6 py-3 text-base",lg:"px-8 py-4 text-lg"}[l],"\n    ").concat(d,"\n  "),h=()=>(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-400/20 to-green-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full blur-xl"}),(0,t.jsxs)("span",{className:"relative z-10 flex items-center",children:[(0,t.jsx)("span",{className:"text",children:r}),(x||"primary"===i)&&(0,t.jsx)(a.P.div,{className:"ml-2 text-current",whileHover:{x:5},transition:{type:"spring",stiffness:400,damping:10},children:x||(0,t.jsx)(n,{})})]})]});return o?(0,t.jsx)(a.P.a,{href:o,className:p,whileHover:{scale:1.02},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:10},children:(0,t.jsx)(h,{})}):(0,t.jsx)(a.P.button,{type:m,onClick:s,disabled:c,className:p,whileHover:{scale:c?1:1.02},whileTap:{scale:c?1:.95},transition:{type:"spring",stiffness:400,damping:10},children:(0,t.jsx)(h,{})})}function i(e){return(0,t.jsx)(o,{...e,variant:"primary"})}function l(e){return(0,t.jsx)(o,{...e,variant:"outline"})}},35695:(e,r,s)=>{"use strict";var t=s(18999);s.o(t,"usePathname")&&s.d(r,{usePathname:function(){return t.usePathname}}),s.o(t,"useRouter")&&s.d(r,{useRouter:function(){return t.useRouter}})},35971:(e,r,s)=>{Promise.resolve().then(s.bind(s,9598))},64198:(e,r,s)=>{"use strict";s.d(r,{CustomToaster:()=>l,P0:()=>i,oR:()=>n.Ay});var t=s(95155),a=s(68289),n=s(13568),o=s(10351);let i={success:e=>{n.Ay.success(e,{duration:4e3,position:"top-right",style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}})},error:e=>{n.Ay.error(e,{duration:5e3,position:"top-right",style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}})},warning:e=>{(0,n.Ay)(e,{duration:4e3,position:"top-right",icon:"⚠️",style:{background:"#92400E",color:"#FFFBEB",border:"1px solid #F59E0B"}})},info:e=>{(0,n.Ay)(e,{duration:4e3,position:"top-right",icon:"ℹ️",style:{background:"#1E3A8A",color:"#EFF6FF",border:"1px solid #3B82F6"}})},loading:e=>n.Ay.loading(e,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"}}),dismiss:e=>{n.Ay.dismiss(e)},promise:(e,r)=>n.Ay.promise(e,r,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"},success:{style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}})};function l(){return(0,t.jsx)(n.l$,{position:"top-right",reverseOrder:!1,gutter:8,containerClassName:"",containerStyle:{},toastOptions:{className:"",duration:4e3,style:{background:"#1F2937",color:"#F9FAFB",border:"1px solid #374151",borderRadius:"8px",fontSize:"14px",fontWeight:"500",padding:"12px 16px",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"},success:{iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}},children:e=>(0,t.jsx)(n.bv,{toast:e,children:r=>{let{icon:s,message:i}=r;return(0,t.jsxs)(a.P.div,{initial:{opacity:0,y:-50,scale:.3},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.3},transition:{type:"spring",stiffness:500,damping:30,duration:.3},className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:s}),(0,t.jsx)("div",{className:"flex-1",children:i}),"loading"!==e.type&&(0,t.jsx)("button",{onClick:()=>n.Ay.dismiss(e.id),className:"flex-shrink-0 p-1 rounded-full hover:bg-gray-600 transition-colors",children:(0,t.jsx)(o.yGN,{className:"w-4 h-4"})})]})}})})}}},e=>{e.O(0,[844,5236,6874,6766,3568,8441,5964,7358],()=>e(e.s=35971)),_N_E=e.O()}]);