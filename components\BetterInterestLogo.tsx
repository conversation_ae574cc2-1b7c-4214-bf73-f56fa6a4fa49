"use client";

import React from "react";
import BetterInterestIcon from "./BetterInterestIcon";

interface BetterInterestLogoProps {
  size?: number;
  showText?: boolean;
  className?: string;
}

export const BetterInterestLogo: React.FC<BetterInterestLogoProps> = ({
  size = 32,
  showText = true,
  className = "",
}) => {
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <BetterInterestIcon size={size} className="text-green-500" />
      {showText && (
        <span className="font-bold text-xl text-gray-800 dark:text-white">
          Better Interest
        </span>
      )}
    </div>
  );
};

export default BetterInterestLogo;
