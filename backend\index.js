require('dotenv').config();
const express = require('express');
const mongoose = require('mongoose');

// Global connection cache for serverless
let cached = global.mongoose;
if (!cached) {
  cached = global.mongoose = { conn: null, promise: null };
}

async function connectToDatabase() {
  if (cached.conn) return cached.conn;
  if (!cached.promise) {
    cached.promise = mongoose.connect(process.env.MONGO_URI, {
      // Add options if needed
    }).then((mongoose) => {
      return mongoose;
    });
  }
  cached.conn = await cached.promise;
  return cached.conn;
}
const authRoutes = require('./routes/userAuth');
const savingsRoutes = require('./routes/savingsRoutes');
const kycRoutes = require('./routes/kycRoutes');
const dojahKycRoutes = require('./routes/dojahKyc');
const transactionRoutes = require('./routes/transactionRoutes');
const profitRoutes = require('./routes/profitRoutes');
const accountsRoutes = require('./routes/accountsRoutes'); // Import accounts routes
const withdrawAccountsRoutes = require('./routes/withdrawAccountsRoutes'); // Import withdraw accounts routes
const depositRoutes = require('./routes/depositRoutes'); // Import deposit routes
const userInfoRoutes = require('./routes/userInfo');
const globalSettingsRoutes = require('./routes/globalSettingsRoutes'); // Import global settings routes
const withdrawRoutes = require('./routes/withdrawRoutes'); // Import userInfo routes
const cors = require('cors');
const notificationRoutes = require('./routes/notificationRoutes'); // Import notification routes

const statsRoutes = require('./routes/statsRoutes'); // Import stats routes
const targetSavingsRoutes = require('./routes/targetSavingsRoutes'); // Import target savings routes

const paystackRoutes = require('./routes/paystackRoutes');

const paystackDepositRoutes = require('./routes/paystackDepositRoutes');

const groupSavingsRoutes = require('./routes/groupSavingsRoutes'); // Import group savings routes
const rotationalGroupSavingsRoutes = require('./routes/rotationalGroupSavingsRoutes'); // Import rotational group savings routes

const paystackWebhookRoutes = require('./routes/paystackWebhook'); // Import Paystack webhook route
const errorHandler = require('./middleware/errorHandler'); // Import error handler middleware

// Import new admin routes
const adminAuditLogsRoutes = require('./routes/adminAuditLogs');
const adminSystemLogsRoutes = require('./routes/adminSystemLogs');
const adminImpersonationRoutes = require('./routes/adminImpersonation');
const adminProfileRoutes = require('./routes/adminProfile');
const adminSettingsHistoryRoutes = require('./routes/adminSettingsHistory');
const userBulkActionsRoutes = require('./routes/userBulkActions');
const adminInterestManagementRoutes = require('./routes/adminInterestManagement');
const adminFeesManagementRoutes = require('./routes/adminFeesManagement');
const withdrawalSettingsRoutes = require('./routes/settingsRoutes'); // Import withdrawal penalty settings routes

const app = express();

// Require the savings scheduler to enable automated savings plan deductions
require('./scheduler/savingsScheduler');


// CORS configuration (fix: allow array of origins)
const allowedOrigins = [
  'http://localhost:3000',
  'http://localhost:3001',
  'http://localhost:3002',
  process.env.FRONTEND_URL,
];
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps, curl, etc.)
    if (!origin) return callback(null, true);
    if (allowedOrigins.includes(origin)) {
      return callback(null, true);
    } else {
      return callback(new Error('Not allowed by CORS'));
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
};
console.log('CORS allowed origins:', allowedOrigins);

// Place CORS middleware at the very top before any other middleware/routes
app.use(cors(corsOptions));

app.use(express.json());
// Register admin dashboard route (ensure this is after express.json and before other routes)
const adminDashboardRoutes = require('./routes/adminDashboard');
app.use('/api/admin', adminDashboardRoutes);

// Register new admin routes
app.use('/api/admin/audit-logs', adminAuditLogsRoutes);
app.use('/api/admin/system-logs', adminSystemLogsRoutes);
app.use('/api/admin/impersonation', adminImpersonationRoutes);
app.use('/api/admin/profile', adminProfileRoutes);
app.use('/api/admin/settings/history', adminSettingsHistoryRoutes);
app.use('/api/users', userBulkActionsRoutes);
app.use('/api/admin/interest-management', adminInterestManagementRoutes);
app.use('/api/admin/fees-management', adminFeesManagementRoutes);

app.use('/api/auth', authRoutes);
app.use('/api/users', authRoutes); // <-- Add this line to expose /api/users/recent
app.use('/api/savings', savingsRoutes);
app.use('/api/kyc', kycRoutes);
app.use('/api/kyc', dojahKycRoutes); // /api/kyc/verify-nin
app.use('/api/transactions', transactionRoutes);
app.use('/api/profits', profitRoutes);
app.use('/api/accounts', accountsRoutes); // Add accounts routes
app.use('/api/withdraw-accounts', withdrawAccountsRoutes); // Add withdraw accounts routes
app.use('/api/deposits', depositRoutes); // Add deposit routes

// Mount global settings routes at /api/settings
app.use('/api/settings', globalSettingsRoutes);
app.use('/api/withdrawalset', withdrawalSettingsRoutes); // Add withdrawal penalty settings routes

app.use('/api/userinfo', userInfoRoutes); // Add userInfo routes
app.use('/api/user/profile', require('./routes/userProfile'));
app.use('/api/withdraw', withdrawRoutes); // Add withdraw routes

app.use('/api/paystack', paystackRoutes); // Paystack endpoints
app.use('/api/paystack/deposit', paystackDepositRoutes); // Paystack deposit endpoints
app.use('/api', paystackWebhookRoutes); // Paystack webhook endpoint
app.use('/api/notifications', notificationRoutes); // Add notification routes
app.use('/api', statsRoutes); // Add stats routes

app.use('/api/group-savings', groupSavingsRoutes); // Add group savings routes
app.use('/api/rotational-group-savings', rotationalGroupSavingsRoutes); // Add rotational group savings routes
app.use('/api/target-savings', targetSavingsRoutes); // Add target savings routes
app.use(errorHandler); // Use the error handler middleware


// For Vercel: connect on every request (but use cache)
if (process.env.VERCEL) {
  connectToDatabase().then(() => {
    console.log('MongoDB connected (Vercel serverless)');
  }).catch(err => console.log('MongoDB connection error:', err));
} else {
  // Check if MONGO_URI is set
  if (!process.env.MONGO_URI) {
    console.log('⚠️  MONGO_URI not set. Please create a .env file with your MongoDB connection string.');
    console.log('📝 Example .env file:');
    console.log('   MONGO_URI=mongodb://localhost:27017/betterinterest');
    console.log('   JWT_SECRET=your-secret-key');
    console.log('');
    console.log('🚀 Starting server without database connection for development...');
    app.listen(8080, () => console.log('Server running on http://localhost:8080 (No DB connection)'));
  } else {
    mongoose.connect(process.env.MONGO_URI)
      .then(() => {
        console.log('MongoDB connected');
        app.listen(8080, () => console.log('Server running on http://localhost:8080'));
      })
      .catch(err => {
        console.log('❌ MongoDB connection failed:', err.message);
        console.log('🚀 Starting server without database connection for development...');
        app.listen(8080, () => console.log('Server running on http://localhost:8080 (No DB connection)'));
      });
  }
}

// Export the app for Vercel serverless
module.exports = app;
