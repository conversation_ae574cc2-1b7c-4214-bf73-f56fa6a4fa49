(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1032],{8619:(e,t,r)=>{"use strict";r.d(t,{d:()=>l});var a=r(60098),n=r(12115),s=r(51508),i=r(82885);function l(e){let t=(0,i.M)(()=>(0,a.OQ)(e)),{isStatic:r}=(0,n.useContext)(s.Q);if(r){let[,r]=(0,n.useState)(e);(0,n.useEffect)(()=>t.on("change",r),[])}return t}},19958:(e,t,r)=>{"use strict";r.d(t,{YO:()=>u,uk:()=>d});var a=r(95155),n=r(12115),s=r(8619),i=r(37602),l=r(58829),o=r(68289),c=r(57740);function d(e){let{children:t,className:r="",intensity:d="medium",glowEffect:u=!0,hoverScale:x=!0,borderGradient:p=!1,elevation:m=2,onClick:g}=e,{theme:h}=(0,c.DP)(),y=(0,c.Yx)(h),b=(0,n.useRef)(null),f=(0,s.d)(0),v=(0,s.d)(0),j=(0,i.z)(f),w=(0,i.z)(v),N=(0,l.G)(w,[-.5,.5],"light"===d?[-5,5]:"medium"===d?[-10,10]:[-15,15]),A=(0,l.G)(j,[-.5,.5],"light"===d?[-5,5]:"medium"===d?[-10,10]:[-15,15]),F=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r={1:"light"===h?"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)":"0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.5)",2:"light"===h?"0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)":"0 3px 6px rgba(0, 0, 0, 0.4), 0 3px 6px rgba(0, 0, 0, 0.6)",3:"light"===h?"0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)":"0 10px 20px rgba(0, 0, 0, 0.5), 0 6px 6px rgba(0, 0, 0, 0.7)",4:"light"===h?"0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22)":"0 14px 28px rgba(0, 0, 0, 0.6), 0 10px 10px rgba(0, 0, 0, 0.8)",5:"light"===h?"0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22)":"0 19px 38px rgba(0, 0, 0, 0.7), 0 15px 12px rgba(0, 0, 0, 0.9)"};return r[t?Math.min(e+2,5):e]||r[2]},k="\n    relative rounded-xl overflow-hidden transition-all duration-300 group\n    ".concat(y.bg.card,"\n    ").concat(y.border.primary,"\n    ").concat(p?"border-2 border-transparent bg-gradient-to-r from-green-400/20 to-blue-400/20 p-[1px]":"border","\n    ").concat(r,"\n  "),T=(0,a.jsxs)(o.P.div,{ref:b,className:k,style:{rotateY:A,rotateX:N,transformStyle:"preserve-3d",boxShadow:F(m)},onMouseMove:e=>{if(!b.current)return;let t=b.current.getBoundingClientRect(),r=t.width,a=t.height,n=(e.clientX-t.left)/r-.5,s=(e.clientY-t.top)/a-.5;f.set(n),v.set(s)},onMouseLeave:()=>{f.set(0),v.set(0)},whileHover:x?{scale:1.02,boxShadow:F(m,!0),transition:{duration:.2,ease:"easeOut"}}:{},transition:{type:"spring",stiffness:300,damping:30},onClick:g,children:[u&&(0,a.jsx)(o.P.div,{className:"absolute inset-0 bg-gradient-to-r from-green-400/5 to-blue-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl blur-xl",initial:{scale:.8},whileHover:{scale:1.1},transition:{duration:.3}}),(0,a.jsx)(o.P.div,{className:"absolute inset-0 bg-green-400/10 opacity-0 group-hover:opacity-20 rounded-xl",initial:{scale:0},whileHover:{scale:1},transition:{duration:.4,ease:"easeOut"}}),(0,a.jsx)("div",{className:"relative z-10 ".concat(p?"".concat(y.bg.card," rounded-xl"):""),children:t}),(0,a.jsx)(o.P.div,{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ".concat("dark"===h?"via-white/5":"via-white/10"),initial:{x:"-100%"},whileHover:{x:"100%"},transition:{duration:.6,ease:"easeInOut"}})]});return(0,a.jsx)("div",{className:"group",children:T})}function u(e){let{title:t,value:r,subtitle:n,icon:s,color:i="green",className:l=""}=e,{theme:o}=(0,c.DP)(),u=(0,c.Yx)(o);return(0,a.jsx)(d,{className:"p-6 ".concat(l),glowEffect:!0,borderGradient:!0,children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium ".concat(u.text.secondary),children:t}),(0,a.jsx)("p",{className:"text-2xl font-bold ".concat(u.text.primary," mt-1"),children:r}),n&&(0,a.jsx)("p",{className:"text-xs ".concat(u.text.tertiary," mt-1"),children:n})]}),s&&(0,a.jsx)("div",{className:"w-12 h-12 rounded-lg bg-gradient-to-r ".concat({green:"from-green-400 to-green-600",blue:"from-blue-400 to-blue-600",purple:"from-purple-400 to-purple-600",yellow:"from-yellow-400 to-yellow-600"}[i]," flex items-center justify-center"),children:(0,a.jsx)(s,{className:"w-6 h-6 text-white"})})]})})}},20858:(e,t,r)=>{Promise.resolve().then(r.bind(r,85719))},31246:(e,t,r)=>{"use strict";r.d(t,{jn:()=>l,rp:()=>o});var a=r(95155);r(12115);var n=r(68289);let s=()=>(0,a.jsxs)("svg",{width:34,height:34,viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("circle",{cx:37,cy:37,r:"35.5",stroke:"currentColor",strokeWidth:3}),(0,a.jsx)("path",{d:"M25 35.5C24.1716 35.5 23.5 36.1716 23.5 37C23.5 37.8284 24.1716 38.5 25 38.5V35.5ZM49.0607 38.0607C49.6464 37.4749 49.6464 36.5251 49.0607 35.9393L39.5147 26.3934C38.9289 25.8076 37.9792 25.8076 37.3934 26.3934C36.8076 26.9792 36.8076 27.9289 37.3934 28.5147L45.8787 37L37.3934 45.4853C36.8076 46.0711 36.8076 47.0208 37.3934 47.6066C37.9792 48.1924 38.9289 48.1924 39.5147 47.6066L49.0607 38.0607ZM25 38.5L48 38.5V35.5L25 35.5V38.5Z",fill:"currentColor"})]});function i(e){let{children:t,onClick:r,href:i,variant:l="primary",size:o="md",disabled:c=!1,className:d="",type:u="button",icon:x}=e,p="\n    cursor-pointer font-bold font-sans transition-all duration-200 \n    border border-transparent flex items-center justify-center\n    rounded-full relative overflow-hidden group\n    ".concat(c?"opacity-50 cursor-not-allowed":"","\n  "),m="\n    ".concat(p,"\n    ").concat({primary:"\n      bg-gradient-to-r from-green-400 to-green-600 \n      hover:from-green-500 hover:to-green-700\n      text-white border-green-500\n    ",secondary:"\n      bg-gradient-to-r from-blue-400 to-blue-600 \n      hover:from-blue-500 hover:to-blue-700\n      text-white border-blue-500\n    ",outline:"\n      bg-transparent border-green-400 text-green-400\n      hover:bg-green-400 hover:text-black\n    "}[l],"\n    ").concat({sm:"px-4 py-2 text-sm",md:"px-6 py-3 text-base",lg:"px-8 py-4 text-lg"}[o],"\n    ").concat(d,"\n  "),g=()=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-400/20 to-green-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full blur-xl"}),(0,a.jsxs)("span",{className:"relative z-10 flex items-center",children:[(0,a.jsx)("span",{className:"text",children:t}),(x||"primary"===l)&&(0,a.jsx)(n.P.div,{className:"ml-2 text-current",whileHover:{x:5},transition:{type:"spring",stiffness:400,damping:10},children:x||(0,a.jsx)(s,{})})]})]});return i?(0,a.jsx)(n.P.a,{href:i,className:m,whileHover:{scale:1.02},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:10},children:(0,a.jsx)(g,{})}):(0,a.jsx)(n.P.button,{type:u,onClick:r,disabled:c,className:m,whileHover:{scale:c?1:1.02},whileTap:{scale:c?1:.95},transition:{type:"spring",stiffness:400,damping:10},children:(0,a.jsx)(g,{})})}function l(e){return(0,a.jsx)(i,{...e,variant:"primary"})}function o(e){return(0,a.jsx)(i,{...e,variant:"outline"})}},35695:(e,t,r)=>{"use strict";var a=r(18999);r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}})},37602:(e,t,r)=>{"use strict";r.d(t,{z:()=>u});var a=r(64803),n=r(30532),s=r(69515);function i(e){return"number"==typeof e?e:parseFloat(e)}var l=r(12115),o=r(51508),c=r(8619),d=r(58829);function u(e,t={}){let{isStatic:r}=(0,l.useContext)(o.Q),x=()=>(0,a.S)(e)?e.get():e;if(r)return(0,d.G)(x);let p=(0,c.d)(x());return(0,l.useInsertionEffect)(()=>(function(e,t,r){let l,o,c=e.get(),d=null,u=c,x="string"==typeof c?c.replace(/[\d.-]/g,""):void 0,p=()=>{d&&(d.stop(),d=null)},m=()=>{p(),d=new n.s({keyframes:[i(e.get()),i(u)],velocity:e.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...r,onUpdate:l})};return e.attach((t,r)=>(u=t,l=e=>{var t,a;return r((t=e,(a=x)?t+a:t))},s.Gt.postRender(m),e.get()),p),(0,a.S)(t)&&(o=t.on("change",t=>{var r,a;return e.set((r=t,(a=x)?r+a:r))}),e.on("destroy",o)),o})(p,e,t),[p,JSON.stringify(t)]),p}},57740:(e,t,r)=>{"use strict";r.d(t,{DP:()=>l,ThemeProvider:()=>i,Yx:()=>c});var a=r(95155),n=r(12115);let s=(0,n.createContext)(void 0);function i(e){let{children:t}=e,[r,i]=(0,n.useState)("dark"),[l,o]=(0,n.useState)(!1);return((0,n.useEffect)(()=>{o(!0);let e=localStorage.getItem("theme");e?i(e):i("dark")},[]),(0,n.useEffect)(()=>{l&&(document.documentElement.classList.remove("light","dark"),document.documentElement.classList.add(r),localStorage.setItem("theme",r))},[r,l]),l)?(0,a.jsx)(s.Provider,{value:{theme:r,toggleTheme:()=>{i(e=>"light"===e?"dark":"light")},setTheme:e=>{i(e)}},children:t}):(0,a.jsx)("div",{className:"min-h-screen bg-black",children:t})}function l(){let e=(0,n.useContext)(s);return void 0===e?(console.warn("useTheme called outside of ThemeProvider, using fallback values"),{theme:"dark",toggleTheme:()=>{},setTheme:()=>{}}):e}let o={light:{bg:{primary:"bg-white",secondary:"bg-gray-50",tertiary:"bg-gray-100",card:"bg-white",sidebar:"bg-white",header:"bg-white/80"},text:{primary:"text-gray-900",secondary:"text-gray-600",tertiary:"text-gray-500",inverse:"text-white"},border:{primary:"border-gray-200",secondary:"border-gray-300",accent:"border-green-200"},button:{primary:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white hover:shadow-green-500/25",secondary:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white hover:shadow-blue-500/25",outline:"bg-transparent border-2 border-green-500 text-green-600 hover:bg-green-500 hover:text-white"},accent:{green:"text-green-600",blue:"text-blue-600",purple:"text-purple-600",yellow:"text-yellow-600"}},dark:{bg:{primary:"bg-black",secondary:"bg-gray-900",tertiary:"bg-gray-800",card:"bg-gray-900",sidebar:"bg-gray-900",header:"bg-gray-900/80"},text:{primary:"text-white",secondary:"text-gray-300",tertiary:"text-gray-400",inverse:"text-black"},border:{primary:"border-gray-800",secondary:"border-gray-700",accent:"border-green-500/30"},button:{primary:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white hover:shadow-green-500/25",secondary:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white hover:shadow-blue-500/25",outline:"bg-transparent border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-black"},accent:{green:"text-green-400",blue:"text-blue-400",purple:"text-purple-400",yellow:"text-yellow-400"}}};function c(e){return o[e]}},58829:(e,t,r)=>{"use strict";r.d(t,{G:()=>d});var a=r(6775),n=r(82885),s=r(69515),i=r(97494),l=r(8619);function o(e,t){let r=(0,l.d)(t()),a=()=>r.set(t());return a(),(0,i.E)(()=>{let t=()=>s.Gt.preRender(a,!1,!0),r=e.map(e=>e.on("change",t));return()=>{r.forEach(e=>e()),(0,s.WG)(a)}}),r}var c=r(60098);function d(e,t,r,n){if("function"==typeof e){c.bt.current=[],e();let t=o(c.bt.current,e);return c.bt.current=void 0,t}let s="function"==typeof t?t:function(...e){let t=!Array.isArray(e[0]),r=t?0:-1,n=e[0+r],s=e[1+r],i=e[2+r],l=e[3+r],o=(0,a.G)(s,i,l);return t?o(n):o}(t,r,n);return Array.isArray(e)?u(e,s):u([e],([e])=>s(e))}function u(e,t){let r=(0,n.M)(()=>[]);return o(e,()=>{r.length=0;let a=e.length;for(let t=0;t<a;t++)r[t]=e[t].get();return t(r)})}},64198:(e,t,r)=>{"use strict";r.d(t,{CustomToaster:()=>o,P0:()=>l,oR:()=>s.Ay});var a=r(95155),n=r(68289),s=r(13568),i=r(10351);let l={success:e=>{s.Ay.success(e,{duration:4e3,position:"top-right",style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}})},error:e=>{s.Ay.error(e,{duration:5e3,position:"top-right",style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}})},warning:e=>{(0,s.Ay)(e,{duration:4e3,position:"top-right",icon:"⚠️",style:{background:"#92400E",color:"#FFFBEB",border:"1px solid #F59E0B"}})},info:e=>{(0,s.Ay)(e,{duration:4e3,position:"top-right",icon:"ℹ️",style:{background:"#1E3A8A",color:"#EFF6FF",border:"1px solid #3B82F6"}})},loading:e=>s.Ay.loading(e,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"}}),dismiss:e=>{s.Ay.dismiss(e)},promise:(e,t)=>s.Ay.promise(e,t,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"},success:{style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}})};function o(){return(0,a.jsx)(s.l$,{position:"top-right",reverseOrder:!1,gutter:8,containerClassName:"",containerStyle:{},toastOptions:{className:"",duration:4e3,style:{background:"#1F2937",color:"#F9FAFB",border:"1px solid #374151",borderRadius:"8px",fontSize:"14px",fontWeight:"500",padding:"12px 16px",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"},success:{iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}},children:e=>(0,a.jsx)(s.bv,{toast:e,children:t=>{let{icon:r,message:l}=t;return(0,a.jsxs)(n.P.div,{initial:{opacity:0,y:-50,scale:.3},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.3},transition:{type:"spring",stiffness:500,damping:30,duration:.3},className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:r}),(0,a.jsx)("div",{className:"flex-1",children:l}),"loading"!==e.type&&(0,a.jsx)("button",{onClick:()=>s.Ay.dismiss(e.id),className:"flex-shrink-0 p-1 rounded-full hover:bg-gray-600 transition-colors",children:(0,a.jsx)(i.yGN,{className:"w-4 h-4"})})]})}})})}},85719:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var a=r(95155),n=r(68289),s=r(12115),i=r(10351),l=r(26071),o=r(31246),c=r(19958),d=r(64198);let u=[{id:"1",name:"Early Withdrawal Penalty",type:"early_withdrawal",calculationType:"percentage",amount:5,minAmount:1e3,maxAmount:5e4,description:"Penalty for withdrawing before maturity date",isActive:!0,applicableAfter:0,createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"},{id:"2",name:"Late Payment Fee",type:"late_payment",calculationType:"fixed",amount:2500,description:"Fee for late monthly contributions",isActive:!0,applicableAfter:7,createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"},{id:"3",name:"Account Maintenance Fee",type:"account_maintenance",calculationType:"fixed",amount:500,description:"Monthly account maintenance fee",isActive:!0,createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"},{id:"4",name:"Transaction Fee",type:"transaction_fee",calculationType:"percentage",amount:1.5,minAmount:100,maxAmount:1e3,description:"Fee for external transfers",isActive:!0,createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"}];function x(){let[e,t]=(0,s.useState)(u),[r,x]=(0,s.useState)(null),[p,m]=(0,s.useState)(!1),[g,h]=(0,s.useState)(!1),[y,b]=(0,s.useState)({name:"",type:"penalty",calculationType:"fixed",amount:0,minAmount:0,maxAmount:0,description:"",isActive:!0,applicableAfter:0}),f=async()=>{m(!0);try{if(await new Promise(e=>setTimeout(e,1e3)),r)t(e=>e.map(e=>e.id===r.id?{...e,...y,updatedAt:new Date().toISOString()}:e)),d.P0.success("Penalty/Fee updated successfully"),x(null);else{let e={id:Date.now().toString(),...y,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};t(t=>[...t,e]),d.P0.success("Penalty/Fee added successfully"),h(!1)}b({name:"",type:"penalty",calculationType:"fixed",amount:0,minAmount:0,maxAmount:0,description:"",isActive:!0,applicableAfter:0})}catch(e){d.P0.error("Failed to save penalty/fee")}finally{m(!1)}},v=async e=>{if(confirm("Are you sure you want to delete this penalty/fee?")){m(!0);try{await new Promise(e=>setTimeout(e,1e3)),t(t=>t.filter(t=>t.id!==e)),d.P0.success("Penalty/Fee deleted successfully")}catch(e){d.P0.error("Failed to delete penalty/fee")}finally{m(!1)}}},j=async e=>{m(!0);try{await new Promise(e=>setTimeout(e,500)),t(t=>t.map(t=>t.id===e?{...t,isActive:!t.isActive,updatedAt:new Date().toISOString()}:t)),d.P0.success("Status updated successfully")}catch(e){d.P0.error("Failed to update status")}finally{m(!1)}},w=(e,t)=>"percentage"===t?"".concat(e,"%"):new Intl.NumberFormat("en-NG",{style:"currency",currency:"NGN"}).format(e),N=e.filter(e=>e.isActive).length,A=e.filter(e=>"early_withdrawal"===e.type||"late_payment"===e.type||"penalty"===e.type).length,F=e.filter(e=>"account_maintenance"===e.type||"transaction_fee"===e.type).length;return(0,a.jsx)(l.A,{title:"Penalties & Fees",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white",children:"Penalties & Fees Management"}),(0,a.jsx)("p",{className:"text-gray-400 mt-2",children:"Configure penalties and fees for various scenarios"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)(o.rp,{onClick:()=>h(!g),children:[(0,a.jsx)(i.GGD,{className:"mr-2"}),"Add Penalty/Fee"]}),(0,a.jsxs)(o.jn,{children:[(0,a.jsx)(i.jTZ,{className:"mr-2"}),"Refresh"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsx)(c.uk,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Active Items"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:N})]}),(0,a.jsx)(i.eHT,{className:"text-green-500 text-3xl"})]})}),(0,a.jsx)(c.uk,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Penalties"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:A})]}),(0,a.jsx)(i.yGN,{className:"text-red-500 text-3xl"})]})}),(0,a.jsx)(c.uk,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Fees"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:F})]}),(0,a.jsx)(i.z8N,{className:"text-blue-500 text-3xl"})]})})]}),g&&(0,a.jsxs)(c.uk,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:r?"Edit Penalty/Fee":"Add New Penalty/Fee"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Name"}),(0,a.jsx)("input",{type:"text",value:y.name,onChange:e=>b({...y,name:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white",placeholder:"e.g., Early Withdrawal Penalty"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Type"}),(0,a.jsxs)("select",{value:y.type,onChange:e=>b({...y,type:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white",children:[(0,a.jsx)("option",{value:"early_withdrawal",children:"Early Withdrawal"}),(0,a.jsx)("option",{value:"late_payment",children:"Late Payment"}),(0,a.jsx)("option",{value:"account_maintenance",children:"Account Maintenance"}),(0,a.jsx)("option",{value:"transaction_fee",children:"Transaction Fee"}),(0,a.jsx)("option",{value:"penalty",children:"General Penalty"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Calculation Type"}),(0,a.jsxs)("select",{value:y.calculationType,onChange:e=>b({...y,calculationType:e.target.value}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white",children:[(0,a.jsx)("option",{value:"fixed",children:"Fixed Amount"}),(0,a.jsx)("option",{value:"percentage",children:"Percentage"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Amount ","percentage"===y.calculationType?"(%)":"(₦)"]}),(0,a.jsx)("input",{type:"number",step:"percentage"===y.calculationType?"0.1":"1",value:y.amount,onChange:e=>b({...y,amount:parseFloat(e.target.value)}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white"})]}),"percentage"===y.calculationType&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Minimum Amount (₦)"}),(0,a.jsx)("input",{type:"number",value:y.minAmount,onChange:e=>b({...y,minAmount:parseInt(e.target.value)}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Maximum Amount (₦)"}),(0,a.jsx)("input",{type:"number",value:y.maxAmount,onChange:e=>b({...y,maxAmount:parseInt(e.target.value)}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Applicable After (days)"}),(0,a.jsx)("input",{type:"number",value:y.applicableAfter,onChange:e=>b({...y,applicableAfter:parseInt(e.target.value)}),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white",placeholder:"0 for immediate"})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Description"}),(0,a.jsx)("textarea",{value:y.description,onChange:e=>b({...y,description:e.target.value}),rows:3,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:border-green-500 focus:outline-none text-white",placeholder:"Describe when this penalty/fee applies"})]})]}),(0,a.jsxs)("div",{className:"flex items-center mt-4",children:[(0,a.jsx)("input",{type:"checkbox",id:"isActive",checked:y.isActive,onChange:e=>b({...y,isActive:e.target.checked}),className:"mr-2"}),(0,a.jsx)("label",{htmlFor:"isActive",className:"text-sm text-gray-300",children:"Active"})]}),(0,a.jsxs)("div",{className:"flex space-x-2 mt-6",children:[(0,a.jsxs)(o.jn,{onClick:f,disabled:p||!y.name||!y.description,children:[(0,a.jsx)(i.Bc_,{className:"mr-2"}),r?"Update":"Save"," Item"]}),(0,a.jsx)(o.rp,{onClick:()=>{h(!1),x(null),b({name:"",type:"penalty",calculationType:"fixed",amount:0,minAmount:0,maxAmount:0,description:"",isActive:!0,applicableAfter:0})},children:"Cancel"})]})]}),(0,a.jsx)(c.uk,{className:"bg-gray-800 border-gray-700 overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-700",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Name & Type"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Amount"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Applicable After"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Updated"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-700",children:e.map(e=>(0,a.jsxs)(n.P.tr,{initial:{opacity:0},animate:{opacity:1},className:"hover:bg-gray-700/50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(e=>{switch(e){case"early_withdrawal":return(0,a.jsx)(i.Ohp,{className:"text-orange-400"});case"late_payment":return(0,a.jsx)(i.eHT,{className:"text-red-400"});case"account_maintenance":return(0,a.jsx)(i.z8N,{className:"text-blue-400"});case"transaction_fee":return(0,a.jsx)(i.fTJ,{className:"text-green-400"});default:return(0,a.jsx)(i.yGN,{className:"text-gray-400"})}})(e.type),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-white",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-400 capitalize",children:e.type.replace("_"," ")})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-white",children:w(e.amount,e.calculationType)}),"percentage"===e.calculationType&&e.minAmount&&e.maxAmount&&(0,a.jsxs)("div",{className:"text-sm text-gray-400",children:["Min: ",w(e.minAmount,"fixed")," - Max: ",w(e.maxAmount,"fixed")]})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-white",children:0===e.applicableAfter?"Immediate":"".concat(e.applicableAfter," days")})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("button",{onClick:()=>j(e.id),disabled:p,className:"px-2 py-1 text-xs rounded-full ".concat(e.isActive?"bg-green-600 text-white":"bg-gray-600 text-gray-300"),children:e.isActive?"Active":"Inactive"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-400",children:new Date(e.updatedAt).toLocaleDateString("en-NG",{year:"numeric",month:"short",day:"numeric"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>{x(e),b({name:e.name,type:e.type,calculationType:e.calculationType,amount:e.amount,minAmount:e.minAmount||0,maxAmount:e.maxAmount||0,description:e.description,isActive:e.isActive,applicableAfter:e.applicableAfter||0}),h(!0)},className:"text-blue-400 hover:text-blue-300",children:(0,a.jsx)(i.SG1,{})}),(0,a.jsx)("button",{onClick:()=>v(e.id),disabled:p,className:"text-red-400 hover:text-red-300 disabled:opacity-50",children:(0,a.jsx)(i.IXo,{})})]})})]},e.id))})]})})})]})})}},93915:(e,t,r)=>{"use strict";r.d(t,{A:()=>s,p:()=>n});var a=r(95155);let n=(0,r(12115).forwardRef)((e,t)=>{let{label:r,error:n,helperText:s,leftIcon:i,rightIcon:l,variant:o="default",className:c="",...d}=e,u="\n    w-full px-4 py-3 rounded-lg transition-all duration-200\n    focus:outline-none focus:ring-2 focus:ring-green-500/50\n    disabled:opacity-50 disabled:cursor-not-allowed\n    ".concat(i?"pl-12":"","\n    ").concat(l?"pr-12":"","\n  "),x="".concat(u," ").concat({default:"\n      bg-gray-800 border border-gray-700 text-white\n      placeholder-gray-400 hover:border-gray-600\n      focus:border-green-500\n    ",filled:"\n      bg-gray-700 border-0 text-white\n      placeholder-gray-400 hover:bg-gray-600\n    ",outline:"\n      bg-transparent border-2 border-gray-600 text-white\n      placeholder-gray-400 hover:border-gray-500\n      focus:border-green-500\n    "}[o]," ").concat(c," ").concat(n?"border-red-500 focus:border-red-500 focus:ring-red-500/50":"");return(0,a.jsxs)("div",{className:"w-full",children:[r&&(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:r}),(0,a.jsxs)("div",{className:"relative",children:[i&&(0,a.jsx)("div",{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400",children:i}),(0,a.jsx)("input",{ref:t,className:x,...d}),l&&(0,a.jsx)("div",{className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400",children:l})]}),n&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-400",children:n}),s&&!n&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:s})]})});n.displayName="Input";let s=n}},e=>{e.O(0,[844,9268,5236,6874,6766,3568,5221,3289,4630,6071,8441,5964,7358],()=>e(e.s=20858)),_N_E=e.O()}]);