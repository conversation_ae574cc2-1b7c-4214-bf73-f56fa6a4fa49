(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2164],{57740:(e,r,t)=>{"use strict";t.d(r,{DP:()=>o,ThemeProvider:()=>l,Yx:()=>c});var n=t(95155),s=t(12115);let a=(0,s.createContext)(void 0);function l(e){let{children:r}=e,[t,l]=(0,s.useState)("dark"),[o,i]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{i(!0);let e=localStorage.getItem("theme");e?l(e):l("dark")},[]),(0,s.useEffect)(()=>{o&&(document.documentElement.classList.remove("light","dark"),document.documentElement.classList.add(t),localStorage.setItem("theme",t))},[t,o]),o)?(0,n.jsx)(a.Provider,{value:{theme:t,toggleTheme:()=>{l(e=>"light"===e?"dark":"light")},setTheme:e=>{l(e)}},children:r}):(0,n.jsx)("div",{className:"min-h-screen bg-black",children:r})}function o(){let e=(0,s.useContext)(a);return void 0===e?(console.warn("useTheme called outside of ThemeProvider, using fallback values"),{theme:"dark",toggleTheme:()=>{},setTheme:()=>{}}):e}let i={light:{bg:{primary:"bg-white",secondary:"bg-gray-50",tertiary:"bg-gray-100",card:"bg-white",sidebar:"bg-white",header:"bg-white/80"},text:{primary:"text-gray-900",secondary:"text-gray-600",tertiary:"text-gray-500",inverse:"text-white"},border:{primary:"border-gray-200",secondary:"border-gray-300",accent:"border-green-200"},button:{primary:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white hover:shadow-green-500/25",secondary:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white hover:shadow-blue-500/25",outline:"bg-transparent border-2 border-green-500 text-green-600 hover:bg-green-500 hover:text-white"},accent:{green:"text-green-600",blue:"text-blue-600",purple:"text-purple-600",yellow:"text-yellow-600"}},dark:{bg:{primary:"bg-black",secondary:"bg-gray-900",tertiary:"bg-gray-800",card:"bg-gray-900",sidebar:"bg-gray-900",header:"bg-gray-900/80"},text:{primary:"text-white",secondary:"text-gray-300",tertiary:"text-gray-400",inverse:"text-black"},border:{primary:"border-gray-800",secondary:"border-gray-700",accent:"border-green-500/30"},button:{primary:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white hover:shadow-green-500/25",secondary:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white hover:shadow-blue-500/25",outline:"bg-transparent border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-black"},accent:{green:"text-green-400",blue:"text-blue-400",purple:"text-purple-400",yellow:"text-yellow-400"}}};function c(e){return i[e]}},61895:(e,r,t)=>{Promise.resolve().then(t.bind(t,93553))},74436:(e,r,t)=>{"use strict";t.d(r,{k5:()=>d});var n=t(12115),s={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},a=n.createContext&&n.createContext(s),l=["attr","size","title"];function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}function i(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function c(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?i(Object(t),!0).forEach(function(r){var n,s,a;n=e,s=r,a=t[r],(s=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(s))in n?Object.defineProperty(n,s,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[s]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function d(e){return r=>n.createElement(g,o({attr:c({},e.attr)},r),function e(r){return r&&r.map((r,t)=>n.createElement(r.tag,c({key:t},r.attr),e(r.child)))}(e.child))}function g(e){var r=r=>{var t,{attr:s,size:a,title:i}=e,d=function(e,r){if(null==e)return{};var t,n,s=function(e,r){if(null==e)return{};var t={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(r.indexOf(n)>=0)continue;t[n]=e[n]}return t}(e,r);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)t=a[n],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(s[t]=e[t])}return s}(e,l),g=a||r.size||"1em";return r.className&&(t=r.className),e.className&&(t=(t?t+" ":"")+e.className),n.createElement("svg",o({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,s,d,{className:t,style:c(c({color:e.color||r.color},r.style),e.style),height:g,width:g,xmlns:"http://www.w3.org/2000/svg"}),i&&n.createElement("title",null,i),e.children)};return void 0!==a?n.createElement(a.Consumer,null,e=>r(e)):r(s)}},93553:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var n=t(95155),s=t(12115),a=t(57740),l=t(10351);function o(){let[e,r]=(0,s.useState)(!1),{theme:t,toggleTheme:o}=(0,a.DP)();return(0,n.jsxs)("div",{className:"min-h-screen flex ".concat("light"===t?"bg-gray-50 text-gray-900":"bg-gray-900 text-white"),children:[e&&(0,n.jsx)("div",{className:"fixed inset-0 z-40 bg-black/50 lg:hidden",onClick:()=>r(!1)}),(0,n.jsxs)("div",{className:"\n        ".concat(e?"translate-x-0":"-translate-x-full"," \n        lg:translate-x-0 \n        fixed lg:relative \n        inset-y-0 left-0 \n        z-50 lg:z-10\n        w-80 \n        transition-transform duration-300 ease-in-out\n        ").concat("light"===t?"bg-white border-gray-200":"bg-gray-800 border-gray-700","\n        border-r flex flex-col\n      "),children:[(0,n.jsxs)("div",{className:"flex items-center justify-between h-16 px-6 border-b border-gray-700",children:[(0,n.jsx)("h1",{className:"text-xl font-bold",children:"Simple Dashboard"}),(0,n.jsx)("button",{onClick:()=>r(!1),className:"lg:hidden p-2 rounded hover:bg-gray-700",children:(0,n.jsx)(l.yGN,{className:"w-5 h-5"})})]}),(0,n.jsxs)("nav",{className:"flex-1 p-6 space-y-2",children:[(0,n.jsxs)("a",{href:"#",className:"flex items-center space-x-3 p-3 rounded hover:bg-gray-700",children:[(0,n.jsx)(l.V5Y,{className:"w-5 h-5"}),(0,n.jsx)("span",{children:"Home"})]}),(0,n.jsxs)("a",{href:"#",className:"flex items-center space-x-3 p-3 rounded hover:bg-gray-700",children:[(0,n.jsx)(l.VSk,{className:"w-5 h-5"}),(0,n.jsx)("span",{children:"Settings"})]})]}),(0,n.jsx)("div",{className:"p-6 border-t border-gray-700",children:(0,n.jsxs)("button",{onClick:o,className:"w-full p-3 rounded bg-green-600 hover:bg-green-700 text-white",children:["Toggle Theme (",t,")"]})})]}),(0,n.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,n.jsx)("header",{className:"h-16 px-6 flex items-center justify-between border-b ".concat("light"===t?"bg-white border-gray-200":"bg-gray-800 border-gray-700"),children:(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)("button",{onClick:()=>r(!0),className:"lg:hidden p-2 rounded hover:bg-gray-700",children:(0,n.jsx)(l.ND1,{className:"w-5 h-5"})}),(0,n.jsx)("h1",{className:"text-xl font-semibold",children:"Dashboard"})]})}),(0,n.jsxs)("main",{className:"flex-1 p-6",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,n.jsxs)("div",{className:"p-6 rounded-lg ".concat("light"===t?"bg-white shadow":"bg-gray-800"),children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Card 1"}),(0,n.jsx)("p",{children:"This is a test card to verify the layout is working."})]}),(0,n.jsxs)("div",{className:"p-6 rounded-lg ".concat("light"===t?"bg-white shadow":"bg-gray-800"),children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Card 2"}),(0,n.jsx)("p",{children:"The sidebar should be visible on large screens and hidden on mobile."})]}),(0,n.jsxs)("div",{className:"p-6 rounded-lg ".concat("light"===t?"bg-white shadow":"bg-gray-800"),children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Card 3"}),(0,n.jsx)("p",{children:"Theme switching should work properly."})]})]}),(0,n.jsxs)("div",{className:"mt-8",children:[(0,n.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Responsive Test"}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("div",{className:"block lg:hidden p-4 bg-red-500 text-white rounded",children:"Mobile Only: This shows only on small screens"}),(0,n.jsx)("div",{className:"hidden lg:block p-4 bg-green-500 text-white rounded",children:"Desktop Only: This shows only on large screens (lg and up)"}),(0,n.jsx)("div",{className:"p-4 bg-blue-500 text-white rounded",children:"Always Visible: This shows on all screen sizes"})]})]})]})]})]})}}},e=>{e.O(0,[844,8441,5964,7358],()=>e(e.s=61895)),_N_E=e.O()}]);