(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2116],{585:(e,t,a)=>{"use strict";a.d(t,{L$:()=>o});var r=a(95155);a(12115);var s=a(68289);let l=[{left:10,top:20},{left:25,top:15},{left:40,top:30},{left:55,top:10},{left:70,top:25},{left:85,top:35},{left:15,top:45},{left:30,top:55},{left:45,top:40},{left:60,top:50},{left:75,top:60},{left:90,top:45},{left:5,top:70},{left:20,top:80},{left:35,top:65},{left:50,top:75},{left:65,top:85},{left:80,top:70},{left:95,top:80},{left:10,top:90},{left:25,top:5},{left:40,top:85},{left:55,top:95},{left:70,top:5},{left:85,top:15},{left:15,top:25},{left:30,top:35},{left:45,top:45},{left:60,top:55},{left:75,top:65},{left:90,top:75},{left:5,top:85},{left:20,top:95},{left:35,top:5},{left:50,top:15},{left:65,top:25},{left:80,top:35},{left:95,top:45}];function o(e){let{variant:t="default",className:a=""}=e,o={default:{gradient:"bg-gradient-to-br from-gray-900 via-green-900 to-black",particles:30,colors:["bg-green-400","bg-orange-400","bg-yellow-400"]},auth:{gradient:"bg-gradient-to-br from-gray-900 via-black to-gray-900",particles:20,colors:["bg-green-500","bg-orange-500","bg-yellow-500"]},dashboard:{gradient:"bg-gradient-to-br from-gray-900 via-gray-800 to-black",particles:15,colors:["bg-green-400","bg-orange-400","bg-yellow-400"]}}[t];return(0,r.jsxs)("div",{className:"fixed inset-0 z-0 ".concat(a),children:[(0,r.jsx)("div",{className:"absolute inset-0 ".concat(o.gradient," animate-gradient")}),(0,r.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,r.jsx)(s.P.div,{className:"absolute -top-40 -right-40 w-80 h-80 bg-orange-500 rounded-full mix-blend-multiply filter blur-xl opacity-20",animate:{x:[0,30,-20,0],y:[0,-50,20,0],scale:[1,1.1,.9,1]},transition:{duration:7,repeat:1/0,ease:"easeInOut"}}),(0,r.jsx)(s.P.div,{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20",animate:{x:[0,-30,20,0],y:[0,50,-20,0],scale:[1,.9,1.1,1]},transition:{duration:7,repeat:1/0,ease:"easeInOut",delay:2}}),(0,r.jsx)(s.P.div,{className:"absolute top-40 left-40 w-80 h-80 bg-amber-500 rounded-full mix-blend-multiply filter blur-xl opacity-20",animate:{x:[0,20,-30,0],y:[0,-30,40,0],scale:[1,1.2,.8,1]},transition:{duration:7,repeat:1/0,ease:"easeInOut",delay:4}})]}),(0,r.jsx)("div",{className:"absolute inset-0 opacity-20",children:[...Array(o.particles)].map((e,t)=>{let a=l[t%l.length];return(0,r.jsx)(s.P.div,{className:"absolute w-1 h-1 ".concat(o.colors[t%o.colors.length]," rounded-full"),style:{left:"".concat(a.left,"%"),top:"".concat(a.top,"%")},animate:{y:[0,-20,0],opacity:[.2,.8,.2],scale:[1,1.5,1]},transition:{duration:3+t%3,repeat:1/0,delay:t%4*.5,ease:"easeInOut"}},t)})}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-black/30"})]})}},3727:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var r=a(95155),s=a(68289),l=a(6874),o=a.n(l),n=a(35695),i=a(12115),d=a(10351),c=a(585),u=a(13741),p=a(98030);function m(){let e=(0,n.useRouter)(),{adminLogin:t,isLoading:a,error:l,clearError:m,isAuthenticated:f,user:h}=(0,p.A)(),[x,y]=(0,i.useState)({email:"",password:"",adminCode:""}),[g,b]=(0,i.useState)(!1),[v,j]=(0,i.useState)({});(0,i.useEffect)(()=>{f&&(null==h?void 0:h.role)==="ADMIN"?e.push("/admin/dashboard"):f&&(null==h?void 0:h.role)==="USER"&&e.push("/dashboard")},[f,h,e]),(0,i.useEffect)(()=>{l&&m(),j({})},[x,l,m]);let A=async a=>{if(a.preventDefault(),(()=>{var e;let t={};return x.email.trim()?/\S+@\S+\.\S+/.test(x.email)||(t.email="Email is invalid"):t.email="Email is required",x.password||(t.password="Password is required"),(null==(e=x.adminCode)?void 0:e.trim())||(t.adminCode="Admin code is required"),j(t),0===Object.keys(t).length})())try{await t(x),e.push("/admin/dashboard")}catch(e){console.error("Admin login failed:",e)}},w=e=>{let{name:t,value:a}=e.target;y(e=>({...e,[t]:a}))};return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-green-900 to-black text-white overflow-hidden relative",children:[(0,r.jsx)(c.L$,{variant:"default"}),(0,r.jsx)("nav",{className:"relative z-50 px-6 py-4",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto flex items-center justify-between",children:[(0,r.jsx)(o(),{href:"/",className:"flex items-center",children:(0,r.jsx)("span",{className:"text-xl font-bold bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent",children:"BetterInterest"})}),(0,r.jsxs)(o(),{href:"/",className:"inline-flex items-center text-gray-400 hover:text-white transition-colors",children:[(0,r.jsx)(d.kRp,{className:"mr-2"}),"Back to Home"]})]})}),(0,r.jsx)("div",{className:"relative z-10 flex items-center justify-center min-h-[calc(100vh-80px)] p-8",children:(0,r.jsxs)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center mb-6",children:[(0,r.jsx)("span",{className:"text-3xl font-bold bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent",children:"BetterInterest"}),(0,r.jsx)(d.pcC,{className:"text-red-400 ml-2",size:24})]}),(0,r.jsx)("h1",{className:"text-4xl font-bold mb-4 text-white",children:"Admin Access"}),(0,r.jsx)("p",{className:"text-gray-400 text-lg mb-8",children:"Secure administrative portal"})]}),l&&(0,r.jsx)(s.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-6 max-w-md mx-auto",children:(0,r.jsx)("p",{className:"text-red-400 text-sm",children:l})}),(0,r.jsxs)("form",{onSubmit:A,className:"space-y-6 max-w-md mx-auto",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2 text-white",children:"Admin Email"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(d.pHD,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,r.jsx)("input",{type:"email",name:"email",value:x.email,onChange:w,className:"w-full pl-10 pr-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg focus:border-red-500 focus:outline-none transition-colors",placeholder:"<EMAIL>"})]}),v.email&&(0,r.jsx)("p",{className:"text-red-400 text-xs mt-1",children:v.email})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2 text-white",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(d.F5$,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,r.jsx)("input",{type:g?"text":"password",name:"password",value:x.password,onChange:w,className:"w-full pl-10 pr-12 py-3 bg-gray-900/50 border border-gray-700 rounded-lg focus:border-red-500 focus:outline-none transition-colors",placeholder:"••••••••"}),(0,r.jsx)("button",{type:"button",onClick:()=>b(!g),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white",children:g?(0,r.jsx)(d._NO,{}):(0,r.jsx)(d.Vap,{})})]}),v.password&&(0,r.jsx)("p",{className:"text-red-400 text-xs mt-1",children:v.password})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2 text-white",children:"Admin Access Code"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(d.pcC,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,r.jsx)("input",{type:"password",name:"adminCode",value:x.adminCode||"",onChange:w,className:"w-full pl-10 pr-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg focus:border-red-500 focus:outline-none transition-colors",placeholder:"Enter admin code"})]}),v.adminCode&&(0,r.jsx)("p",{className:"text-red-400 text-xs mt-1",children:v.adminCode})]}),(0,r.jsx)(u.$n,{type:"submit",disabled:a,className:"w-full",variant:"primary",children:a?"Authenticating...":"Access Admin Panel"})]}),(0,r.jsx)("div",{className:"text-center mt-8",children:(0,r.jsxs)("p",{className:"text-gray-400",children:["Not an admin?"," ",(0,r.jsx)(o(),{href:"/auth/login",className:"text-green-400 hover:text-green-300 font-medium",children:"User Login"})]})})]})})]})}},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}})},41778:(e,t,a)=>{Promise.resolve().then(a.bind(a,3727))},98030:(e,t,a)=>{"use strict";a.d(t,{A:()=>c,AuthProvider:()=>d});var r=a(95155),s=a(12115),l=a(96365);let o={user:null,token:null,isAuthenticated:!1,isLoading:!0,error:null};function n(e,t){switch(t.type){case"AUTH_START":return{...e,isLoading:!0,error:null};case"AUTH_SUCCESS":return{...e,user:t.payload.user,token:t.payload.token,isAuthenticated:!0,isLoading:!1,error:null};case"AUTH_FAILURE":return{...e,user:null,token:null,isAuthenticated:!1,isLoading:!1,error:t.payload};case"LOGOUT":return{...e,user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null};case"UPDATE_USER":return{...e,user:t.payload};case"CLEAR_ERROR":return{...e,error:null};default:return e}}let i=(0,s.createContext)(void 0);function d(e){let{children:t}=e,[a,d]=(0,s.useReducer)(n,o);(0,s.useEffect)(()=>{c()},[]),(0,s.useEffect)(()=>{if(a.token&&a.isAuthenticated){let e=setInterval(async()=>{try{a.token&&l.authService.isTokenExpired(a.token)&&await l.authService.refreshToken()}catch(e){console.error("Token refresh failed:",e),f()}},3e5);return()=>clearInterval(e)}},[a.token,a.isAuthenticated]);let c=async()=>{try{let e=l.authService.getStoredToken();if(!e)return void d({type:"AUTH_FAILURE",payload:"No token found"});if(l.authService.isTokenExpired(e))try{await l.authService.refreshToken();let e=l.authService.getStoredToken();if(e){let t=await l.authService.getCurrentUser();d({type:"AUTH_SUCCESS",payload:{user:t,token:e}})}else throw Error("Token refresh failed")}catch(e){d({type:"AUTH_FAILURE",payload:"Session expired"});return}else{let t=await l.authService.getCurrentUser();d({type:"AUTH_SUCCESS",payload:{user:t,token:e}})}}catch(e){d({type:"AUTH_FAILURE",payload:"Authentication check failed"})}},u=async e=>{try{d({type:"AUTH_START"});let t=await l.authService.login(e);d({type:"AUTH_SUCCESS",payload:{user:t.user,token:t.token}})}catch(e){throw d({type:"AUTH_FAILURE",payload:e instanceof Error?e.message:"Login failed"}),e}},p=async e=>{try{d({type:"AUTH_START"});let t=await l.authService.signup(e);d({type:"AUTH_SUCCESS",payload:{user:t.user,token:t.token}})}catch(e){throw d({type:"AUTH_FAILURE",payload:e instanceof Error?e.message:"Signup failed"}),e}},m=async e=>{try{d({type:"AUTH_START"});let t=await l.authService.adminLogin(e);d({type:"AUTH_SUCCESS",payload:{user:t.user,token:t.token}})}catch(e){throw d({type:"AUTH_FAILURE",payload:e instanceof Error?e.message:"Admin login failed"}),e}},f=async()=>{try{await l.authService.logout()}catch(e){console.error("Logout error:",e)}finally{d({type:"LOGOUT"})}},h={...a,login:u,signup:p,adminLogin:m,logout:f,updateUser:e=>{d({type:"UPDATE_USER",payload:e})},clearError:()=>{d({type:"CLEAR_ERROR"})},checkAuth:c};return(0,r.jsx)(i.Provider,{value:h,children:t})}function c(){let e=(0,s.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}}},e=>{e.O(0,[844,5236,6874,3289,8441,5964,7358],()=>e(e.s=41778)),_N_E=e.O()}]);