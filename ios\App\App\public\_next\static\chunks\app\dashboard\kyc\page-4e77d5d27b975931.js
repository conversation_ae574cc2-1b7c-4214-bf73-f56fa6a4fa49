(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8051],{10432:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var n=r(95155),o=r(12115),s=r(98030),i=r(35695),l=r(11846),a=r(64198);let c={fullName:"",phoneNumber:"",ninOrBvn:""};function d(){let{user:e,isAuthenticated:t,isLoading:r}=(0,s.A)(),d=(0,i.useRouter)(),[u,h]=(0,o.useState)(c),[p,m]=(0,o.useState)(!1);(0,o.useEffect)(()=>r||t?r||(null==e?void 0:e.role)!=="ADMIN"?void(e&&h(t=>({...t,fullName:e.firstName&&e.lastName?"".concat(e.firstName," ").concat(e.lastName):t.fullName,phoneNumber:e.phoneNumber||t.phoneNumber}))):void d.push("/admin/dashboard"):void d.push("/auth/login"),[t,r,e,d]);let f=(e,t)=>{h(r=>({...r,[e]:t}))},x=async e=>{if(e.preventDefault(),!u.fullName||!u.phoneNumber||!u.ninOrBvn)return void a.P0.error("Please fill in all required fields");m(!0);try{if(!(await fetch("/api/kyc/verify-nin",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(u)})).ok)throw Error("Verification failed");a.P0.success("KYC verification submitted successfully!"),d.push("/dashboard")}catch(e){a.P0.error("Failed to submit KYC verification")}finally{m(!1)}};return r?(0,n.jsx)(l.A,{children:(0,n.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,n.jsxs)("div",{className:"text-center","data-aos":"fade-in",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"}),(0,n.jsx)("p",{className:"text-gray-400",children:"Loading KYC verification..."})]})})}):t&&(null==e?void 0:e.role)!=="ADMIN"?(null==e?void 0:e.kycStatus)==="APPROVED"?(0,n.jsx)(l.A,{title:"KYC Verification",children:(0,n.jsxs)("div",{className:"max-w-2xl mx-auto text-center py-12",children:[(0,n.jsx)("div",{className:"w-20 h-20 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,n.jsx)("span",{className:"text-green-400 text-4xl",children:"✓"})}),(0,n.jsx)("h2",{className:"text-2xl font-bold text-white mb-4",children:"KYC Verification Complete"}),(0,n.jsx)("p",{className:"text-gray-300 mb-6",children:"Your identity has been successfully verified. You now have access to all platform features."}),(0,n.jsx)("button",{onClick:()=>d.push("/dashboard"),className:"px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors",children:"Return to Dashboard"})]})}):(0,n.jsx)(l.A,{title:"KYC Verification",children:(0,n.jsxs)("div",{className:"max-w-xl mx-auto mt-12 bg-gray-900/50 border border-gray-800 rounded-lg p-8",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-white mb-6 text-center",children:"Identity Verification"}),(0,n.jsx)("p",{className:"text-gray-400 mb-8 text-center",children:"Please complete this short form to verify your identity. Your information is secure and encrypted."}),(0,n.jsxs)("form",{onSubmit:x,className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-gray-300 mb-2",children:"Full Name"}),(0,n.jsx)("input",{type:"text",className:"w-full px-4 py-2 rounded-lg bg-gray-800 text-white border border-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500",value:u.fullName,onChange:e=>f("fullName",e.target.value),required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-gray-300 mb-2",children:"Phone Number"}),(0,n.jsx)("input",{type:"tel",className:"w-full px-4 py-2 rounded-lg bg-gray-800 text-white border border-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500",value:u.phoneNumber,onChange:e=>f("phoneNumber",e.target.value),required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-gray-300 mb-2",children:"NIN or BVN"}),(0,n.jsx)("input",{type:"text",className:"w-full px-4 py-2 rounded-lg bg-gray-800 text-white border border-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500",value:u.ninOrBvn,onChange:e=>f("ninOrBvn",e.target.value),required:!0}),(0,n.jsx)("p",{className:"text-gray-400 text-xs mt-1",children:"Enter your National Identification Number (NIN) or Bank Verification Number (BVN)."})]}),(0,n.jsx)("button",{type:"submit",disabled:p,className:"w-full py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg transition-colors font-semibold mt-4",children:p?"Submitting...":"Submit for Verification"})]})]})}):null}},16643:(e,t,r)=>{Promise.resolve().then(r.bind(r,10432))},35695:(e,t,r)=>{"use strict";var n=r(18999);r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}})},60760:(e,t,r)=>{"use strict";r.d(t,{N:()=>b});var n=r(95155),o=r(12115),s=r(90869),i=r(82885),l=r(97494),a=r(80845),c=r(27351),d=r(51508);class u extends o.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=(0,c.s)(e)&&e.offsetWidth||0,n=this.props.sizeRef.current;n.height=t.offsetHeight||0,n.width=t.offsetWidth||0,n.top=t.offsetTop,n.left=t.offsetLeft,n.right=r-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function h(e){let{children:t,isPresent:r,anchorX:s,root:i}=e,l=(0,o.useId)(),a=(0,o.useRef)(null),c=(0,o.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:h}=(0,o.useContext)(d.Q);return(0,o.useInsertionEffect)(()=>{let{width:e,height:t,top:n,left:o,right:d}=c.current;if(r||!a.current||!e||!t)return;a.current.dataset.motionPopId=l;let u=document.createElement("style");h&&(u.nonce=h);let p=null!=i?i:document.head;return p.appendChild(u),u.sheet&&u.sheet.insertRule('\n          [data-motion-pop-id="'.concat(l,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===s?"left: ".concat(o):"right: ".concat(d),"px !important;\n            top: ").concat(n,"px !important;\n          }\n        ")),()=>{p.removeChild(u),p.contains(u)&&p.removeChild(u)}},[r]),(0,n.jsx)(u,{isPresent:r,childRef:a,sizeRef:c,children:o.cloneElement(t,{ref:a})})}let p=e=>{let{children:t,initial:r,isPresent:s,onExitComplete:l,custom:c,presenceAffectsLayout:d,mode:u,anchorX:p,root:f}=e,x=(0,i.M)(m),g=(0,o.useId)(),b=!0,y=(0,o.useMemo)(()=>(b=!1,{id:g,initial:r,isPresent:s,custom:c,onExitComplete:e=>{for(let t of(x.set(e,!0),x.values()))if(!t)return;l&&l()},register:e=>(x.set(e,!1),()=>x.delete(e))}),[s,x,l]);return d&&b&&(y={...y}),(0,o.useMemo)(()=>{x.forEach((e,t)=>x.set(t,!1))},[s]),o.useEffect(()=>{s||x.size||!l||l()},[s]),"popLayout"===u&&(t=(0,n.jsx)(h,{isPresent:s,anchorX:p,root:f,children:t})),(0,n.jsx)(a.t.Provider,{value:y,children:t})};function m(){return new Map}var f=r(32082);let x=e=>e.key||"";function g(e){let t=[];return o.Children.forEach(e,e=>{(0,o.isValidElement)(e)&&t.push(e)}),t}let b=e=>{let{children:t,custom:r,initial:a=!0,onExitComplete:c,presenceAffectsLayout:d=!0,mode:u="sync",propagate:h=!1,anchorX:m="left",root:b}=e,[y,N]=(0,f.xQ)(h),F=(0,o.useMemo)(()=>g(t),[t]),v=h&&!y?[]:F.map(x),j=(0,o.useRef)(!0),E=(0,o.useRef)(F),C=(0,i.M)(()=>new Map),[w,k]=(0,o.useState)(F),[P,A]=(0,o.useState)(F);(0,l.E)(()=>{j.current=!1,E.current=F;for(let e=0;e<P.length;e++){let t=x(P[e]);v.includes(t)?C.delete(t):!0!==C.get(t)&&C.set(t,!1)}},[P,v.length,v.join("-")]);let B=[];if(F!==w){let e=[...F];for(let t=0;t<P.length;t++){let r=P[t],n=x(r);v.includes(n)||(e.splice(t,0,r),B.push(r))}return"wait"===u&&B.length&&(e=B),A(g(e)),k(F),null}let{forceRender:R}=(0,o.useContext)(s.L);return(0,n.jsx)(n.Fragment,{children:P.map(e=>{let t=x(e),o=(!h||!!y)&&(F===P||v.includes(t));return(0,n.jsx)(p,{isPresent:o,initial:(!j.current||!!a)&&void 0,custom:r,presenceAffectsLayout:d,mode:u,root:b,onExitComplete:o?void 0:()=>{if(!C.has(t))return;C.set(t,!0);let e=!0;C.forEach(t=>{t||(e=!1)}),e&&(null==R||R(),A(E.current),h&&(null==N||N()),c&&c())},anchorX:m,children:e},t)})})}},64198:(e,t,r)=>{"use strict";r.d(t,{CustomToaster:()=>a,P0:()=>l,oR:()=>s.Ay});var n=r(95155),o=r(68289),s=r(13568),i=r(10351);let l={success:e=>{s.Ay.success(e,{duration:4e3,position:"top-right",style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}})},error:e=>{s.Ay.error(e,{duration:5e3,position:"top-right",style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}})},warning:e=>{(0,s.Ay)(e,{duration:4e3,position:"top-right",icon:"⚠️",style:{background:"#92400E",color:"#FFFBEB",border:"1px solid #F59E0B"}})},info:e=>{(0,s.Ay)(e,{duration:4e3,position:"top-right",icon:"ℹ️",style:{background:"#1E3A8A",color:"#EFF6FF",border:"1px solid #3B82F6"}})},loading:e=>s.Ay.loading(e,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"}}),dismiss:e=>{s.Ay.dismiss(e)},promise:(e,t)=>s.Ay.promise(e,t,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"},success:{style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}})};function a(){return(0,n.jsx)(s.l$,{position:"top-right",reverseOrder:!1,gutter:8,containerClassName:"",containerStyle:{},toastOptions:{className:"",duration:4e3,style:{background:"#1F2937",color:"#F9FAFB",border:"1px solid #374151",borderRadius:"8px",fontSize:"14px",fontWeight:"500",padding:"12px 16px",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"},success:{iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}},children:e=>(0,n.jsx)(s.bv,{toast:e,children:t=>{let{icon:r,message:l}=t;return(0,n.jsxs)(o.P.div,{initial:{opacity:0,y:-50,scale:.3},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.3},transition:{type:"spring",stiffness:500,damping:30,duration:.3},className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:r}),(0,n.jsx)("div",{className:"flex-1",children:l}),"loading"!==e.type&&(0,n.jsx)("button",{onClick:()=>s.Ay.dismiss(e.id),className:"flex-shrink-0 p-1 rounded-full hover:bg-gray-600 transition-colors",children:(0,n.jsx)(i.yGN,{className:"w-4 h-4"})})]})}})})}}},e=>{e.O(0,[844,5236,6874,6766,3568,3289,1846,8441,5964,7358],()=>e(e.s=16643)),_N_E=e.O()}]);