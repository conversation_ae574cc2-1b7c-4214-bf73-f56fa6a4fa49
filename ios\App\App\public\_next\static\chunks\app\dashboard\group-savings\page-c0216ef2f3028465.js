(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2155],{12771:(e,t,r)=>{Promise.resolve().then(r.bind(r,18352))},18352:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var o=r(95155),a=r(68289),s=r(35695),n=r(12115),i=r(13568),l=r(10351),c=r(58111),u=r(11846),d=r(14585),p=r(66440),g=r(98030),h=r(59972);let m=["RECRUITING","ACTIVE","COMPLETED","FILLED"],x=(e,t)=>{var r,o,a,s,n,i,l,c,u,d,p,g,h,x,b,v;let y=e.status?e.status.toUpperCase():"ACTIVE",f=m.includes(y)?y:"ACTIVE",w=null!=(s=null!=(a=null!=(o=e.memberCount)?o:e.currentMembers)?a:Array.isArray(e.members)?e.members.length:0)?s:0,j=null!=(c=null!=(l=null!=(i=null!=(n=e.maxMembers)?n:e.max_members)?i:e.maximumMembers)?l:e.capacity)?c:0,S=null!=(p=null!=(d=null!=(u=e.currentAmount)?u:e.savedAmount)?d:e.balance)?p:0,N=null!=(x=null!=(h=null!=(g=e.contributionAmount)?g:e.depositAmount)?h:e.contribution)?x:0,E=(e.frequency||e.depositFrequency||"MONTHLY").toUpperCase(),A=(null==(r=e.creator)?void 0:r.name)||e.creatorName||e.createdBy||"",C=e.startDate||e.createdAt||"",G=e.endDate||e.targetDate||"",I=e.inviteCode||e.invite_code||e.code||"",P=t&&(e.owner===t.id||e.owner===t._id||e.creatorId===t.id||e.creatorId===t._id||e.creator&&(e.creator.id===t.id||e.creator.id===t._id)),k=null!=(v=null!=(b=e.targetAmount)?b:e.target_amount)?v:0,T=k>0?Math.round(S/k*100):0;return{id:e._id||e.id,name:e.name||e.title||"",description:e.description||"",targetAmount:k,currentAmount:S,memberCount:w,maxMembers:j,contributionAmount:N,frequency:E,status:f,isPublic:e.isPublic||!1,createdBy:A,startDate:C,endDate:G,inviteCode:I,isOwner:P,progress:T}};function b(){let{user:e,isAuthenticated:t,isLoading:r}=(0,g.A)(),m=(0,s.useRouter)(),[b,v]=(0,n.useState)([]),[y,f]=(0,n.useState)(!1),[w,j]=(0,n.useState)(!1),[S,N]=(0,n.useState)(!1),[E,A]=(0,n.useState)("my-groups"),[C,G]=(0,n.useState)(""),[I,P]=(0,n.useState)({name:"",description:"",targetAmount:"",maxMembers:"10",contributionAmount:"",frequency:"MONTHLY",isPublic:!1,duration:"12"}),[k,T]=(0,n.useState)(!1),[L,O]=(0,n.useState)(""),[M,D]=(0,n.useState)(!1),[F,R]=(0,n.useState)(null),H=async e=>{if(e.preventDefault(),L&&F){D(!0);try{let e={"Content-Type":"application/json"};{let t=localStorage.getItem("auth_token");t&&(e.Authorization="Bearer ".concat(t))}let t=await fetch("".concat("http://localhost:8080","/api/group-savings/group-plans/invite"),{method:"POST",headers:e,body:JSON.stringify({groupId:F,email:L})});if(!t.ok){let e=await t.json();throw Error(e.error||e.message||"Failed to send invite")}i.Ay.success("Invite sent!"),T(!1),O(""),R(null)}catch(e){i.Ay.error((null==e?void 0:e.message)||"Failed to send invite")}finally{D(!1)}}};(0,n.useEffect)(()=>r||t?r||(null==e?void 0:e.role)!=="ADMIN"?void(async()=>{try{N(!0);let t=await h.I.getUserGroups(),r=await h.I.getAllGroups();v([...t.map(t=>x(t,e)),...r.filter(e=>!t.some(t=>(t._id||t.id)===(e._id||e.id))).map(t=>x(t,e))])}catch(e){console.error("Failed to fetch group savings:",e)}finally{N(!1)}})():void m.push("/admin/dashboard"):void m.push("/auth/login"),[t,r,e,m]);let U=async t=>{t.preventDefault(),N(!0);try{let t={name:I.name,description:I.description,targetAmount:parseInt(I.targetAmount),maxMembers:parseInt(I.maxMembers),contributionAmount:parseInt(I.contributionAmount),frequency:I.frequency,isPublic:I.isPublic,duration:parseInt(I.duration),startDate:new Date().toISOString(),category:"GENERAL"};await h.I.createGroup({...t,category:"GENERAL"});let r=await h.I.getUserGroups(),o=await h.I.getAllGroups();v([...r.map(t=>x(t,e)),...o.filter(e=>!r.some(t=>(t._id||t.id)===(e._id||e.id))).map(t=>x(t,e))]),f(!1),P({name:"",description:"",targetAmount:"",maxMembers:"10",contributionAmount:"",frequency:"MONTHLY",isPublic:!1,duration:"12"})}catch(e){console.error("Failed to create group:",e)}finally{N(!1)}},J=async t=>{t.preventDefault(),N(!0);try{let t,r,o=C.trim(),a=o.match(/[?&]groupId=([a-zA-Z0-9]+)/),s=o.match(/[?&](inviteCode|code)=([a-zA-Z0-9]+)/);if(s?r=s[2]:a?t=a[1]:/^[a-fA-F0-9]{24}$/.test(o)?t=o:r=o,!t&&!r)throw Error("Please enter a valid group link, group ID, or invite code.");let n=!1,l="",c="http://localhost:8080",u={"Content-Type":"application/json"};{let e=localStorage.getItem("auth_token");e&&(u.Authorization="Bearer ".concat(e))}if(r)try{let e=await fetch("".concat(c,"/api/group-savings/group-plans/accept-invite/").concat(r),{method:"POST",headers:u});if(!e.ok){let t=await e.json();throw Error(t.error||t.message||"Failed to join group")}n=!0}catch(e){l=(null==e?void 0:e.message)||""}else if(t)try{let e=await fetch("".concat(c,"/api/group-savings/group-plans/join/").concat(t),{method:"POST",headers:u});if(!e.ok){let t=await e.json();throw Error(t.error||t.message||"Failed to join group")}n=!0}catch(e){l=(null==e?void 0:e.message)||""}if(!n)return void(l.toLowerCase().includes("full")?i.Ay.error("Group is full."):l.toLowerCase().includes("already")?i.Ay.error("You are already a member of this group."):l.toLowerCase().includes("not found")?i.Ay.error("Group not found."):l.toLowerCase().includes("not public")?i.Ay.error("This group is private. Please use an invite link."):l?i.Ay.error(l):i.Ay.error("Invalid group link, group ID, or invite code."));let d=await h.I.getUserGroups(),p=await h.I.getAllGroups();v([...d.map(t=>x(t,e)),...p.filter(e=>!d.some(t=>(t.id||t._id)===(e.id||e._id))).map(t=>x(t,e))]),j(!1),G(""),i.Ay.success("Successfully joined group!")}catch(e){i.Ay.error((null==e?void 0:e.message)||"Invalid group link, group ID, or invite code."),console.error("Failed to join group:",e)}finally{N(!1)}},_=e=>{let{name:t,value:r,type:o}=e.target;P(a=>({...a,[t]:"checkbox"===o?e.target.checked:r}))},q=b.filter(e=>e.isOwner||e.memberCount>0);console.log("[DEBUG] groupSavings:",b);let z=b.map(e=>("true"===String(e.isPublic).toLowerCase()||!0===e.isPublic)&&"number"==typeof e.maxMembers&&"number"==typeof e.memberCount&&e.maxMembers>0&&e.memberCount>=e.maxMembers&&"FILLED"!==e.status?{...e,status:"FILLED"}:["RECRUITING","ACTIVE","COMPLETED","FILLED"].includes(e.status)?e:{...e,status:"ACTIVE"});console.log("[DEBUG] groupSavings:",z);let B=z.filter(e=>{let t="true"===String(e.isPublic).toLowerCase()||!0===e.isPublic||1===e.isPublic,r=(e.status||"").toString().toUpperCase(),o=t&&("RECRUITING"===r||"ACTIVE"===r||"FILLED"===r||!r);return console.log("[DEBUG] group:",e,"| isPublic:",t,"| status:",r,"| result:",o),o}),V=q.reduce((e,t)=>e+t.currentAmount,0),Y=q.filter(e=>"ACTIVE"===e.status||"RECRUITING"===e.status).length;return r?(0,o.jsx)(u.A,{children:(0,o.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"}),(0,o.jsx)("p",{className:"text-gray-400",children:"Loading group savings..."})]})})}):t&&(null==e?void 0:e.role)!=="ADMIN"?(0,o.jsx)(u.A,{title:"Group Savings",children:(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,o.jsx)(c.hI,{title:"Total Contributions",value:"₦".concat(V.toLocaleString()),subtitle:"Across all groups",icon:l.z8N,color:"green"}),(0,o.jsx)(c.hI,{title:"Active Groups",value:Y,subtitle:"".concat(q.length," total joined"),icon:l.cfS,color:"blue"}),(0,o.jsx)(c.hI,{title:"Monthly Commitment",value:"₦".concat(q.reduce((e,t)=>e+("ACTIVE"===t.status||"RECRUITING"===t.status?t.contributionAmount:0),0).toLocaleString()),subtitle:"Total scheduled",icon:l.x_j,color:"purple"})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsxs)("div",{className:"flex space-x-4",children:[(0,o.jsx)("button",{onClick:()=>A("my-groups"),className:"px-4 py-2 rounded-lg transition-colors ".concat("my-groups"===E?"bg-green-600 text-white":"text-gray-400 hover:text-white"),children:"My Groups"}),(0,o.jsx)("button",{onClick:()=>A("discover"),className:"px-4 py-2 rounded-lg transition-colors ".concat("discover"===E?"bg-green-600 text-white":"text-gray-400 hover:text-white"),children:"Discover"})]}),(0,o.jsxs)("div",{className:"flex space-x-3",children:[(0,o.jsxs)("button",{onClick:()=>j(!0),className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors",children:[(0,o.jsx)(l.vq3,{className:"w-4 h-4"}),(0,o.jsx)("span",{children:"Join Group"})]}),(0,o.jsxs)("button",{onClick:()=>f(!0),className:"flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors",children:[(0,o.jsx)(l.GGD,{className:"w-4 h-4"}),(0,o.jsx)("span",{children:"Create Group"})]})]})]}),(0,o.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:("my-groups"===E?q:B).map(t=>(0,o.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6 hover:border-gray-700 transition-colors",children:[(0,o.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-white mb-1",children:t.name}),(0,o.jsx)("p",{className:"text-gray-400 text-sm",children:t.description})]}),(0,o.jsx)("div",{className:"px-2 py-1 rounded-full text-xs ".concat("ACTIVE"===t.status?"bg-green-500/20 text-green-400":"RECRUITING"===t.status?"bg-yellow-500/20 text-yellow-400":"bg-blue-500/20 text-blue-400"),children:t.status})]}),(0,o.jsxs)("div",{className:"space-y-3 mb-4",children:[(0,o.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,o.jsx)("span",{className:"text-gray-400",children:"Progress"}),(0,o.jsxs)("span",{className:"text-green-400",children:[t.progress,"%"]})]}),(0,o.jsx)("div",{className:"w-full bg-gray-800 rounded-full h-2",children:(0,o.jsx)("div",{className:"bg-green-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(t.progress,"%")}})}),(0,o.jsxs)("div",{className:"flex justify-between text-sm text-gray-400",children:[(0,o.jsxs)("span",{children:["₦",t.currentAmount.toLocaleString()]}),(0,o.jsxs)("span",{children:["₦",t.targetAmount.toLocaleString()]})]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4 text-sm",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"text-gray-400",children:"Members"}),(0,o.jsxs)("p",{className:"text-white",children:[t.memberCount,"/",t.maxMembers]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"text-gray-400",children:"Contribution"}),(0,o.jsxs)("p",{className:"text-white",children:["₦",t.contributionAmount.toLocaleString()]})]})]}),(0,o.jsxs)("div",{className:"flex space-x-2",children:[(0,o.jsxs)("button",{className:"flex-1 px-3 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors flex items-center justify-center space-x-1",children:[(0,o.jsx)(l.Vap,{className:"w-4 h-4"}),(0,o.jsx)("span",{children:"View"})]}),"discover"===E&&t.isPublic&&"RECRUITING"===t.status&&(0,o.jsxs)("button",{onClick:async()=>{N(!0);try{let r={"Content-Type":"application/json"};{let e=localStorage.getItem("auth_token");e&&(r.Authorization="Bearer ".concat(e))}let o=await fetch("".concat("http://localhost:8080","/api/group-savings/group-plans/join/").concat(t.id),{method:"POST",headers:r});if(!o.ok){let e=await o.json();throw Error(e.error||e.message||"Failed to join group")}let a=await h.I.getUserGroups(),s=await h.I.getAllGroups();v([...a.map(t=>x(t,e)),...s.filter(e=>!a.some(t=>(t.id||t._id)===(e.id||e._id))).map(t=>x(t,e))]),i.Ay.success("Successfully joined group!")}catch(e){var r,o,a;(null==e||null==(r=e.message)?void 0:r.includes("already"))?i.Ay.error("You are already a member of this group."):(null==e||null==(o=e.message)?void 0:o.includes("full"))?i.Ay.error("Group is full."):(null==e||null==(a=e.message)?void 0:a.includes("not public"))?i.Ay.error("This group is private. Please use an invite link."):i.Ay.error((null==e?void 0:e.message)||"Failed to join group.")}finally{N(!1)}},className:"px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center space-x-1",children:[(0,o.jsx)(l.vq3,{className:"w-4 h-4"}),(0,o.jsx)("span",{children:"Join"})]}),t.isOwner&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("button",{onClick:()=>(e=>{let t=window.location.origin,r="";r=e.isPublic?"".concat(t,"/join?groupId=").concat(e.id):"".concat(t,"/join?inviteCode=").concat(e.inviteCode),navigator.clipboard.writeText(r),i.Ay.success("Join link copied to clipboard!")})(t),className:"px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center space-x-1",children:[(0,o.jsx)(l.nxz,{className:"w-4 h-4"}),(0,o.jsx)("span",{children:"Copy Join Link"})]}),(0,o.jsxs)("button",{onClick:()=>{R(t.id),T(!0)},className:"px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center space-x-1",children:[(0,o.jsx)(l.Pum,{className:"w-4 h-4"}),(0,o.jsx)("span",{children:"Invite by Email"})]})]}),(0,o.jsxs)(p.k5,{isOpen:k,onClose:()=>{T(!1),O(""),R(null)},title:"Invite User to Group",onSubmit:H,submitText:"Send Invite",isLoading:M,size:"sm",children:[(0,o.jsx)(d.ZQ,{label:"User Email",name:"inviteEmail",type:"email",value:L,onChange:e=>O(e.target.value),placeholder:"Enter user's email address",required:!0}),(0,o.jsx)("p",{className:"text-gray-400 text-sm",children:"The user will receive an email with a join link for this group."})]})]})]},t.id))}),(0,o.jsx)(p.k5,{isOpen:y,onClose:()=>f(!1),title:"Create Group Savings",onSubmit:U,submitText:"Create Group",isLoading:S,size:"lg",children:(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsx)(d.ZQ,{label:"Group Name",name:"name",value:I.name,onChange:_,placeholder:"e.g., Family Emergency Fund",required:!0}),(0,o.jsx)(d.ZQ,{label:"Max Members",name:"maxMembers",type:"number",value:I.maxMembers,onChange:_,placeholder:"10",required:!0}),(0,o.jsx)("div",{className:"md:col-span-2",children:(0,o.jsx)(d.TM,{label:"Description",name:"description",value:I.description,onChange:_,placeholder:"Describe the purpose of this group savings...",rows:3,required:!0})}),(0,o.jsx)(d.ZQ,{label:"Target Amount",name:"targetAmount",type:"number",value:I.targetAmount,onChange:_,placeholder:"5000000",required:!0}),(0,o.jsx)(d.ZQ,{label:"Contribution Amount",name:"contributionAmount",type:"number",value:I.contributionAmount,onChange:_,placeholder:"50000",required:!0}),(0,o.jsx)(d.pp,{label:"Frequency",name:"frequency",value:I.frequency,onChange:_,options:[{value:"WEEKLY",label:"Weekly"},{value:"MONTHLY",label:"Monthly"}],required:!0}),(0,o.jsx)(d.ZQ,{label:"Duration (months)",name:"duration",type:"number",value:I.duration,onChange:_,placeholder:"12",required:!0}),(0,o.jsx)("div",{className:"md:col-span-2",children:(0,o.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,o.jsx)("input",{type:"checkbox",name:"isPublic",checked:I.isPublic,onChange:_,className:"w-4 h-4 text-green-600 bg-gray-800 border-gray-600 rounded focus:ring-green-500"}),(0,o.jsx)("span",{className:"text-gray-300",children:"Make this group public (others can discover and join)"})]})})]})}),(0,o.jsxs)(p.k5,{isOpen:w,onClose:()=>j(!1),title:"Join Group Savings",onSubmit:J,submitText:"Join Group",isLoading:S,size:"sm",children:[(0,o.jsx)(d.ZQ,{label:"Join Link, Group ID, or Invite Code",name:"joinCode",value:C,onChange:e=>G(e.target.value),placeholder:"Paste join link, group ID, or invite code",required:!0}),(0,o.jsx)("p",{className:"text-gray-400 text-sm",children:"You can paste a join link, group ID, or invite code shared by the group owner to join their savings group."})]})]})}):null}},35695:(e,t,r)=>{"use strict";var o=r(18999);r.o(o,"usePathname")&&r.d(t,{usePathname:function(){return o.usePathname}}),r.o(o,"useRouter")&&r.d(t,{useRouter:function(){return o.useRouter}})},58111:(e,t,r)=>{"use strict";r.d(t,{Lz:()=>u,gz:()=>d,hI:()=>c});var o=r(95155),a=r(12115),s=r(68289),n=r(57740);let i={green:{bg:"bg-green-500/20",text:"text-green-400",border:"border-green-500/30",icon:"text-green-400"},blue:{bg:"bg-blue-500/20",text:"text-blue-400",border:"border-blue-500/30",icon:"text-blue-400"},red:{bg:"bg-red-500/20",text:"text-red-400",border:"border-red-500/30",icon:"text-red-400"},yellow:{bg:"bg-yellow-500/20",text:"text-yellow-400",border:"border-yellow-500/30",icon:"text-yellow-400"},purple:{bg:"bg-purple-500/20",text:"text-purple-400",border:"border-purple-500/30",icon:"text-purple-400"}};function l(e){var t,r,l,c;let{title:u,value:d,subtitle:p,icon:g,trend:h,color:m="green",onClick:x,className:b=""}=e,v=i[m]||i.green,{theme:y}=(0,n.DP)(),f=(0,n.Yx)(y),w=(0,a.useRef)(null),j=e=>{let t={1:"light"===y?"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)":"0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.5)",2:"light"===y?"0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)":"0 3px 6px rgba(0, 0, 0, 0.4), 0 3px 6px rgba(0, 0, 0, 0.6)",3:"light"===y?"0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)":"0 10px 20px rgba(0, 0, 0, 0.5), 0 6px 6px rgba(0, 0, 0, 0.7)"};return t[e]||t[1]};return(0,o.jsxs)(s.P.div,{ref:w,initial:{opacity:0,y:20},animate:{opacity:1,y:0},whileHover:{y:-4,boxShadow:j(3),transition:{duration:.2,ease:"easeOut"}},className:"\n        relative overflow-hidden rounded-xl p-6 transition-all duration-300 group\n        ".concat(x?"cursor-pointer":"","\n        ").concat("light"===y?"bg-white border border-gray-200 hover:border-gray-300":"bg-gray-900/80 border border-gray-700 hover:border-gray-600","\n        ").concat(b,"\n      "),style:{boxShadow:j(1)},onClick:x,children:[x&&(0,o.jsx)(s.P.div,{className:"absolute inset-0 bg-green-400 opacity-0 group-hover:opacity-5 transition-opacity duration-300",initial:{scale:0,opacity:0},whileHover:{scale:1,opacity:.05},transition:{duration:.3}}),(0,o.jsx)("div",{className:"absolute top-0 left-0 w-full h-1 bg-gradient-to-r ".concat((null==v||null==(t=v.bg)?void 0:t.replace("bg-","from-"))||"from-brand"," to-transparent opacity-60")}),(0,o.jsxs)("div",{className:"flex items-start justify-between relative z-10",children:[(0,o.jsxs)("div",{className:"flex-1",children:[(0,o.jsx)("p",{className:"text-sm font-medium mb-3 ".concat(f.text.secondary),children:u}),(0,o.jsx)("p",{className:"text-3xl font-bold mb-2 ".concat(f.text.primary),style:{fontFamily:"Inter, system-ui, sans-serif"},children:d}),p&&(0,o.jsx)("p",{className:"text-sm ".concat(f.text.tertiary," mb-2"),children:p}),h&&(0,o.jsxs)(s.P.div,{className:"flex items-center mt-3",initial:{opacity:0,x:-10},animate:{opacity:1,x:0},transition:{delay:.2},children:[(0,o.jsxs)("span",{className:"text-sm font-semibold px-2 py-1 rounded-full ".concat(h.isPositive?"text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/30":"text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/30"),children:[h.isPositive?"↗":"↘"," ",h.isPositive?"+":"",h.value,"%"]}),(0,o.jsx)("span",{className:"text-xs ml-2 ".concat(f.text.tertiary),children:"vs last month"})]})]}),g&&(0,o.jsx)(s.P.div,{className:"p-4 rounded-xl ".concat((null==v?void 0:v.bg)||"bg-brand/20"," ").concat((null==v?void 0:v.border)||"border-brand"," border-2 shadow-lg"),whileHover:{scale:1.05,rotate:5},transition:{duration:.2},style:{boxShadow:"0 4px 12px ".concat((null==v||null==(r=v.bg)?void 0:r.includes("green"))?"rgba(34, 197, 94, 0.3)":(null==v||null==(l=v.bg)?void 0:l.includes("blue"))?"rgba(59, 130, 246, 0.3)":(null==v||null==(c=v.bg)?void 0:c.includes("purple"))?"rgba(147, 51, 234, 0.3)":"rgba(34, 197, 94, 0.3)")},children:(0,o.jsx)(g,{className:"w-7 h-7 ".concat((null==v?void 0:v.icon)||"text-brand")})})]})]})}function c(e){let{title:t,value:r,subtitle:a,icon:s,color:n="green",trend:i}=e;return(0,o.jsx)(l,{title:t,value:r,subtitle:a,icon:s,color:n,trend:i})}function u(e){var t,r,a,l;let{title:c,subtitle:u,icon:d,color:p="blue",onClick:g}=e,h=i[p]||i.blue,{theme:m}=(0,n.DP)(),x=(0,n.Yx)(m);return(0,o.jsxs)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},whileHover:{y:-6,scale:1.02,boxShadow:"light"===m?"0 8px 25px rgba(0, 0, 0, 0.15), 0 16px 40px rgba(0, 0, 0, 0.1)":"0 8px 25px rgba(0, 0, 0, 0.4), 0 16px 40px rgba(0, 0, 0, 0.3)",transition:{duration:.2,ease:"easeOut"}},whileTap:{scale:.98},className:"\n        relative overflow-hidden rounded-xl p-6 cursor-pointer transition-all duration-300 group\n        ".concat("light"===m?"bg-gradient-to-br from-white to-gray-50 border border-gray-200 hover:border-gray-300":"bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-700 hover:border-gray-600","\n      "),style:{boxShadow:"light"===m?"0 2px 8px rgba(0, 0, 0, 0.1), 0 4px 16px rgba(0, 0, 0, 0.05)":"0 2px 8px rgba(0, 0, 0, 0.3), 0 4px 16px rgba(0, 0, 0, 0.2)"},onClick:g,children:[(0,o.jsx)(s.P.div,{className:"absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300 ".concat((null==h?void 0:h.bg)||"bg-brand/10"),initial:{scale:0},whileHover:{scale:1},transition:{duration:.3}}),(0,o.jsx)("div",{className:"absolute top-0 left-0 w-full h-1 bg-gradient-to-r ".concat((null==h||null==(t=h.bg)?void 0:t.replace("bg-","from-"))||"from-brand"," to-transparent")}),(0,o.jsxs)("div",{className:"flex items-center space-x-4 relative z-10",children:[(0,o.jsx)(s.P.div,{className:"p-4 rounded-xl ".concat((null==h?void 0:h.bg)||"bg-brand/20"," shadow-lg"),whileHover:{scale:1.1,rotate:10},transition:{duration:.2},style:{boxShadow:"0 4px 12px ".concat((null==h||null==(r=h.bg)?void 0:r.includes("green"))?"rgba(34, 197, 94, 0.3)":(null==h||null==(a=h.bg)?void 0:a.includes("blue"))?"rgba(59, 130, 246, 0.3)":(null==h||null==(l=h.bg)?void 0:l.includes("purple"))?"rgba(147, 51, 234, 0.3)":"rgba(34, 197, 94, 0.3)")},children:(0,o.jsx)(d,{className:"w-6 h-6 ".concat((null==h?void 0:h.icon)||"text-brand")})}),(0,o.jsxs)("div",{className:"flex-1",children:[(0,o.jsx)("h3",{className:"font-semibold text-lg ".concat(x.text.primary," mb-1"),children:c}),(0,o.jsx)("p",{className:"text-sm ".concat(x.text.secondary),children:u})]}),(0,o.jsx)(s.P.div,{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat((null==h?void 0:h.bg)||"bg-brand/20"," opacity-70"),whileHover:{scale:1.2,opacity:1},transition:{duration:.2},children:(0,o.jsx)("svg",{className:"w-4 h-4 ".concat((null==h?void 0:h.icon)||"text-brand"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]})]})}function d(e){let{title:t,current:r,target:a,unit:n="",color:l="green"}=e,c=Math.min(r/a*100,100),u=i[l]||i.green;return(0,o.jsxs)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6",children:[(0,o.jsx)("h3",{className:"text-white font-semibold mb-4",children:t}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,o.jsx)("span",{className:"text-gray-400",children:"Progress"}),(0,o.jsxs)("span",{className:(null==u?void 0:u.text)||"text-brand",children:[c.toFixed(1),"%"]})]}),(0,o.jsx)("div",{className:"w-full bg-gray-800 rounded-full h-2",children:(0,o.jsx)(s.P.div,{initial:{width:0},animate:{width:"".concat(c,"%")},transition:{duration:1,ease:"easeOut"},className:"h-2 rounded-full bg-gradient-to-r from-".concat(l,"-400 to-").concat(l,"-600")})}),(0,o.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,o.jsxs)("span",{className:"text-gray-400",children:[r.toLocaleString(),n," / ",a.toLocaleString(),n]}),(0,o.jsxs)("span",{className:"text-gray-400",children:[(a-r).toLocaleString(),n," remaining"]})]})]})]})}},59972:(e,t,r)=>{"use strict";r.d(t,{I:()=>s});let o="http://localhost:8080";class a{getAuthHeaders(){let e=localStorage.getItem("auth_token");return{"Content-Type":"application/json",...e&&{Authorization:"Bearer ".concat(e)}}}async getAllGroups(){let e="".concat(o,"/api/group-savings/group-plans/public");console.log("[GroupSavingsService] GET",e);try{let t=await fetch(e,{headers:this.getAuthHeaders()});console.log("[GroupSavingsService] Response status:",t.status);let r=await t.text();try{let e=JSON.parse(r);if(!t.ok)throw console.error("[GroupSavingsService] Error response:",e),Error(e.message||"Failed to fetch group savings");return e}catch(e){throw console.error("[GroupSavingsService] JSON parse error:",e,"Raw response:",r),Error("Invalid response from server.")}}catch(e){throw console.error("[GroupSavingsService] getAllGroups error:",e),e}}async getUserGroups(){let e="".concat(o,"/api/group-savings/group-plans/my");console.log("[GroupSavingsService] GET",e);try{let t=await fetch(e,{headers:this.getAuthHeaders()});console.log("[GroupSavingsService] Response status:",t.status);let r=await t.text();try{let e=JSON.parse(r);if(!t.ok)throw console.error("[GroupSavingsService] Error response:",e),Error(e.message||"Failed to fetch user groups");return e}catch(e){throw console.error("[GroupSavingsService] JSON parse error:",e,"Raw response:",r),Error("Invalid response from server.")}}catch(e){throw console.error("[GroupSavingsService] getUserGroups error:",e),e}}async getGroupById(e){let t="".concat(o,"/api/group-savings/group-plans/").concat(e);console.log("[GroupSavingsService] GET",t);try{let e=await fetch(t,{headers:this.getAuthHeaders()});console.log("[GroupSavingsService] Response status:",e.status);let r=await e.text();try{let t=JSON.parse(r);if(!e.ok)throw console.error("[GroupSavingsService] Error response:",t),Error(t.message||"Failed to fetch group details");return t}catch(e){throw console.error("[GroupSavingsService] JSON parse error:",e,"Raw response:",r),Error("Invalid response from server.")}}catch(e){throw console.error("[GroupSavingsService] getGroupById error:",e),e}}async createGroup(e){let t=e.frequency;"string"==typeof t&&(t=t.toLowerCase().includes("bi-week")?"WEEKLY":t.charAt(0).toUpperCase()+t.slice(1).toLowerCase());let r={title:e.name,depositFrequency:t,depositAmount:e.contributionAmount,targetDate:e.startDate,targetAmount:e.targetAmount,isPublic:e.isPublic,maxMembers:e.maxMembers,description:e.description,duration:e.duration,category:e.category},a="".concat(o,"/api/group-savings/group-plan");console.log("[GroupSavingsService] POST",a,"Payload:",r);try{let e=await fetch(a,{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(r)});console.log("[GroupSavingsService] Response status:",e.status);let t=await e.text();try{let r=JSON.parse(t);if(!e.ok)throw console.error("[GroupSavingsService] Error response:",r),Error(r.message||"Failed to create group");return r}catch(e){throw console.error("[GroupSavingsService] JSON parse error:",e,"Raw response:",t),Error("Invalid response from server.")}}catch(e){throw console.error("[GroupSavingsService] createGroup error:",e),e}}async updateGroup(e,t){let r="".concat(o,"/api/group-savings/").concat(e);console.log("[GroupSavingsService] PUT",r,"Payload:",t);try{let e=await fetch(r,{method:"PUT",headers:this.getAuthHeaders(),body:JSON.stringify(t)});console.log("[GroupSavingsService] Response status:",e.status);let o=await e.text();try{let t=JSON.parse(o);if(!e.ok)throw console.error("[GroupSavingsService] Error response:",t),Error(t.message||"Failed to update group");return t}catch(e){throw console.error("[GroupSavingsService] JSON parse error:",e,"Raw response:",o),Error("Invalid response from server.")}}catch(e){throw console.error("[GroupSavingsService] updateGroup error:",e),e}}async deleteGroup(e){let t="".concat(o,"/api/group-savings/").concat(e);console.log("[GroupSavingsService] DELETE",t);try{let e=await fetch(t,{method:"DELETE",headers:this.getAuthHeaders()});if(console.log("[GroupSavingsService] Response status:",e.status),!e.ok){let t;try{t=await e.json()}catch(e){throw console.error("[GroupSavingsService] JSON parse error:",e),Error("Invalid response from server.")}throw console.error("[GroupSavingsService] Error response:",t),Error(t.message||"Failed to delete group")}}catch(e){throw console.error("[GroupSavingsService] deleteGroup error:",e),e}}async joinGroup(e){let t=await fetch("".concat(o,"/api/group-savings/join"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Failed to join group");return t.json()}async leaveGroup(e){let t=await fetch("".concat(o,"/api/group-savings/").concat(e,"/leave"),{method:"POST",headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to leave group")}async inviteMember(e){let t=await fetch("".concat(o,"/api/group-savings/invite"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Failed to send invitation")}async removeMember(e,t){let r=await fetch("".concat(o,"/api/group-savings/").concat(e,"/members/").concat(t),{method:"DELETE",headers:this.getAuthHeaders()});if(!r.ok)throw Error((await r.json()).message||"Failed to remove member")}async updateMemberRole(e,t,r){let a=await fetch("".concat(o,"/api/group-savings/").concat(e,"/members/").concat(t,"/role"),{method:"PATCH",headers:this.getAuthHeaders(),body:JSON.stringify({role:r})});if(!a.ok)throw Error((await a.json()).message||"Failed to update member role")}async makeContribution(e){let t=await fetch("".concat(o,"/api/group-savings/contribute"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Failed to make contribution");return t.json()}async getGroupContributions(e,t){let r=new URLSearchParams;t&&Object.entries(t).forEach(e=>{let[t,o]=e;null!=o&&""!==o&&r.append(t,o.toString())});let a=await fetch("".concat(o,"/api/group-savings/").concat(e,"/contributions?").concat(r),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch contributions");return a.json()}async getUserContributions(e){let t=await fetch("".concat(o,"/api/group-savings/").concat(e,"/contributions/user"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch user contributions");return t.json()}async getPayoutSchedule(e){let t=await fetch("".concat(o,"/api/group-savings/").concat(e,"/payout-schedule"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch payout schedule");return t.json()}async updatePayoutSchedule(e,t){let r=await fetch("".concat(o,"/api/group-savings/").concat(e,"/payout-schedule"),{method:"PUT",headers:this.getAuthHeaders(),body:JSON.stringify({schedule:t})});if(!r.ok)throw Error((await r.json()).message||"Failed to update payout schedule");return r.json()}async processPayout(e,t){let r=await fetch("".concat(o,"/api/group-savings/").concat(e,"/payout/").concat(t,"/process"),{method:"POST",headers:this.getAuthHeaders()});if(!r.ok)throw Error((await r.json()).message||"Failed to process payout")}async getGroupStats(){let e=await fetch("".concat(o,"/api/group-savings/stats"),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch group statistics");return e.json()}async getGroupAnalytics(e){let t=await fetch("".concat(o,"/api/group-savings/").concat(e,"/analytics"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch group analytics");return t.json()}async getActiveGroups(){let e=await fetch("".concat(o,"/api/savings/group/active"),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch active group savings");return(await e.json()).activeGroups||[]}calculateGroupProgress(e){return Math.min(e.currentAmount/e.targetAmount*100,100)}calculateMemberContributionRate(e,t){return 0===t?0:e.totalContributions/t*100}getNextPayoutDate(e){let t=e.filter(e=>"PENDING"===e.status).sort((e,t)=>new Date(e.scheduledDate).getTime()-new Date(t.scheduledDate).getTime());return t.length>0?t[0].scheduledDate:null}formatCurrency(e){return new Intl.NumberFormat("en-NG",{style:"currency",currency:"NGN"}).format(e)}generateInviteCode(){return Math.random().toString(36).substring(2,8).toUpperCase()}}let s=new a},60760:(e,t,r)=>{"use strict";r.d(t,{N:()=>v});var o=r(95155),a=r(12115),s=r(90869),n=r(82885),i=r(97494),l=r(80845),c=r(27351),u=r(51508);class d extends a.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=(0,c.s)(e)&&e.offsetWidth||0,o=this.props.sizeRef.current;o.height=t.offsetHeight||0,o.width=t.offsetWidth||0,o.top=t.offsetTop,o.left=t.offsetLeft,o.right=r-o.width-o.left}return null}componentDidUpdate(){}render(){return this.props.children}}function p(e){let{children:t,isPresent:r,anchorX:s,root:n}=e,i=(0,a.useId)(),l=(0,a.useRef)(null),c=(0,a.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:p}=(0,a.useContext)(u.Q);return(0,a.useInsertionEffect)(()=>{let{width:e,height:t,top:o,left:a,right:u}=c.current;if(r||!l.current||!e||!t)return;l.current.dataset.motionPopId=i;let d=document.createElement("style");p&&(d.nonce=p);let g=null!=n?n:document.head;return g.appendChild(d),d.sheet&&d.sheet.insertRule('\n          [data-motion-pop-id="'.concat(i,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===s?"left: ".concat(a):"right: ".concat(u),"px !important;\n            top: ").concat(o,"px !important;\n          }\n        ")),()=>{g.removeChild(d),g.contains(d)&&g.removeChild(d)}},[r]),(0,o.jsx)(d,{isPresent:r,childRef:l,sizeRef:c,children:a.cloneElement(t,{ref:l})})}let g=e=>{let{children:t,initial:r,isPresent:s,onExitComplete:i,custom:c,presenceAffectsLayout:u,mode:d,anchorX:g,root:m}=e,x=(0,n.M)(h),b=(0,a.useId)(),v=!0,y=(0,a.useMemo)(()=>(v=!1,{id:b,initial:r,isPresent:s,custom:c,onExitComplete:e=>{for(let t of(x.set(e,!0),x.values()))if(!t)return;i&&i()},register:e=>(x.set(e,!1),()=>x.delete(e))}),[s,x,i]);return u&&v&&(y={...y}),(0,a.useMemo)(()=>{x.forEach((e,t)=>x.set(t,!1))},[s]),a.useEffect(()=>{s||x.size||!i||i()},[s]),"popLayout"===d&&(t=(0,o.jsx)(p,{isPresent:s,anchorX:g,root:m,children:t})),(0,o.jsx)(l.t.Provider,{value:y,children:t})};function h(){return new Map}var m=r(32082);let x=e=>e.key||"";function b(e){let t=[];return a.Children.forEach(e,e=>{(0,a.isValidElement)(e)&&t.push(e)}),t}let v=e=>{let{children:t,custom:r,initial:l=!0,onExitComplete:c,presenceAffectsLayout:u=!0,mode:d="sync",propagate:p=!1,anchorX:h="left",root:v}=e,[y,f]=(0,m.xQ)(p),w=(0,a.useMemo)(()=>b(t),[t]),j=p&&!y?[]:w.map(x),S=(0,a.useRef)(!0),N=(0,a.useRef)(w),E=(0,n.M)(()=>new Map),[A,C]=(0,a.useState)(w),[G,I]=(0,a.useState)(w);(0,i.E)(()=>{S.current=!1,N.current=w;for(let e=0;e<G.length;e++){let t=x(G[e]);j.includes(t)?E.delete(t):!0!==E.get(t)&&E.set(t,!1)}},[G,j.length,j.join("-")]);let P=[];if(w!==A){let e=[...w];for(let t=0;t<G.length;t++){let r=G[t],o=x(r);j.includes(o)||(e.splice(t,0,r),P.push(r))}return"wait"===d&&P.length&&(e=P),I(b(e)),C(w),null}let{forceRender:k}=(0,a.useContext)(s.L);return(0,o.jsx)(o.Fragment,{children:G.map(e=>{let t=x(e),a=(!p||!!y)&&(w===G||j.includes(t));return(0,o.jsx)(g,{isPresent:a,initial:(!S.current||!!l)&&void 0,custom:r,presenceAffectsLayout:u,mode:d,root:v,onExitComplete:a?void 0:()=>{if(!E.has(t))return;E.set(t,!0);let e=!0;E.forEach(t=>{t||(e=!1)}),e&&(null==k||k(),I(N.current),p&&(null==f||f()),c&&c())},anchorX:h,children:e},t)})})}}},e=>{e.O(0,[844,5236,6874,6766,3568,3289,1846,6560,8441,5964,7358],()=>e(e.s=12771)),_N_E=e.O()}]);