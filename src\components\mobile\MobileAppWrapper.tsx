"use client";

import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useAuth } from '../../hooks/use-auth';
import { isMobileApp, shouldSkipLandingPage } from '../../utils/mobileDetection';
import MobileSplashScreen from './MobileSplashScreen';

interface MobileAppWrapperProps {
  children: React.ReactNode;
}

export default function MobileAppWrapper({ children }: MobileAppWrapperProps) {
  const [showSplash, setShowSplash] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const { user, isLoading } = useAuth();

  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return;

    const initializeMobileApp = () => {
      const isMobile = isMobileApp();
      const shouldSkip = shouldSkipLandingPage();
      
      // If it's a mobile app and user is on the landing page
      if (isMobile && shouldSkip && pathname === '/') {
        setShowSplash(true);
      } else {
        setIsInitialized(true);
      }
    };

    // Small delay to ensure Capacitor is loaded
    const timer = setTimeout(initializeMobileApp, 100);
    
    return () => clearTimeout(timer);
  }, [pathname]);

  const handleSplashComplete = () => {
    setShowSplash(false);
    setIsInitialized(true);
  };

  // Show splash screen for mobile app users on landing page
  if (showSplash) {
    return <MobileSplashScreen onComplete={handleSplashComplete} />;
  }

  // Don't render children until initialized (prevents flash)
  if (!isInitialized) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-green-900 to-black flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400"></div>
      </div>
    );
  }

  return <>{children}</>;
}
