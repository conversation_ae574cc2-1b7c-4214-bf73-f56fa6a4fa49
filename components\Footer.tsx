"use client";

import { motion } from "framer-motion";
import {
    FiFacebook,
    FiInstagram,
    FiLinkedin,
    FiMail,
    FiPhone,
    FiTwitter,
} from "react-icons/fi";
import { BetterInterestLogo } from "../src/components/ui/BetterInterestLogo";

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-black border-t border-gray-800" data-oid="ob34wqn">
      <div
        className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12"
        data-oid="4fd49yb"
      >
        <div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          data-oid="gshg3r7"
        >
          {/* Brand Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="col-span-1 lg:col-span-2"
            data-oid="h:16t_v"
          >
            <div
              className="flex items-center mb-4"
              data-oid="u:9qpg-"
            >
              <BetterInterestLogo
                size="md"
                variant="gradient"
                showIcon={true}
                className="text-xl"
              />
            </div>
            <p
              className="text-gray-300 mb-6 max-w-md leading-relaxed"
              data-oid="e0_u3s5"
            >
              Empowering your financial future with smart savings solutions.
              Join thousands of users who trust Koja Save to help them achieve
              their financial goals.
            </p>
            <div className="flex space-x-4" data-oid="j0ie8hv">
              <motion.a
                href="#"
                whileHover={{ scale: 1.1 }}
                className="w-10 h-10 bg-gray-800 hover:bg-green-600 rounded-lg flex items-center justify-center transition-colors duration-300"
                data-oid="7bb8:rw"
              >
                <FiTwitter
                  className="text-gray-300 hover:text-white"
                  data-oid="obv.i7o"
                />
              </motion.a>
              <motion.a
                href="#"
                whileHover={{ scale: 1.1 }}
                className="w-10 h-10 bg-gray-800 hover:bg-green-600 rounded-lg flex items-center justify-center transition-colors duration-300"
                data-oid="-:c8ptj"
              >
                <FiFacebook
                  className="text-gray-300 hover:text-white"
                  data-oid="w-2_smv"
                />
              </motion.a>
              <motion.a
                href="#"
                whileHover={{ scale: 1.1 }}
                className="w-10 h-10 bg-gray-800 hover:bg-green-600 rounded-lg flex items-center justify-center transition-colors duration-300"
                data-oid="upe.pr2"
              >
                <FiInstagram
                  className="text-gray-300 hover:text-white"
                  data-oid="trovaml"
                />
              </motion.a>
              <motion.a
                href="#"
                whileHover={{ scale: 1.1 }}
                className="w-10 h-10 bg-gray-800 hover:bg-green-600 rounded-lg flex items-center justify-center transition-colors duration-300"
                data-oid=".q75g7f"
              >
                <FiLinkedin
                  className="text-gray-300 hover:text-white"
                  data-oid="98ayc.x"
                />
              </motion.a>
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            data-oid="4foq3ph"
          >
            <h3 className="text-white font-semibold mb-4" data-oid="xsjh0ci">
              Quick Links
            </h3>
            <ul className="space-y-2" data-oid="tys6_fm">
              <li data-oid="r:8qqjl">
                <a
                  href="#features"
                  className="text-gray-300 hover:text-green-400 transition-colors duration-300"
                  data-oid="9iam7aj"
                >
                  Features
                </a>
              </li>
              <li data-oid="-v_jwny">
                <a
                  href="#about"
                  className="text-gray-300 hover:text-green-400 transition-colors duration-300"
                  data-oid="17o9-23"
                >
                  About Us
                </a>
              </li>
              <li data-oid="0e1tu2r">
                <a
                  href="#contact"
                  className="text-gray-300 hover:text-green-400 transition-colors duration-300"
                  data-oid="xy5v:md"
                >
                  Contact
                </a>
              </li>
              <li data-oid="u204efy">
                <a
                  href="#"
                  className="text-gray-300 hover:text-green-400 transition-colors duration-300"
                  data-oid="2axq5on"
                >
                  Pricing
                </a>
              </li>
              <li data-oid="k50zi9l">
                <a
                  href="#"
                  className="text-gray-300 hover:text-green-400 transition-colors duration-300"
                  data-oid="1awzh_u"
                >
                  Blog
                </a>
              </li>
            </ul>
          </motion.div>

          {/* Support */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            data-oid="svl1061"
          >
            <h3 className="text-white font-semibold mb-4" data-oid="b8u2d5z">
              Support
            </h3>
            <ul className="space-y-2" data-oid="tgfbft8">
              <li data-oid="evzy5av">
                <a
                  href="#"
                  className="text-gray-300 hover:text-green-400 transition-colors duration-300"
                  data-oid="bzcpjoy"
                >
                  Help Center
                </a>
              </li>
              <li data-oid="2_jh6b9">
                <a
                  href="#"
                  className="text-gray-300 hover:text-green-400 transition-colors duration-300"
                  data-oid="b8x290_"
                >
                  Privacy Policy
                </a>
              </li>
              <li data-oid="4shlhf0">
                <a
                  href="#"
                  className="text-gray-300 hover:text-green-400 transition-colors duration-300"
                  data-oid="rdcg1fg"
                >
                  Terms of Service
                </a>
              </li>
              <li data-oid="thbuyjj">
                <a
                  href="#"
                  className="text-gray-300 hover:text-green-400 transition-colors duration-300"
                  data-oid="y4r:zem"
                >
                  Security
                </a>
              </li>
              <li data-oid="22f.7oy">
                <div
                  className="flex items-center space-x-2 text-gray-300"
                  data-oid="42mt.y2"
                >
                  <FiMail size={16} data-oid="lb1fowi" />
                  <span className="text-sm" data-oid="wbok165">
                    <EMAIL>
                  </span>
                </div>
              </li>
              <li data-oid="kyd_smt">
                <div
                  className="flex items-center space-x-2 text-gray-300"
                  data-oid="y.ly:8j"
                >
                  <FiPhone size={16} data-oid="a:3fv88" />
                  <span className="text-sm" data-oid="g4td7.p">
                    +****************
                  </span>
                </div>
              </li>
            </ul>
          </motion.div>
        </div>

        {/* Bottom Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="border-t border-gray-800 mt-12 pt-8"
          data-oid="d02f7-e"
        >
          <div
            className="flex flex-col md:flex-row justify-between items-center"
            data-oid="zojroiz"
          >
            <p
              className="text-gray-400 text-sm mb-4 md:mb-0"
              data-oid="vnvnxwy"
            >
              © {currentYear} Koja Save. All rights reserved.
            </p>
            <div
              className="flex items-center space-x-6 text-sm text-gray-400"
              data-oid="xwl9780"
            >
              <span data-oid="ijde0_6">Made with ❤️ for smart savers</span>
              <div className="flex items-center space-x-1" data-oid="14mn.-t">
                <div
                  className="w-2 h-2 bg-green-400 rounded-full animate-pulse"
                  data-oid="eb6p9yf"
                ></div>
                <span data-oid="cdic1yj">All systems operational</span>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </footer>
  );
}
