(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2210],{1147:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if(!e||"object"!=typeof e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&"[object Object]"===Object.prototype.toString.call(e)}},2767:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.noop=function(){}},4993:(e,t,r)=>{"use strict";var n=r(12115);"function"==typeof Object.is&&Object.is,n.useSyncExternalStore,n.useRef,n.useEffect,n.useMemo,n.useDebugValue},6115:(e,t,r)=>{"use strict";var n=r(12115),i=r(49033),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=i.useSyncExternalStore,l=n.useRef,c=n.useEffect,u=n.useMemo,s=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var f=l(null);if(null===f.current){var p={hasValue:!1,value:null};f.current=p}else p=f.current;var d=o(e,(f=u(function(){function e(e){if(!c){if(c=!0,o=e,e=n(e),void 0!==i&&p.hasValue){var t=p.value;if(i(t,e))return l=t}return l=e}if(t=l,a(o,e))return t;var r=n(e);return void 0!==i&&i(t,r)?(o=e,t):(o=e,l=r)}var o,l,c=!1,u=void 0===r?null:r;return[function(){return e(t())},null===u?void 0:function(){return e(u())}]},[t,r,n,i]))[0],f[1]);return c(function(){p.hasValue=!0,p.value=d},[d]),s(d),d}},6706:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(81571);t.sumBy=function(e,t){let r;if(!e||!e.length)return 0;null!=t&&(t=n.iteratee(t));for(let n=0;n<e.length;n++){let i=t?t(e[n]):e[n];void 0!==i&&(void 0===r?r=i:r+=i)}return r}},7547:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(23676),i=r(72465),a=r(10656),o=r(81571);t.uniqBy=function(e,t=i.identity){return a.isArrayLikeObject(e)?n.uniqBy(Array.from(e),o.iteratee(t)):[]}},8287:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isSymbol=function(e){return"symbol"==typeof e||e instanceof Symbol}},8870:function(e,t,r){var n;!function(i){"use strict";var a,o={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},l=!0,c="[DecimalError] ",u=c+"Invalid argument: ",s=c+"Exponent out of range: ",f=Math.floor,p=Math.pow,d=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,h=f(1286742750677284.5),y={};function v(e,t){var r,n,i,a,o,c,u,s,f=e.constructor,p=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),l?S(t,p):t;if(u=e.d,s=t.d,o=e.e,i=t.e,u=u.slice(),a=o-i){for(a<0?(n=u,a=-a,c=s.length):(n=s,i=o,c=u.length),a>(c=(o=Math.ceil(p/7))>c?o+1:c+1)&&(a=c,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for((c=u.length)-(a=s.length)<0&&(a=c,n=s,s=u,u=n),r=0;a;)r=(u[--a]=u[a]+s[a]+r)/1e7|0,u[a]%=1e7;for(r&&(u.unshift(r),++i),c=u.length;0==u[--c];)u.pop();return t.d=u,t.e=i,l?S(t,p):t}function m(e,t,r){if(e!==~~e||e<t||e>r)throw Error(u+e)}function g(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)(r=7-(n=e[t]+"").length)&&(a+=P(r)),a+=n;(r=7-(n=(o=e[t])+"").length)&&(a+=P(r))}else if(0===o)return"0";for(;o%10==0;)o/=10;return a+o}y.absoluteValue=y.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},y.comparedTo=y.cmp=function(e){var t,r,n,i;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(i=e.d.length)?n:i;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},y.decimalPlaces=y.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},y.dividedBy=y.div=function(e){return b(this,new this.constructor(e))},y.dividedToIntegerBy=y.idiv=function(e){var t=this.constructor;return S(b(this,new t(e),0,1),t.precision)},y.equals=y.eq=function(e){return!this.cmp(e)},y.exponent=function(){return O(this)},y.greaterThan=y.gt=function(e){return this.cmp(e)>0},y.greaterThanOrEqualTo=y.gte=function(e){return this.cmp(e)>=0},y.isInteger=y.isint=function(){return this.e>this.d.length-2},y.isNegative=y.isneg=function(){return this.s<0},y.isPositive=y.ispos=function(){return this.s>0},y.isZero=function(){return 0===this.s},y.lessThan=y.lt=function(e){return 0>this.cmp(e)},y.lessThanOrEqualTo=y.lte=function(e){return 1>this.cmp(e)},y.logarithm=y.log=function(e){var t,r=this.constructor,n=r.precision,i=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(a))throw Error(c+"NaN");if(this.s<1)throw Error(c+(this.s?"NaN":"-Infinity"));return this.eq(a)?new r(0):(l=!1,t=b(j(this,i),j(e,i),i),l=!0,S(t,n))},y.minus=y.sub=function(e){return e=new this.constructor(e),this.s==e.s?A(this,e):v(this,(e.s=-e.s,e))},y.modulo=y.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(c+"NaN");return this.s?(l=!1,t=b(this,e,0,1).times(e),l=!0,this.minus(t)):S(new r(this),n)},y.naturalExponential=y.exp=function(){return x(this)},y.naturalLogarithm=y.ln=function(){return j(this)},y.negated=y.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},y.plus=y.add=function(e){return e=new this.constructor(e),this.s==e.s?v(this,e):A(this,(e.s=-e.s,e))},y.precision=y.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(u+e);if(t=O(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},y.squareRoot=y.sqrt=function(){var e,t,r,n,i,a,o,u=this.constructor;if(this.s<1){if(!this.s)return new u(0);throw Error(c+"NaN")}for(e=O(this),l=!1,0==(i=Math.sqrt(+this))||i==1/0?(((t=g(this.d)).length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=f((e+1)/2)-(e<0||e%2),n=new u(t=i==1/0?"5e"+e:(t=i.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new u(i.toString()),i=o=(r=u.precision)+3;;)if(n=(a=n).plus(b(this,a,o+2)).times(.5),g(a.d).slice(0,o)===(t=g(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&"4999"==t){if(S(a,r+1,0),a.times(a).eq(this)){n=a;break}}else if("9999"!=t)break;o+=4}return l=!0,S(n,r)},y.times=y.mul=function(e){var t,r,n,i,a,o,c,u,s,f=this.constructor,p=this.d,d=(e=new f(e)).d;if(!this.s||!e.s)return new f(0);for(e.s*=this.s,r=this.e+e.e,(u=p.length)<(s=d.length)&&(a=p,p=d,d=a,o=u,u=s,s=o),a=[],n=o=u+s;n--;)a.push(0);for(n=s;--n>=0;){for(t=0,i=u+n;i>n;)c=a[i]+d[n]*p[i-n-1]+t,a[i--]=c%1e7|0,t=c/1e7|0;a[i]=(a[i]+t)%1e7|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,l?S(e,f.precision):e},y.toDecimalPlaces=y.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(m(e,0,1e9),void 0===t?t=n.rounding:m(t,0,8),S(r,e+O(r)+1,t))},y.toExponential=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=k(n,!0):(m(e,0,1e9),void 0===t?t=i.rounding:m(t,0,8),r=k(n=S(new i(n),e+1,t),!0,e+1)),r},y.toFixed=function(e,t){var r,n,i=this.constructor;return void 0===e?k(this):(m(e,0,1e9),void 0===t?t=i.rounding:m(t,0,8),r=k((n=S(new i(this),e+O(this)+1,t)).abs(),!1,e+O(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},y.toInteger=y.toint=function(){var e=this.constructor;return S(new e(this),O(this)+1,e.rounding)},y.toNumber=function(){return+this},y.toPower=y.pow=function(e){var t,r,n,i,o,u,s=this,p=s.constructor,d=+(e=new p(e));if(!e.s)return new p(a);if(!(s=new p(s)).s){if(e.s<1)throw Error(c+"Infinity");return s}if(s.eq(a))return s;if(n=p.precision,e.eq(a))return S(s,n);if(u=(t=e.e)>=(r=e.d.length-1),o=s.s,u){if((r=d<0?-d:d)<=0x1fffffffffffff){for(i=new p(a),t=Math.ceil(n/7+4),l=!1;r%2&&M((i=i.times(s)).d,t),0!==(r=f(r/2));)M((s=s.times(s)).d,t);return l=!0,e.s<0?new p(a).div(i):S(i,n)}}else if(o<0)throw Error(c+"NaN");return o=o<0&&1&e.d[Math.max(t,r)]?-1:1,s.s=1,l=!1,i=e.times(j(s,n+12)),l=!0,(i=x(i)).s=o,i},y.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return void 0===e?(r=O(i),n=k(i,r<=a.toExpNeg||r>=a.toExpPos)):(m(e,1,1e9),void 0===t?t=a.rounding:m(t,0,8),r=O(i=S(new a(i),e,t)),n=k(i,e<=r||r<=a.toExpNeg,e)),n},y.toSignificantDigits=y.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(m(e,1,1e9),void 0===t?t=r.rounding:m(t,0,8)),S(new r(this),e,t)},y.toString=y.valueOf=y.val=y.toJSON=function(){var e=O(this),t=this.constructor;return k(this,e<=t.toExpNeg||e>=t.toExpPos)};var b=function(){function e(e,t){var r,n=0,i=e.length;for(e=e.slice();i--;)r=e[i]*t+n,e[i]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var i,a;if(r!=n)a=r>n?1:-1;else for(i=a=0;i<r;i++)if(e[i]!=t[i]){a=e[i]>t[i]?1:-1;break}return a}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=+(e[r]<t[r]),e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,i,a,o){var l,u,s,f,p,d,h,y,v,m,g,b,x,w,P,j,E,A,k=n.constructor,M=n.s==i.s?1:-1,T=n.d,C=i.d;if(!n.s)return new k(n);if(!i.s)throw Error(c+"Division by zero");for(s=0,u=n.e-i.e,E=C.length,P=T.length,y=(h=new k(M)).d=[];C[s]==(T[s]||0);)++s;if(C[s]>(T[s]||0)&&--u,(b=null==a?a=k.precision:o?a+(O(n)-O(i))+1:a)<0)return new k(0);if(b=b/7+2|0,s=0,1==E)for(f=0,C=C[0],b++;(s<P||f)&&b--;s++)x=1e7*f+(T[s]||0),y[s]=x/C|0,f=x%C|0;else{for((f=1e7/(C[0]+1)|0)>1&&(C=e(C,f),T=e(T,f),E=C.length,P=T.length),w=E,m=(v=T.slice(0,E)).length;m<E;)v[m++]=0;(A=C.slice()).unshift(0),j=C[0],C[1]>=1e7/2&&++j;do f=0,(l=t(C,v,E,m))<0?(g=v[0],E!=m&&(g=1e7*g+(v[1]||0)),(f=g/j|0)>1?(f>=1e7&&(f=1e7-1),d=(p=e(C,f)).length,m=v.length,1==(l=t(p,v,d,m))&&(f--,r(p,E<d?A:C,d))):(0==f&&(l=f=1),p=C.slice()),(d=p.length)<m&&p.unshift(0),r(v,p,m),-1==l&&(m=v.length,(l=t(C,v,E,m))<1&&(f++,r(v,E<m?A:C,m))),m=v.length):0===l&&(f++,v=[0]),y[s++]=f,l&&v[0]?v[m++]=T[w]||0:(v=[T[w]],m=1);while((w++<P||void 0!==v[0])&&b--)}return y[0]||y.shift(),h.e=u,S(h,o?a+O(h)+1:a)}}();function x(e,t){var r,n,i,o,c,u=0,f=0,d=e.constructor,h=d.precision;if(O(e)>16)throw Error(s+O(e));if(!e.s)return new d(a);for(null==t?(l=!1,c=h):c=t,o=new d(.03125);e.abs().gte(.1);)e=e.times(o),f+=5;for(c+=Math.log(p(2,f))/Math.LN10*2+5|0,r=n=i=new d(a),d.precision=c;;){if(n=S(n.times(e),c),r=r.times(++u),g((o=i.plus(b(n,r,c))).d).slice(0,c)===g(i.d).slice(0,c)){for(;f--;)i=S(i.times(i),c);return d.precision=h,null==t?(l=!0,S(i,h)):i}i=o}}function O(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function w(e,t,r){if(t>e.LN10.sd())throw l=!0,r&&(e.precision=r),Error(c+"LN10 precision limit exceeded");return S(new e(e.LN10),t)}function P(e){for(var t="";e--;)t+="0";return t}function j(e,t){var r,n,i,o,u,s,f,p,d,h=1,y=e,v=y.d,m=y.constructor,x=m.precision;if(y.s<1)throw Error(c+(y.s?"NaN":"-Infinity"));if(y.eq(a))return new m(0);if(null==t?(l=!1,p=x):p=t,y.eq(10))return null==t&&(l=!0),w(m,p);if(m.precision=p+=10,n=(r=g(v)).charAt(0),!(15e14>Math.abs(o=O(y))))return f=w(m,p+2,x).times(o+""),y=j(new m(n+"."+r.slice(1)),p-10).plus(f),m.precision=x,null==t?(l=!0,S(y,x)):y;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=g((y=y.times(e)).d)).charAt(0),h++;for(o=O(y),n>1?(y=new m("0."+r),o++):y=new m(n+"."+r.slice(1)),s=u=y=b(y.minus(a),y.plus(a),p),d=S(y.times(y),p),i=3;;){if(u=S(u.times(d),p),g((f=s.plus(b(u,new m(i),p))).d).slice(0,p)===g(s.d).slice(0,p))return s=s.times(2),0!==o&&(s=s.plus(w(m,p+2,x).times(o+""))),s=b(s,new m(h),p),m.precision=x,null==t?(l=!0,S(s,x)):s;s=f,i+=2}}function E(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(i=t.length;48===t.charCodeAt(i-1);)--i;if(t=t.slice(n,i)){if(i-=n,e.e=f((r=r-n-1)/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=7;n<i;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),l&&(e.e>h||e.e<-h))throw Error(s+r)}else e.s=0,e.e=0,e.d=[0];return e}function S(e,t,r){var n,i,a,o,c,u,d,y,v=e.d;for(o=1,a=v[0];a>=10;a/=10)o++;if((n=t-o)<0)n+=7,i=t,d=v[y=0];else{if((y=Math.ceil((n+1)/7))>=(a=v.length))return e;for(o=1,d=a=v[y];a>=10;a/=10)o++;n%=7,i=n-7+o}if(void 0!==r&&(c=d/(a=p(10,o-i-1))%10|0,u=t<0||void 0!==v[y+1]||d%a,u=r<4?(c||u)&&(0==r||r==(e.s<0?3:2)):c>5||5==c&&(4==r||u||6==r&&(n>0?i>0?d/p(10,o-i):0:v[y-1])%10&1||r==(e.s<0?8:7))),t<1||!v[0])return u?(a=O(e),v.length=1,t=t-a-1,v[0]=p(10,(7-t%7)%7),e.e=f(-t/7)||0):(v.length=1,v[0]=e.e=e.s=0),e;if(0==n?(v.length=y,a=1,y--):(v.length=y+1,a=p(10,7-n),v[y]=i>0?(d/p(10,o-i)%p(10,i)|0)*a:0),u)for(;;)if(0==y){1e7==(v[0]+=a)&&(v[0]=1,++e.e);break}else{if(v[y]+=a,1e7!=v[y])break;v[y--]=0,a=1}for(n=v.length;0===v[--n];)v.pop();if(l&&(e.e>h||e.e<-h))throw Error(s+O(e));return e}function A(e,t){var r,n,i,a,o,c,u,s,f,p,d=e.constructor,h=d.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new d(e),l?S(t,h):t;if(u=e.d,p=t.d,n=t.e,s=e.e,u=u.slice(),o=s-n){for((f=o<0)?(r=u,o=-o,c=p.length):(r=p,n=s,c=u.length),o>(i=Math.max(Math.ceil(h/7),c)+2)&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for((f=(i=u.length)<(c=p.length))&&(c=i),i=0;i<c;i++)if(u[i]!=p[i]){f=u[i]<p[i];break}o=0}for(f&&(r=u,u=p,p=r,t.s=-t.s),c=u.length,i=p.length-c;i>0;--i)u[c++]=0;for(i=p.length;i>o;){if(u[--i]<p[i]){for(a=i;a&&0===u[--a];)u[a]=1e7-1;--u[a],u[i]+=1e7}u[i]-=p[i]}for(;0===u[--c];)u.pop();for(;0===u[0];u.shift())--n;return u[0]?(t.d=u,t.e=n,l?S(t,h):t):new d(0)}function k(e,t,r){var n,i=O(e),a=g(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+P(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+P(-i-1)+a,r&&(n=r-o)>0&&(a+=P(n))):i>=o?(a+=P(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+P(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=P(n))),e.s<0?"-"+a:a}function M(e,t){if(e.length>t)return e.length=t,!0}function T(e){if(!e||"object"!=typeof e)throw Error(c+"Object expected");var t,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if(void 0!==(n=e[r=i[t]]))if(f(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(u+r+": "+n);if(void 0!==(n=e[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(u+r+": "+n);return this}(o=function e(t){var r,n,i;function a(e){if(!(this instanceof a))return new a(e);if(this.constructor=a,e instanceof a){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(u+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return E(this,e.toString())}if("string"!=typeof e)throw Error(u+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,d.test(e))E(this,e);else throw Error(u+e)}if(a.prototype=y,a.ROUND_UP=0,a.ROUND_DOWN=1,a.ROUND_CEIL=2,a.ROUND_FLOOR=3,a.ROUND_HALF_UP=4,a.ROUND_HALF_DOWN=5,a.ROUND_HALF_EVEN=6,a.ROUND_HALF_CEIL=7,a.ROUND_HALF_FLOOR=8,a.clone=e,a.config=a.set=T,void 0===t&&(t={}),t)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)t.hasOwnProperty(n=i[r++])||(t[n]=this[n]);return a.config(t),a}(o)).default=o.Decimal=o,a=new o(1),void 0===(n=(function(){return o}).call(t,r,t,e))||(e.exports=n)}(0)},10656:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(68179),i=r(99279);t.isArrayLikeObject=function(e){return i.isObjectLike(e)&&n.isArrayLike(e)}},10921:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(27040),i=r(14545),a=r(46200),o=r(64072);t.get=function e(t,r,l){if(null==t)return l;switch(typeof r){case"string":{if(n.isUnsafeProperty(r))return l;let a=t[r];if(void 0===a)if(i.isDeepKey(r))return e(t,o.toPath(r),l);else return l;return a}case"number":case"symbol":{"number"==typeof r&&(r=a.toKey(r));let e=t[r];if(void 0===e)return l;return e}default:{if(Array.isArray(r)){var c=t,u=r,s=l;if(0===u.length)return s;let e=c;for(let t=0;t<u.length;t++){if(null==e||n.isUnsafeProperty(u[t]))return s;e=e[u[t]]}return void 0===e?s:e}if(r=Object.is(r?.valueOf(),-0)?"-0":String(r),n.isUnsafeProperty(r))return l;let e=t[r];if(void 0===e)return l;return e}}}},12429:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(44117);t.cloneDeep=function(e){return n.cloneDeepWithImpl(e,void 0,e,new Map,void 0)}},14545:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isDeepKey=function(e){switch(typeof e){case"number":case"symbol":return!1;case"string":return e.includes(".")||e.includes("[")||e.includes("]")}}},14804:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98132),i=r(12429);t.matches=function(e){return e=i.cloneDeep(e),t=>n.isMatch(t,e)}},14986:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toArray=function(e){return Array.isArray(e)?e:Array.from(e)}},15160:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getTag=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}},19452:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isLength=function(e){return Number.isSafeInteger(e)&&e>=0}},20241:(e,t,r)=>{e.exports=r(22434).sortBy},20400:(e,t,r)=>{e.exports=r(82962).throttle},20697:(e,t,r)=>{e.exports=r(73054).omit},22188:(e,t,r)=>{e.exports=r(85252).isEqual},22434:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(47064),i=r(55998),a=r(64373);t.sortBy=function(e,...t){let r=t.length;return r>1&&a.isIterateeCall(e,t[0],t[1])?t=[]:r>2&&a.isIterateeCall(t[0],t[1],t[2])&&(t=[t[0]]),n.orderBy(e,i.flatten(t),["asc"])}},22436:(e,t,r)=>{"use strict";var n=r(12115),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,o=n.useEffect,l=n.useLayoutEffect,c=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,s=n[1];return l(function(){i.value=r,i.getSnapshot=t,u(i)&&s({inst:i})},[e,r,t]),o(function(){return u(i)&&s({inst:i}),e(function(){u(i)&&s({inst:i})})},[e]),c(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:s},22520:(e,t,r)=>{"use strict";var n=r(44134).Buffer;Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let i=r(1147),a=r(98221),o=r(15160),l=r(42721),c=r(83616);t.isEqualWith=function(e,t,r){return function e(t,r,u,s,f,p,d){let h=d(t,r,u,s,f,p);if(void 0!==h)return h;if(typeof t==typeof r)switch(typeof t){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return t===r;case"number":return t===r||Object.is(t,r)}return function t(r,u,s,f){if(Object.is(r,u))return!0;let p=o.getTag(r),d=o.getTag(u);if(p===l.argumentsTag&&(p=l.objectTag),d===l.argumentsTag&&(d=l.objectTag),p!==d)return!1;switch(p){case l.stringTag:return r.toString()===u.toString();case l.numberTag:{let e=r.valueOf(),t=u.valueOf();return c.eq(e,t)}case l.booleanTag:case l.dateTag:case l.symbolTag:return Object.is(r.valueOf(),u.valueOf());case l.regexpTag:return r.source===u.source&&r.flags===u.flags;case l.functionTag:return r===u}let h=(s=s??new Map).get(r),y=s.get(u);if(null!=h&&null!=y)return h===u;s.set(r,u),s.set(u,r);try{switch(p){case l.mapTag:if(r.size!==u.size)return!1;for(let[t,n]of r.entries())if(!u.has(t)||!e(n,u.get(t),t,r,u,s,f))return!1;return!0;case l.setTag:{if(r.size!==u.size)return!1;let t=Array.from(r.values()),n=Array.from(u.values());for(let i=0;i<t.length;i++){let a=t[i],o=n.findIndex(t=>e(a,t,void 0,r,u,s,f));if(-1===o)return!1;n.splice(o,1)}return!0}case l.arrayTag:case l.uint8ArrayTag:case l.uint8ClampedArrayTag:case l.uint16ArrayTag:case l.uint32ArrayTag:case l.bigUint64ArrayTag:case l.int8ArrayTag:case l.int16ArrayTag:case l.int32ArrayTag:case l.bigInt64ArrayTag:case l.float32ArrayTag:case l.float64ArrayTag:if(void 0!==n&&n.isBuffer(r)!==n.isBuffer(u)||r.length!==u.length)return!1;for(let t=0;t<r.length;t++)if(!e(r[t],u[t],t,r,u,s,f))return!1;return!0;case l.arrayBufferTag:if(r.byteLength!==u.byteLength)return!1;return t(new Uint8Array(r),new Uint8Array(u),s,f);case l.dataViewTag:if(r.byteLength!==u.byteLength||r.byteOffset!==u.byteOffset)return!1;return t(new Uint8Array(r),new Uint8Array(u),s,f);case l.errorTag:return r.name===u.name&&r.message===u.message;case l.objectTag:{if(!(t(r.constructor,u.constructor,s,f)||i.isPlainObject(r)&&i.isPlainObject(u)))return!1;let n=[...Object.keys(r),...a.getSymbols(r)],o=[...Object.keys(u),...a.getSymbols(u)];if(n.length!==o.length)return!1;for(let t=0;t<n.length;t++){let i=n[t],a=r[i];if(!Object.hasOwn(u,i))return!1;let o=u[i];if(!e(a,o,i,r,u,s,f))return!1}return!0}default:return!1}}finally{s.delete(r),s.delete(u)}}(t,r,p,d)}(e,t,void 0,void 0,void 0,void 0,r)}},23676:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.uniqBy=function(e,t){let r=new Map;for(let n=0;n<e.length;n++){let i=e[n],a=t(i);r.has(a)||r.set(a,i)}return Array.from(r.values())}},24517:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98132),i=r(46200),a=r(37298),o=r(10921),l=r(93205);t.matchesProperty=function(e,t){switch(typeof e){case"object":Object.is(e?.valueOf(),-0)&&(e="-0");break;case"number":e=i.toKey(e)}return t=a.cloneDeep(t),function(r){let i=o.get(r,e);return void 0===i?l.has(r,e):void 0===t?void 0===i:n.isMatch(i,t)}}},27040:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isUnsafeProperty=function(e){return"__proto__"===e}},29738:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(44117),i=r(42721);t.cloneDeepWith=function(e,t){return n.cloneDeepWith(e,(r,a,o,l)=>{let c=t?.(r,a,o,l);if(null!=c)return c;if("object"==typeof e)switch(Object.prototype.toString.call(e)){case i.numberTag:case i.stringTag:case i.booleanTag:{let t=new e.constructor(e?.valueOf());return n.copyProperties(t,e),t}case i.argumentsTag:{let t={};return n.copyProperties(t,e),t.length=e.length,t[Symbol.iterator]=e[Symbol.iterator],t}default:return}})}},31901:(e,t,r)=>{e.exports=r(72605).minBy},32417:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Area:()=>xj,AreaChart:()=>Pa,Bar:()=>mo,BarChart:()=>wn,Brush:()=>gS,CartesianAxis:()=>bg,CartesianGrid:()=>bL,Cell:()=>de,ComposedChart:()=>Ps,Cross:()=>i_,Curve:()=>iT,Customized:()=>dH,DefaultLegendContent:()=>eM,DefaultTooltipContent:()=>nZ,Dot:()=>d1,ErrorBar:()=>v$,Funnel:()=>PW,FunnelChart:()=>PX,Global:()=>n4,Label:()=>d_,LabelList:()=>dV,Layer:()=>q,Legend:()=>nX,Line:()=>b8,LineChart:()=>wt,Pie:()=>yq,PieChart:()=>wf,PolarAngleAxis:()=>h0,PolarGrid:()=>hx,PolarRadiusAxis:()=>hF,Polygon:()=>dQ,Radar:()=>vx,RadarChart:()=>Pt,RadialBar:()=>m4,RadialBarChart:()=>Pc,Rectangle:()=>ae,ReferenceArea:()=>bi,ReferenceDot:()=>g6,ReferenceLine:()=>gJ,ResponsiveContainer:()=>p9,Sankey:()=>w7,Scatter:()=>x6,ScatterChart:()=>Pn,Sector:()=>ao,SunburstChart:()=>Px,Surface:()=>V,Symbols:()=>eE,Text:()=>dS,Tooltip:()=>p5,Trapezoid:()=>yg,Treemap:()=>w_,XAxis:()=>Oa,YAxis:()=>Od,ZAxis:()=>xK,getNiceTickValues:()=>ul,useActiveTooltipDataPoints:()=>y0,useActiveTooltipLabel:()=>yZ,useChartHeight:()=>nj,useChartWidth:()=>nP,useOffset:()=>yJ,usePlotArea:()=>yQ});var n,i,a,o,l,c,u,s,f={};r.r(f),r.d(f,{scaleBand:()=>av,scaleDiverging:()=>function e(){var t=oT(cX()(op));return t.copy=function(){return c$(t,e())},as.apply(t,arguments)},scaleDivergingLog:()=>function e(){var t=oz(cX()).domain([.1,1,10]);return t.copy=function(){return c$(t,e()).base(t.base())},as.apply(t,arguments)},scaleDivergingPow:()=>cH,scaleDivergingSqrt:()=>cq,scaleDivergingSymlog:()=>function e(){var t=oU(cX());return t.copy=function(){return c$(t,e()).constant(t.constant())},as.apply(t,arguments)},scaleIdentity:()=>function e(t){var r;function n(e){return null==e||isNaN(e*=1)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,os),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,os):[0,1],oT(n)},scaleImplicit:()=>ah,scaleLinear:()=>oC,scaleLog:()=>function e(){let t=oz(om()).domain([1,10]);return t.copy=()=>ov(t,e()).base(t.base()),au.apply(t,arguments),t},scaleOrdinal:()=>ay,scalePoint:()=>am,scalePow:()=>oH,scaleQuantile:()=>function e(){var t,r=[],n=[],i=[];function a(){var e=0,t=Math.max(1,n.length);for(i=Array(t-1);++e<t;)i[e-1]=function(e,t,r=aM){if(!(!(n=e.length)||isNaN(t*=1))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e);return o+(r(e[a+1],a+1,e)-o)*(i-a)}}(r,e/t);return o}function o(e){return null==e||isNaN(e*=1)?t:n[aC(i,e)]}return o.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?i[t-1]:r[0],t<i.length?i[t]:r[r.length-1]]},o.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t*=1)||r.push(t);return r.sort(aE),a()},o.range=function(e){return arguments.length?(n=Array.from(e),a()):n.slice()},o.unknown=function(e){return arguments.length?(t=e,o):t},o.quantiles=function(){return i.slice()},o.copy=function(){return e().domain(r).range(n).unknown(t)},au.apply(o,arguments)},scaleQuantize:()=>function e(){var t,r=0,n=1,i=1,a=[.5],o=[0,1];function l(e){return null!=e&&e<=e?o[aC(a,e,0,i)]:t}function c(){var e=-1;for(a=Array(i);++e<i;)a[e]=((e+1)*n-(e-i)*r)/(i+1);return l}return l.domain=function(e){return arguments.length?([r,n]=e,r*=1,n*=1,c()):[r,n]},l.range=function(e){return arguments.length?(i=(o=Array.from(e)).length-1,c()):o.slice()},l.invertExtent=function(e){var t=o.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,a[0]]:t>=i?[a[i-1],n]:[a[t-1],a[t]]},l.unknown=function(e){return arguments.length&&(t=e),l},l.thresholds=function(){return a.slice()},l.copy=function(){return e().domain([r,n]).range(o).unknown(t)},au.apply(oT(l),arguments)},scaleRadial:()=>function e(){var t,r=og(),n=[0,1],i=!1;function a(e){var n,a=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(a)?t:i?Math.round(a):a}return a.invert=function(e){return r.invert(oY(e))},a.domain=function(e){return arguments.length?(r.domain(e),a):r.domain()},a.range=function(e){return arguments.length?(r.range((n=Array.from(e,os)).map(oY)),a):n.slice()},a.rangeRound=function(e){return a.range(e).round(!0)},a.round=function(e){return arguments.length?(i=!!e,a):i},a.clamp=function(e){return arguments.length?(r.clamp(e),a):r.clamp()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e(r.domain(),n).round(i).clamp(r.clamp()).unknown(t)},au.apply(a,arguments),oT(a)},scaleSequential:()=>function e(){var t=oT(cU()(op));return t.copy=function(){return c$(t,e())},as.apply(t,arguments)},scaleSequentialLog:()=>function e(){var t=oz(cU()).domain([1,10]);return t.copy=function(){return c$(t,e()).base(t.base())},as.apply(t,arguments)},scaleSequentialPow:()=>cW,scaleSequentialQuantile:()=>function e(){var t=[],r=op;function n(e){if(null!=e&&!isNaN(e*=1))return r((aC(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r*=1)||t.push(r);return t.sort(aE),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t*=1)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n*=1)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t*=1))){if(t<=0||n<2)return oZ(e);if(t>=1)return oG(e);var n,i=(n-1)*t,a=Math.floor(i),o=oG((function e(t,r,n=0,i=1/0,a){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(t.length-1,i)),!(n<=r&&r<=i))return t;for(a=void 0===a?oJ:function(e=aE){if(e===aE)return oJ;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(a);i>n;){if(i-n>600){let o=i-n+1,l=r-n+1,c=Math.log(o),u=.5*Math.exp(2*c/3),s=.5*Math.sqrt(c*u*(o-u)/o)*(l-o/2<0?-1:1),f=Math.max(n,Math.floor(r-l*u/o+s)),p=Math.min(i,Math.floor(r+(o-l)*u/o+s));e(t,r,f,p,a)}let o=t[r],l=n,c=i;for(oQ(t,n,r),a(t[i],o)>0&&oQ(t,n,i);l<c;){for(oQ(t,l,c),++l,--c;0>a(t[l],o);)++l;for(;a(t[c],o)>0;)--c}0===a(t[n],o)?oQ(t,n,c):oQ(t,++c,i),c<=r&&(n=c+1),r<=c&&(i=c-1)}return t})(e,a).subarray(0,a+1));return o+(oZ(e.subarray(a+1))-o)*(i-a)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},as.apply(n,arguments)},scaleSequentialSqrt:()=>cV,scaleSequentialSymlog:()=>function e(){var t=oU(cU());return t.copy=function(){return c$(t,e()).constant(t.constant())},as.apply(t,arguments)},scaleSqrt:()=>oq,scaleSymlog:()=>function e(){var t=oU(om());return t.copy=function(){return ov(t,e()).constant(t.constant())},au.apply(t,arguments)},scaleThreshold:()=>function e(){var t,r=[.5],n=[0,1],i=1;function a(e){return null!=e&&e<=e?n[aC(r,e,0,i)]:t}return a.domain=function(e){return arguments.length?(i=Math.min((r=Array.from(e)).length,n.length-1),a):r.slice()},a.range=function(e){return arguments.length?(n=Array.from(e),i=Math.min(r.length,n.length-1),a):n.slice()},a.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e().domain(r).range(n).unknown(t)},au.apply(a,arguments)},scaleTime:()=>cB,scaleUtc:()=>cF,tickFormat:()=>oM});var p=r(12115);function d(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=function e(t){var r,n,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t)if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(n=e(t[r]))&&(i&&(i+=" "),i+=n)}else for(n in t)t[n]&&(i&&(i+=" "),i+=n);return i}(e))&&(n&&(n+=" "),n+=t);return n}var h=r(95672),y=r.n(h),v=r(53588),m=e=>0===e?0:e>0?1:-1,g=e=>"number"==typeof e&&e!=+e,b=e=>"string"==typeof e&&e.indexOf("%")===e.length-1,x=e=>("number"==typeof e||e instanceof Number)&&!g(e),O=e=>x(e)||"string"==typeof e,w=0,P=e=>{var t=++w;return"".concat(e||"").concat(t)},j=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!x(e)&&"string"!=typeof e)return n;if(b(e)){if(null==t)return n;var a=e.indexOf("%");r=t*parseFloat(e.slice(0,a))/100}else r=+e;return g(r)&&(r=n),i&&null!=t&&r>t&&(r=t),r},E=e=>{if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++)if(r[e[n]])return!0;else r[e[n]]=!0;return!1},S=(e,t)=>x(e)&&x(t)?r=>e+r*(t-e):()=>t;function A(e,t,r){return x(e)&&x(t)?e+r*(t-e):t}function k(e,t,r){if(e&&e.length)return e.find(e=>e&&("function"==typeof t?t(e):y()(e,t))===r)}var M=e=>null==e?e:"".concat(e.charAt(0).toUpperCase()).concat(e.slice(1)),T=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],C=["points","pathLength"],D={svg:["viewBox","children"],polygon:C,polyline:C},N=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],_=(e,t)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,p.isValidElement)(e)&&(r=e.props),"object"!=typeof r&&"function"!=typeof r)return null;var n={};return Object.keys(r).forEach(e=>{N.includes(e)&&(n[e]=t||(t=>r[e](r,t)))}),n},I=(e,t,r)=>{if(null===e||"object"!=typeof e&&"function"!=typeof e)return null;var n=null;return Object.keys(e).forEach(i=>{var a=e[i];N.includes(i)&&"function"==typeof a&&(n||(n={}),n[i]=e=>(a(t,r,e),null))}),n},L=e=>"string"==typeof e?e:e?e.displayName||e.name||"Component":"",R=null,K=null,z=e=>{if(e===R&&Array.isArray(K))return K;var t=[];return p.Children.forEach(e,e=>{null==e||((0,v.zv)(e)?t=t.concat(z(e.props.children)):t.push(e))}),K=t,R=e,t};function B(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(e=>L(e)):[L(t)],z(e).forEach(e=>{var t=y()(e,"type.displayName")||y()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}var F=e=>!e||"object"!=typeof e||!("clipDot"in e)||!!e.clipDot,U=(e,t,r)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,p.isValidElement)(e)&&(n=e.props),"object"!=typeof n&&"function"!=typeof n)return null;var i={};return Object.keys(n).forEach(e=>{var a;((e,t,r,n)=>{var i,a=null!=(i=n&&(null==D?void 0:D[n]))?i:[];return t.startsWith("data-")||"function"!=typeof e&&(n&&a.includes(t)||T.includes(t))||r&&N.includes(t)})(null==(a=n)?void 0:a[e],e,t,r)&&(i[e]=n[e])}),i},$=["children","width","height","viewBox","className","style","title","desc"];function W(){return(W=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var V=(0,p.forwardRef)((e,t)=>{var{children:r,width:n,height:i,viewBox:a,className:o,style:l,title:c,desc:u}=e,s=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,$),f=a||{width:n,height:i,x:0,y:0},h=d("recharts-surface",o);return p.createElement("svg",W({},U(s,!0,"svg"),{className:h,width:n,height:i,style:l,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height),ref:t}),p.createElement("title",null,c),p.createElement("desc",null,u),r)}),X=["children","className"];function H(){return(H=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var q=p.forwardRef((e,t)=>{var{children:r,className:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,X),a=d("recharts-layer",n);return p.createElement("g",H({className:a},U(i,!0),{ref:t}),r)}),Y=r(47650),G=(0,p.createContext)(null);let Z=Math.cos,J=Math.sin,Q=Math.sqrt,ee=Math.PI,et=2*ee,er={draw(e,t){let r=Q(t/ee);e.moveTo(r,0),e.arc(0,0,r,0,et)}},en=Q(1/3),ei=2*en,ea=J(ee/10)/J(7*ee/10),eo=J(et/10)*ea,el=-Z(et/10)*ea,ec=Q(3),eu=Q(3)/2,es=1/Q(12),ef=(es/2+1)*3;function ep(e){return function(){return e}}let ed=Math.PI,eh=2*ed,ey=eh-1e-6;function ev(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class em{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?ev:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return ev;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,i,a){this._append`C${+e},${+t},${+r},${+n},${this._x1=+i},${this._y1=+a}`}arcTo(e,t,r,n,i){if(e*=1,t*=1,r*=1,n*=1,(i*=1)<0)throw Error(`negative radius: ${i}`);let a=this._x1,o=this._y1,l=r-e,c=n-t,u=a-e,s=o-t,f=u*u+s*s;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(f>1e-6)if(Math.abs(s*l-c*u)>1e-6&&i){let p=r-a,d=n-o,h=l*l+c*c,y=Math.sqrt(h),v=Math.sqrt(f),m=i*Math.tan((ed-Math.acos((h+f-(p*p+d*d))/(2*y*v)))/2),g=m/v,b=m/y;Math.abs(g-1)>1e-6&&this._append`L${e+g*u},${t+g*s}`,this._append`A${i},${i},0,0,${+(s*p>u*d)},${this._x1=e+b*l},${this._y1=t+b*c}`}else this._append`L${this._x1=e},${this._y1=t}`}arc(e,t,r,n,i,a){if(e*=1,t*=1,r*=1,a=!!a,r<0)throw Error(`negative radius: ${r}`);let o=r*Math.cos(n),l=r*Math.sin(n),c=e+o,u=t+l,s=1^a,f=a?n-i:i-n;null===this._x1?this._append`M${c},${u}`:(Math.abs(this._x1-c)>1e-6||Math.abs(this._y1-u)>1e-6)&&this._append`L${c},${u}`,r&&(f<0&&(f=f%eh+eh),f>ey?this._append`A${r},${r},0,1,${s},${e-o},${t-l}A${r},${r},0,1,${s},${this._x1=c},${this._y1=u}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=ed)},${s},${this._x1=e+r*Math.cos(i)},${this._y1=t+r*Math.sin(i)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function eg(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new em(t)}em.prototype,Q(3),Q(3);var eb=["type","size","sizeType"];function ex(){return(ex=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function eO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ew(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eO(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eO(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var eP={symbolCircle:er,symbolCross:{draw(e,t){let r=Q(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=Q(t/ei),n=r*en;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=Q(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=Q(.8908130915292852*t),n=eo*r,i=el*r;e.moveTo(0,-r),e.lineTo(n,i);for(let t=1;t<5;++t){let a=et*t/5,o=Z(a),l=J(a);e.lineTo(l*r,-o*r),e.lineTo(o*n-l*i,l*n+o*i)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-Q(t/(3*ec));e.moveTo(0,2*r),e.lineTo(-ec*r,-r),e.lineTo(ec*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=Q(t/ef),n=r/2,i=r*es,a=r*es+r,o=-n;e.moveTo(n,i),e.lineTo(n,a),e.lineTo(o,a),e.lineTo(-.5*n-eu*i,eu*n+-.5*i),e.lineTo(-.5*n-eu*a,eu*n+-.5*a),e.lineTo(-.5*o-eu*a,eu*o+-.5*a),e.lineTo(-.5*n+eu*i,-.5*i-eu*n),e.lineTo(-.5*n+eu*a,-.5*a-eu*n),e.lineTo(-.5*o+eu*a,-.5*a-eu*o),e.closePath()}}},ej=Math.PI/180,eE=e=>{var{type:t="circle",size:r=64,sizeType:n="area"}=e,i=ew(ew({},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,eb)),{},{type:t,size:r,sizeType:n}),{className:a,cx:o,cy:l}=i,c=U(i,!0);return o===+o&&l===+l&&r===+r?p.createElement("path",ex({},c,{className:d("recharts-symbols",a),transform:"translate(".concat(o,", ").concat(l,")"),d:(()=>{var e=eP["symbol".concat(M(t))]||er;return(function(e,t){let r=null,n=eg(i);function i(){let i;if(r||(r=i=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),i)return r=null,i+""||null}return e="function"==typeof e?e:ep(e||er),t="function"==typeof t?t:ep(void 0===t?64:+t),i.type=function(t){return arguments.length?(e="function"==typeof t?t:ep(t),i):e},i.size=function(e){return arguments.length?(t="function"==typeof e?e:ep(+e),i):t},i.context=function(e){return arguments.length?(r=null==e?null:e,i):r},i})().type(e).size(((e,t,r)=>{if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*ej;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.tan(n)**2);case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}})(r,n,t))()})()})):null};function eS(){return(eS=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function eA(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ek(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}eE.registerSymbol=(e,t)=>{eP["symbol".concat(M(e))]=t};class eM extends p.PureComponent{renderIcon(e,t){var{inactiveColor:r}=this.props,n=32/6,i=32/3,a=e.inactive?r:e.color,o=null!=t?t:e.type;if("none"===o)return null;if("plainline"===o)return p.createElement("line",{strokeWidth:4,fill:"none",stroke:a,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===o)return p.createElement("path",{strokeWidth:4,fill:"none",stroke:a,d:"M0,".concat(16,"h").concat(i,"\n            A").concat(n,",").concat(n,",0,1,1,").concat(2*i,",").concat(16,"\n            H").concat(32,"M").concat(2*i,",").concat(16,"\n            A").concat(n,",").concat(n,",0,1,1,").concat(i,",").concat(16),className:"recharts-legend-icon"});if("rect"===o)return p.createElement("path",{stroke:"none",fill:a,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(p.isValidElement(e.legendIcon)){var l=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eA(Object(r),!0).forEach(function(t){ek(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eA(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return delete l.legendIcon,p.cloneElement(e.legendIcon,l)}return p.createElement(eE,{fill:a,cx:16,cy:16,size:32,sizeType:"diameter",type:o})}renderItems(){var{payload:e,iconSize:t,layout:r,formatter:n,inactiveColor:i,iconType:a}=this.props,o={x:0,y:0,width:32,height:32},l={display:"horizontal"===r?"inline-block":"block",marginRight:10},c={display:"inline-block",verticalAlign:"middle",marginRight:4};return e.map((e,r)=>{var u=e.formatter||n,s=d({"recharts-legend-item":!0,["legend-item-".concat(r)]:!0,inactive:e.inactive});if("none"===e.type)return null;var f=e.inactive?i:e.color,h=u?u(e.value,e,r):e.value;return p.createElement("li",eS({className:s,style:l,key:"legend-item-".concat(r)},I(this.props,e,r)),p.createElement(V,{width:t,height:t,viewBox:o,style:c,"aria-label":"".concat(h," legend icon")},this.renderIcon(e,a)),p.createElement("span",{className:"recharts-legend-item-text",style:{color:f}},h))})}render(){var{payload:e,layout:t,align:r}=this.props;return e&&e.length?p.createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===t?r:"left"}},this.renderItems()):null}}ek(eM,"displayName","Legend"),ek(eM,"defaultProps",{align:"center",iconSize:14,inactiveColor:"#ccc",layout:"horizontal",verticalAlign:"middle"});var eT=r(60512),eC=r.n(eT);function eD(e,t,r){return!0===t?eC()(e,r):"function"==typeof t?eC()(e,t):e}var eN=r(45643),e_=(0,p.createContext)(null),eI=e=>e,eL=()=>{var e=(0,p.useContext)(e_);return e?e.store.dispatch:eI},eR=()=>{},eK=()=>eR,ez=(e,t)=>e===t;function eB(e){var t=(0,p.useContext)(e_);return(0,eN.useSyncExternalStoreWithSelector)(t?t.subscription.addNestedSub:eK,t?t.store.getState:eR,t?t.store.getState:eR,t?e:eR,ez)}var eF=e=>Array.isArray(e)?e:[e],eU=0,e$=class{revision=eU;_value;_lastValue;_isEqual=eW;constructor(e,t=eW){this._value=this._lastValue=e,this._isEqual=t}get value(){return this._value}set value(e){this.value!==e&&(this._value=e,this.revision=++eU)}};function eW(e,t){return e===t}function eV(e){return e instanceof e$||console.warn("Not a valid cell! ",e),e.value}var eX=(e,t)=>!1;function eH(){return function(e,t=eW){return new e$(null,t)}(0,eX)}var eq=e=>{let t=e.collectionTag;null===t&&(t=e.collectionTag=eH()),eV(t)};Symbol();var eY=0,eG=Object.getPrototypeOf({}),eZ=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy(this,eJ);tag=eH();tags={};children={};collectionTag=null;id=eY++},eJ={get:(e,t)=>(function(){let{value:r}=e,n=Reflect.get(r,t);if("symbol"==typeof t||t in eG)return n;if("object"==typeof n&&null!==n){var i;let r=e.children[t];return void 0===r&&(r=e.children[t]=Array.isArray(i=n)?new eQ(i):new eZ(i)),r.tag&&eV(r.tag),r.proxy}{let r=e.tags[t];return void 0===r&&((r=e.tags[t]=eH()).value=n),eV(r),n}})(),ownKeys:e=>(eq(e),Reflect.ownKeys(e.value)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e.value,t),has:(e,t)=>Reflect.has(e.value,t)},eQ=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy([this],e0);tag=eH();tags={};children={};collectionTag=null;id=eY++},e0={get:([e],t)=>("length"===t&&eq(e),eJ.get(e,t)),ownKeys:([e])=>eJ.ownKeys(e),getOwnPropertyDescriptor:([e],t)=>eJ.getOwnPropertyDescriptor(e,t),has:([e],t)=>eJ.has(e,t)},e1="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function e2(){return{s:0,v:void 0,o:null,p:null}}function e5(e,t={}){let r,n=e2(),{resultEqualityCheck:i}=t,a=0;function o(){let t,o=n,{length:l}=arguments;for(let e=0;e<l;e++){let t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=o.o;null===e&&(o.o=e=new WeakMap);let r=e.get(t);void 0===r?(o=e2(),e.set(t,o)):o=r}else{let e=o.p;null===e&&(o.p=e=new Map);let r=e.get(t);void 0===r?(o=e2(),e.set(t,o)):o=r}}let c=o;if(1===o.s)t=o.v;else if(t=e.apply(null,arguments),a++,i){let e=r?.deref?.()??r;null!=e&&i(e,t)&&(t=e,0!==a&&a--),r="object"==typeof t&&null!==t||"function"==typeof t?new e1(t):t}return c.s=1,c.v=t,t}return o.clearCache=()=>{n=e2(),o.resetResultsCount()},o.resultsCount=()=>a,o.resetResultsCount=()=>{a=0},o}var e3=function(e,...t){let r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,n=(...e)=>{let t,n=0,i=0,a={},o=e.pop();"object"==typeof o&&(a=o,o=e.pop()),function(e,t=`expected a function, instead received ${typeof e}`){if("function"!=typeof e)throw TypeError(t)}(o,`createSelector expects an output function after the inputs, but received: [${typeof o}]`);let{memoize:l,memoizeOptions:c=[],argsMemoize:u=e5,argsMemoizeOptions:s=[],devModeChecks:f={}}={...r,...a},p=eF(c),d=eF(s),h=function(e){let t=Array.isArray(e[0])?e[0]:e;return!function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){let r=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}(e),y=l(function(){return n++,o.apply(null,arguments)},...p);return Object.assign(u(function(){i++;let e=function(e,t){let r=[],{length:n}=e;for(let i=0;i<n;i++)r.push(e[i].apply(null,t));return r}(h,arguments);return t=y.apply(null,e)},...d),{resultFunc:o,memoizedResultFunc:y,dependencies:h,dependencyRecomputations:()=>i,resetDependencyRecomputations:()=>{i=0},lastResult:()=>t,recomputations:()=>n,resetRecomputations:()=>{n=0},memoize:l,argsMemoize:u})};return Object.assign(n,{withTypes:()=>n}),n}(e5),e4=Object.assign((e,t=e3)=>{!function(e,t=`expected an object, instead received ${typeof e}`){if("object"!=typeof e)throw TypeError(t)}(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);let r=Object.keys(e);return t(r.map(t=>e[t]),(...e)=>e.reduce((e,t,n)=>(e[r[n]]=t,e),{}))},{withTypes:()=>e4}),e6=r(20241),e8=r.n(e6),e7=e=>e.legend.settings,e9=e3([e=>e.legend.payload,e7],(e,t)=>{var{itemSorter:r}=t,n=e.flat(1);return r?e8()(n,r):n});function te(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[t,r]=(0,p.useState)({height:0,left:0,top:0,width:0}),n=(0,p.useCallback)(e=>{if(null!=e){var n=e.getBoundingClientRect(),i={height:n.height,left:n.left,top:n.top,width:n.width};(Math.abs(i.height-t.height)>1||Math.abs(i.left-t.left)>1||Math.abs(i.top-t.top)>1||Math.abs(i.width-t.width)>1)&&r({height:i.height,left:i.left,top:i.top,width:i.width})}},[t.width,t.height,t.top,t.left,...e]);return[t,n]}function tt(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var tr="function"==typeof Symbol&&Symbol.observable||"@@observable",tn=()=>Math.random().toString(36).substring(7).split("").join("."),ti={INIT:`@@redux/INIT${tn()}`,REPLACE:`@@redux/REPLACE${tn()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${tn()}`};function ta(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function to(e){let t,r=Object.keys(e),n={};for(let t=0;t<r.length;t++){let i=r[t];"function"==typeof e[i]&&(n[i]=e[i])}let i=Object.keys(n);try{Object.keys(n).forEach(e=>{let t=n[e];if(void 0===t(void 0,{type:ti.INIT}))throw Error(tt(12));if(void 0===t(void 0,{type:ti.PROBE_UNKNOWN_ACTION()}))throw Error(tt(13))})}catch(e){t=e}return function(e={},r){if(t)throw t;let a=!1,o={};for(let t=0;t<i.length;t++){let l=i[t],c=n[l],u=e[l],s=c(u,r);if(void 0===s)throw r&&r.type,Error(tt(14));o[l]=s,a=a||s!==u}return(a=a||i.length!==Object.keys(e).length)?o:e}}function tl(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce((e,t)=>(...r)=>e(t(...r)))}function tc(e){return ta(e)&&"type"in e&&"string"==typeof e.type}function tu(e){return({dispatch:t,getState:r})=>n=>i=>"function"==typeof i?i(t,r,e):n(i)}var ts=tu(),tf=Symbol.for("immer-nothing"),tp=Symbol.for("immer-draftable"),td=Symbol.for("immer-state");function th(e,...t){throw Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var ty=Object.getPrototypeOf;function tv(e){return!!e&&!!e[td]}function tm(e){return!!e&&(tb(e)||Array.isArray(e)||!!e[tp]||!!e.constructor?.[tp]||tj(e)||tE(e))}var tg=Object.prototype.constructor.toString();function tb(e){if(!e||"object"!=typeof e)return!1;let t=ty(e);if(null===t)return!0;let r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===tg}function tx(e,t){0===tO(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function tO(e){let t=e[td];return t?t.type_:Array.isArray(e)?1:tj(e)?2:3*!!tE(e)}function tw(e,t){return 2===tO(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function tP(e,t,r){let n=tO(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function tj(e){return e instanceof Map}function tE(e){return e instanceof Set}function tS(e){return e.copy_||e.base_}function tA(e,t){if(tj(e))return new Map(e);if(tE(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);let r=tb(e);if(!0!==t&&("class_only"!==t||r)){let t=ty(e);return null!==t&&r?{...e}:Object.assign(Object.create(t),e)}{let t=Object.getOwnPropertyDescriptors(e);delete t[td];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){let i=r[n],a=t[i];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(t[i]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[i]})}return Object.create(ty(e),t)}}function tk(e,t=!1){return tT(e)||tv(e)||!tm(e)||(tO(e)>1&&(e.set=e.add=e.clear=e.delete=tM),Object.freeze(e),t&&Object.entries(e).forEach(([e,t])=>tk(t,!0))),e}function tM(){th(2)}function tT(e){return Object.isFrozen(e)}var tC={};function tD(e){let t=tC[e];return t||th(0,e),t}function tN(e,t){t&&(tD("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function t_(e){tI(e),e.drafts_.forEach(tR),e.drafts_=null}function tI(e){e===n&&(n=e.parent_)}function tL(e){return n={drafts_:[],parent_:n,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function tR(e){let t=e[td];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function tK(e,t){t.unfinalizedDrafts_=t.drafts_.length;let r=t.drafts_[0];return void 0!==e&&e!==r?(r[td].modified_&&(t_(t),th(4)),tm(e)&&(e=tz(t,e),t.parent_||tF(t,e)),t.patches_&&tD("Patches").generateReplacementPatches_(r[td].base_,e,t.patches_,t.inversePatches_)):e=tz(t,r,[]),t_(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==tf?e:void 0}function tz(e,t,r){if(tT(t))return t;let n=t[td];if(!n)return tx(t,(i,a)=>tB(e,n,t,i,a,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return tF(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;let t=n.copy_,i=t,a=!1;3===n.type_&&(i=new Set(t),t.clear(),a=!0),tx(i,(i,o)=>tB(e,n,t,i,o,r,a)),tF(e,t,!1),r&&e.patches_&&tD("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function tB(e,t,r,n,i,a,o){if(tv(i)){let o=tz(e,i,a&&t&&3!==t.type_&&!tw(t.assigned_,n)?a.concat(n):void 0);if(tP(r,n,o),!tv(o))return;e.canAutoFreeze_=!1}else o&&r.add(i);if(tm(i)&&!tT(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;tz(e,i),(!t||!t.scope_.parent_)&&"symbol"!=typeof n&&Object.prototype.propertyIsEnumerable.call(r,n)&&tF(e,i)}}function tF(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&tk(t,r)}var tU={get(e,t){if(t===td)return e;let r=tS(e);if(!tw(r,t)){var n=e,i=r,a=t;let o=tV(i,a);return o?"value"in o?o.value:o.get?.call(n.draft_):void 0}let o=r[t];return e.finalized_||!tm(o)?o:o===tW(e.base_,t)?(tH(e),e.copy_[t]=tq(o,e)):o},has:(e,t)=>t in tS(e),ownKeys:e=>Reflect.ownKeys(tS(e)),set(e,t,r){let n=tV(tS(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){let n=tW(tS(e),t),i=n?.[td];if(i&&i.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if((r===n?0!==r||1/r==1/n:r!=r&&n!=n)&&(void 0!==r||tw(e.base_,t)))return!0;tH(e),tX(e)}return!!(e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t]))||(e.copy_[t]=r,e.assigned_[t]=!0,!0)},deleteProperty:(e,t)=>(void 0!==tW(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,tH(e),tX(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){let r=tS(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){th(11)},getPrototypeOf:e=>ty(e.base_),setPrototypeOf(){th(12)}},t$={};function tW(e,t){let r=e[td];return(r?tS(r):e)[t]}function tV(e,t){if(!(t in e))return;let r=ty(e);for(;r;){let e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=ty(r)}}function tX(e){!e.modified_&&(e.modified_=!0,e.parent_&&tX(e.parent_))}function tH(e){e.copy_||(e.copy_=tA(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function tq(e,t){let r=tj(e)?tD("MapSet").proxyMap_(e,t):tE(e)?tD("MapSet").proxySet_(e,t):function(e,t){let r=Array.isArray(e),i={type_:+!!r,scope_:t?t.scope_:n,modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1},a=i,o=tU;r&&(a=[i],o=t$);let{revoke:l,proxy:c}=Proxy.revocable(a,o);return i.draft_=c,i.revoke_=l,c}(e,t);return(t?t.scope_:n).drafts_.push(r),r}function tY(e){return tv(e)||th(10,e),function e(t){let r;if(!tm(t)||tT(t))return t;let n=t[td];if(n){if(!n.modified_)return n.base_;n.finalized_=!0,r=tA(t,n.scope_.immer_.useStrictShallowCopy_)}else r=tA(t,!0);return tx(r,(t,n)=>{tP(r,t,e(n))}),n&&(n.finalized_=!1),r}(e)}tx(tU,(e,t)=>{t$[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),t$.deleteProperty=function(e,t){return t$.set.call(this,e,t,void 0)},t$.set=function(e,t,r){return tU.set.call(this,e[0],t,r,e[0])};var tG=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{let n;if("function"==typeof e&&"function"!=typeof t){let r=t;t=e;let n=this;return function(e=r,...i){return n.produce(e,e=>t.call(this,e,...i))}}if("function"!=typeof t&&th(6),void 0!==r&&"function"!=typeof r&&th(7),tm(e)){let i=tL(this),a=tq(e,void 0),o=!0;try{n=t(a),o=!1}finally{o?t_(i):tI(i)}return tN(i,r),tK(n,i)}if(e&&"object"==typeof e)th(1,e);else{if(void 0===(n=t(e))&&(n=e),n===tf&&(n=void 0),this.autoFreeze_&&tk(n,!0),r){let t=[],i=[];tD("Patches").generateReplacementPatches_(e,n,t,i),r(t,i)}return n}},this.produceWithPatches=(e,t)=>{let r,n;return"function"==typeof e?(t,...r)=>this.produceWithPatches(t,t=>e(t,...r)):[this.produce(e,t,(e,t)=>{r=e,n=t}),r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){tm(e)||th(8),tv(e)&&(e=tY(e));let t=tL(this),r=tq(e,void 0);return r[td].isManual_=!0,tI(t),r}finishDraft(e,t){let r=e&&e[td];r&&r.isManual_||th(9);let{scope_:n}=r;return tN(n,t),tK(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){let n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));let n=tD("Patches").applyPatches_;return tv(e)?n(e,t):this.produce(e,e=>n(e,t))}},tZ=tG.produce;tG.produceWithPatches.bind(tG),tG.setAutoFreeze.bind(tG),tG.setUseStrictShallowCopy.bind(tG),tG.applyPatches.bind(tG),tG.createDraft.bind(tG),tG.finishDraft.bind(tG),r(49509);var tJ="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?tl:tl.apply(null,arguments)};function tQ(e,t){function r(...n){if(t){let r=t(...n);if(!r)throw Error(rT(0));return{type:e,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>tc(t)&&t.type===e,r}"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var t0=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function t1(e){return tm(e)?tZ(e,()=>{}):e}function t2(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var t5=e=>t=>{setTimeout(t,e)};function t3(e){let t,r={},n=[],i={addCase(e,t){let n="string"==typeof e?e:e.type;if(!n)throw Error(rT(28));if(n in r)throw Error(rT(29));return r[n]=t,i},addMatcher:(e,t)=>(n.push({matcher:e,reducer:t}),i),addDefaultCase:e=>(t=e,i)};return e(i),[r,n,t]}var t4=Symbol.for("rtk-slice-createasyncthunk"),t6=(e=>(e.reducer="reducer",e.reducerWithPrepare="reducerWithPrepare",e.asyncThunk="asyncThunk",e))(t6||{}),t8=function({creators:e}={}){let t=e?.asyncThunk?.[t4];return function(e){let r,{name:n,reducerPath:i=n}=e;if(!n)throw Error(rT(11));let a=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},o=Object.keys(a),l={},c={},u={},s=[],f={addCase(e,t){let r="string"==typeof e?e:e.type;if(!r)throw Error(rT(12));if(r in c)throw Error(rT(13));return c[r]=t,f},addMatcher:(e,t)=>(s.push({matcher:e,reducer:t}),f),exposeAction:(e,t)=>(u[e]=t,f),exposeCaseReducer:(e,t)=>(l[e]=t,f)};function p(){let[t={},r=[],n]="function"==typeof e.extraReducers?t3(e.extraReducers):[e.extraReducers],i={...t,...c};return function(e,t){let r,[n,i,a]=t3(t);if("function"==typeof e)r=()=>t1(e());else{let t=t1(e);r=()=>t}function o(e=r(),t){let l=[n[t.type],...i.filter(({matcher:e})=>e(t)).map(({reducer:e})=>e)];return 0===l.filter(e=>!!e).length&&(l=[a]),l.reduce((e,r)=>{if(r)if(tv(e)){let n=r(e,t);return void 0===n?e:n}else{if(tm(e))return tZ(e,e=>r(e,t));let n=r(e,t);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}return e},e)}return o.getInitialState=r,o}(e.initialState,e=>{for(let t in i)e.addCase(t,i[t]);for(let t of s)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)})}o.forEach(r=>{let i=a[r],o={reducerName:r,type:`${n}/${r}`,createNotation:"function"==typeof e.reducers};"asyncThunk"===i._reducerDefinitionType?function({type:e,reducerName:t},r,n,i){if(!i)throw Error(rT(18));let{payloadCreator:a,fulfilled:o,pending:l,rejected:c,settled:u,options:s}=r,f=i(e,a,s);n.exposeAction(t,f),o&&n.addCase(f.fulfilled,o),l&&n.addCase(f.pending,l),c&&n.addCase(f.rejected,c),u&&n.addMatcher(f.settled,u),n.exposeCaseReducer(t,{fulfilled:o||t7,pending:l||t7,rejected:c||t7,settled:u||t7})}(o,i,f,t):function({type:e,reducerName:t,createNotation:r},n,i){let a,o;if("reducer"in n){if(r&&"reducerWithPrepare"!==n._reducerDefinitionType)throw Error(rT(17));a=n.reducer,o=n.prepare}else a=n;i.addCase(e,a).exposeCaseReducer(t,a).exposeAction(t,o?tQ(e,o):tQ(e))}(o,i,f)});let d=e=>e,h=new Map,y=new WeakMap;function v(e,t){return r||(r=p()),r(e,t)}function m(){return r||(r=p()),r.getInitialState()}function g(t,r=!1){function n(e){let i=e[t];return void 0===i&&r&&(i=t2(y,n,m)),i}function i(t=d){let n=t2(h,r,()=>new WeakMap);return t2(n,t,()=>{let n={};for(let[i,a]of Object.entries(e.selectors??{}))n[i]=function(e,t,r,n){function i(a,...o){let l=t(a);return void 0===l&&n&&(l=r()),e(l,...o)}return i.unwrapped=e,i}(a,t,()=>t2(y,t,m),r);return n})}return{reducerPath:t,getSelectors:i,get selectors(){return i(n)},selectSlice:n}}let b={name:n,reducer:v,actions:u,caseReducers:l,getInitialState:m,...g(i),injectInto(e,{reducerPath:t,...r}={}){let n=t??i;return e.inject({reducerPath:n,reducer:v},r),{...b,...g(n,!0)}}};return b}}();function t7(){}var t9="listener",re="completed",rt="cancelled",rr=`task-${rt}`,rn=`task-${re}`,ri=`${t9}-${rt}`,ra=`${t9}-${re}`,ro=class{constructor(e){this.code=e,this.message=`task ${rt} (reason: ${e})`}name="TaskAbortError";message},rl=(e,t)=>{if("function"!=typeof e)throw TypeError(rT(32))},rc=()=>{},ru=(e,t=rc)=>(e.catch(t),e),rs=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),rf=(e,t)=>{let r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},rp=e=>{if(e.aborted){let{reason:t}=e;throw new ro(t)}};function rd(e,t){let r=rc;return new Promise((n,i)=>{let a=()=>i(new ro(e.reason));if(e.aborted)return void a();r=rs(e,a),t.finally(()=>r()).then(n,i)}).finally(()=>{r=rc})}var rh=async(e,t)=>{try{await Promise.resolve();let t=await e();return{status:"ok",value:t}}catch(e){return{status:e instanceof ro?"cancelled":"rejected",error:e}}finally{t?.()}},ry=e=>t=>ru(rd(e,t).then(t=>(rp(e),t))),rv=e=>{let t=ry(e);return e=>t(new Promise(t=>setTimeout(t,e)))},{assign:rm}=Object,rg={},rb="listenerMiddleware",rx=e=>{let{type:t,actionCreator:r,matcher:n,predicate:i,effect:a}=e;if(t)i=tQ(t).match;else if(r)t=r.type,i=r.match;else if(n)i=n;else if(i);else throw Error(rT(21));return rl(a,"options.listener"),{predicate:i,type:t,effect:a}},rO=rm(e=>{let{type:t,predicate:r,effect:n}=rx(e);return{id:((e=21)=>{let t="",r=e;for(;r--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t})(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw Error(rT(22))}}},{withTypes:()=>rO}),rw=(e,t)=>{let{type:r,effect:n,predicate:i}=rx(t);return Array.from(e.values()).find(e=>("string"==typeof r?e.type===r:e.predicate===i)&&e.effect===n)},rP=e=>{e.pending.forEach(e=>{rf(e,ri)})},rj=(e,t,r)=>{try{e(t,r)}catch(e){setTimeout(()=>{throw e},0)}},rE=rm(tQ(`${rb}/add`),{withTypes:()=>rE}),rS=tQ(`${rb}/removeAll`),rA=rm(tQ(`${rb}/remove`),{withTypes:()=>rA}),rk=(...e)=>{console.error(`${rb}/error`,...e)},rM=(e={})=>{let t=new Map,{extra:r,onError:n=rk}=e;rl(n,"onError");let i=e=>(e=>(e.unsubscribe=()=>t.delete(e.id),t.set(e.id,e),t=>{e.unsubscribe(),t?.cancelActive&&rP(e)}))(rw(t,e)??rO(e));rm(i,{withTypes:()=>i});let a=e=>{let r=rw(t,e);return r&&(r.unsubscribe(),e.cancelActive&&rP(r)),!!r};rm(a,{withTypes:()=>a});let o=async(e,a,o,l)=>{let c=new AbortController,u=((e,t)=>{let r=async(r,n)=>{rp(t);let i=()=>{},a=[new Promise((t,n)=>{let a=e({predicate:r,effect:(e,r)=>{r.unsubscribe(),t([e,r.getState(),r.getOriginalState()])}});i=()=>{a(),n()}})];null!=n&&a.push(new Promise(e=>setTimeout(e,n,null)));try{let e=await rd(t,Promise.race(a));return rp(t),e}finally{i()}};return(e,t)=>ru(r(e,t))})(i,c.signal),s=[];try{e.pending.add(c),await Promise.resolve(e.effect(a,rm({},o,{getOriginalState:l,condition:(e,t)=>u(e,t).then(Boolean),take:u,delay:rv(c.signal),pause:ry(c.signal),extra:r,signal:c.signal,fork:((e,t)=>(r,n)=>{rl(r,"taskExecutor");let i=new AbortController;rs(e,()=>rf(i,e.reason));let a=rh(async()=>{rp(e),rp(i.signal);let t=await r({pause:ry(i.signal),delay:rv(i.signal),signal:i.signal});return rp(i.signal),t},()=>rf(i,rn));return n?.autoJoin&&t.push(a.catch(rc)),{result:ry(e)(a),cancel(){rf(i,rr)}}})(c.signal,s),unsubscribe:e.unsubscribe,subscribe:()=>{t.set(e.id,e)},cancelActiveListeners:()=>{e.pending.forEach((e,t,r)=>{e!==c&&(rf(e,ri),r.delete(e))})},cancel:()=>{rf(c,ri),e.pending.delete(c)},throwIfCancelled:()=>{rp(c.signal)}})))}catch(e){e instanceof ro||rj(n,e,{raisedBy:"effect"})}finally{await Promise.all(s),rf(c,ra),e.pending.delete(c)}},l=(e=>()=>{e.forEach(rP),e.clear()})(t);return{middleware:e=>r=>c=>{let u;if(!tc(c))return r(c);if(rE.match(c))return i(c.payload);if(rS.match(c))return void l();if(rA.match(c))return a(c.payload);let s=e.getState(),f=()=>{if(s===rg)throw Error(rT(23));return s};try{if(u=r(c),t.size>0){let r=e.getState();for(let i of Array.from(t.values())){let t=!1;try{t=i.predicate(c,r,s)}catch(e){t=!1,rj(n,e,{raisedBy:"predicate"})}t&&o(i,c,e,f)}}}finally{s=rg}return u},startListening:i,stopListening:a,clearListeners:l}};function rT(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}Symbol.for("rtk-state-proxy-original");var rC=t8({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:rD,setLayout:rN,setChartSize:r_,setScale:rI}=rC.actions,rL=rC.reducer;function rR(e,t){if((i=e.length)>1)for(var r,n,i,a=1,o=e[t[0]],l=o.length;a<i;++a)for(n=o,o=e[t[a]],r=0;r<l;++r)o[r][1]+=o[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function rK(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function rz(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function rB(e,t){return e[t]}function rF(e){let t=[];return t.key=e,t}function rU(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function r$(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rU(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rU(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}Array.prototype.slice;var rW=Math.PI/180,rV=(e,t,r,n)=>({x:e+Math.cos(-rW*n)*r,y:t+Math.sin(-rW*n)*r}),rX=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0,width:0,height:0,brushBottom:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},rH=e=>(0,p.isValidElement)(e)||"function"==typeof e||"boolean"==typeof e||null==e?"":e.className;function rq(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function rY(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rq(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rq(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function rG(e,t,r){return null==e||null==t?r:O(t)?y()(e,t,r):"function"==typeof t?t(e):r}var rZ=(e,t)=>"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t,rJ=(e,t,r,n)=>{if(n)return e.map(e=>e.coordinate);var i,a,o=e.map(e=>(e.coordinate===t&&(i=!0),e.coordinate===r&&(a=!0),e.coordinate));return i||o.push(t),a||o.push(r),o},rQ=(e,t,r)=>{if(!e)return null;var{duplicateDomain:n,type:i,range:a,scale:o,realScaleType:l,isCategorical:c,categoricalDomain:u,tickCount:s,ticks:f,niceTicks:p,axisType:d}=e;if(!o)return null;var h="scaleBand"===l&&o.bandwidth?o.bandwidth()/2:2,y=(t||r)&&"category"===i&&o.bandwidth?o.bandwidth()/h:0;return(y="angleAxis"===d&&a&&a.length>=2?2*m(a[0]-a[1])*y:y,t&&(f||p))?(f||p||[]).map((e,t)=>({coordinate:o(n?n.indexOf(e):e)+y,value:e,offset:y,index:t})).filter(e=>!g(e.coordinate)):c&&u?u.map((e,t)=>({coordinate:o(e)+y,value:e,index:t,offset:y})):o.ticks&&!r&&null!=s?o.ticks(s).map((e,t)=>({coordinate:o(e)+y,value:e,offset:y,index:t})):o.domain().map((e,t)=>({coordinate:o(e)+y,value:n?n[e]:e,index:t,offset:y}))},r0=(e,t)=>{if(!t||2!==t.length||!x(t[0])||!x(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),i=[e[0],e[1]];return(!x(e[0])||e[0]<r)&&(i[0]=r),(!x(e[1])||e[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},r1={sign:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0,o=0;o<t;++o){var l=g(e[o][r][1])?e[o][r][0]:e[o][r][1];l>=0?(e[o][r][0]=i,e[o][r][1]=i+l,i=e[o][r][1]):(e[o][r][0]=a,e[o][r][1]=a+l,a=e[o][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,i,a=0,o=e[0].length;a<o;++a){for(i=r=0;r<n;++r)i+=e[r][a][1]||0;if(i)for(r=0;r<n;++r)e[r][a][1]/=i}rR(e,t)}},none:rR,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,i=e[t[0]],a=i.length;n<a;++n){for(var o=0,l=0;o<r;++o)l+=e[o][n][1]||0;i[n][1]+=i[n][0]=-l/2}rR(e,t)}},wiggle:function(e,t){if((i=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,i,a=0,o=1;o<n;++o){for(var l=0,c=0,u=0;l<i;++l){for(var s=e[t[l]],f=s[o][1]||0,p=(f-(s[o-1][1]||0))/2,d=0;d<l;++d){var h=e[t[d]];p+=(h[o][1]||0)-(h[o-1][1]||0)}c+=f,u+=p*f}r[o-1][1]+=r[o-1][0]=a,c&&(a-=u/c)}r[o-1][1]+=r[o-1][0]=a,rR(e,t)}},positive:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0;a<t;++a){var o=g(e[a][r][1])?e[a][r][0]:e[a][r][1];o>=0?(e[a][r][0]=i,e[a][r][1]=i+o,i=e[a][r][1]):(e[a][r][0]=0,e[a][r][1]=0)}}};function r2(e){return null==e?void 0:String(e)}function r5(e){var{axis:t,ticks:r,bandSize:n,entry:i,index:a,dataKey:o}=e;if("category"===t.type){if(!t.allowDuplicatedCategory&&t.dataKey&&null!=i[t.dataKey]){var l=k(r,"value",i[t.dataKey]);if(l)return l.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var c=rG(i,null==o?t.dataKey:o);return null==c?null:t.scale(c)}var r3=e=>{var{axis:t,ticks:r,offset:n,bandSize:i,entry:a,index:o}=e;if("category"===t.type)return r[o]?r[o].coordinate+n:null;var l=rG(a,t.dataKey,t.scale.domain()[o]);return null==l?null:t.scale(l)-i/2+n},r4=e=>{var{numericAxis:t}=e,r=t.scale.domain();if("number"===t.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},r6=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,r8=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,r7=(e,t,r)=>{if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var i=e8()(t,e=>e.coordinate),a=1/0,o=1,l=i.length;o<l;o++){var c=i[o],u=i[o-1];a=Math.min((c.coordinate||0)-(u.coordinate||0),a)}return a===1/0?0:a}return r?void 0:0};function r9(e){var{tooltipEntrySettings:t,dataKey:r,payload:n,value:i,name:a}=e;return rY(rY({},t),{},{dataKey:r,payload:n,value:i,name:a})}function ne(e,t){return e?String(e):"string"==typeof t?t:void 0}var nt=e=>e.layout.width,nr=e=>e.layout.height,nn=e=>e.layout.scale,ni=e=>e.layout.margin,na=e3(e=>e.cartesianAxis.xAxis,e=>Object.values(e)),no=e3(e=>e.cartesianAxis.yAxis,e=>Object.values(e)),nl=["#1890FF","#66B5FF","#41D9C7","#2FC25B","#6EDB8F","#9AE65C","#FACC14","#E6965C","#57AD71","#223273","#738AE6","#7564CC","#8543E0","#A877ED","#5C8EE6","#13C2C2","#70E0E0","#5CA3E6","#3436C7","#8082FF","#DD81E6","#F04864","#FA7D92","#D598D9"],nc="data-recharts-item-index",nu="data-recharts-item-data-key";function ns(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nf(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ns(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ns(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var np=e3([nt,nr,ni,e=>e.brush.height,na,no,e7,e=>e.legend.size],(e,t,r,n,i,a,o,l)=>{var c=a.reduce((e,t)=>{var{orientation:r}=t;if(!t.mirror&&!t.hide){var n="number"==typeof t.width?t.width:60;return nf(nf({},e),{},{[r]:e[r]+n})}return e},{left:r.left||0,right:r.right||0}),u=i.reduce((e,t)=>{var{orientation:r}=t;return t.mirror||t.hide?e:nf(nf({},e),{},{[r]:y()(e,"".concat(r))+t.height})},{top:r.top||0,bottom:r.bottom||0}),s=nf(nf({},u),c),f=s.bottom;s.bottom+=n;var p=e-(s=((e,t,r)=>{if(t&&r){var{width:n,height:i}=r,{align:a,verticalAlign:o,layout:l}=t;if(("vertical"===l||"horizontal"===l&&"middle"===o)&&"center"!==a&&x(e[a]))return rY(rY({},e),{},{[a]:e[a]+(n||0)});if(("horizontal"===l||"vertical"===l&&"center"===a)&&"middle"!==o&&x(e[o]))return rY(rY({},e),{},{[o]:e[o]+(i||0)})}return e})(s,o,l)).left-s.right,d=t-s.top-s.bottom;return nf(nf({brushBottom:f},s),{},{width:Math.max(p,0),height:Math.max(d,0)})}),nd=e3(np,e=>({x:e.left,y:e.top,width:e.width,height:e.height})),nh=e3(nt,nr,(e,t)=>({x:0,y:0,width:e,height:t})),ny=(0,p.createContext)(null),nv=()=>null!=(0,p.useContext)(ny),nm=e=>{var{children:t}=e;return p.createElement(ny.Provider,{value:!0},t)},ng=e=>e.brush,nb=e3([ng,np,ni],(e,t,r)=>({height:e.height,x:x(e.x)?e.x:t.left,y:x(e.y)?e.y:t.top+t.height+t.brushBottom-((null==r?void 0:r.bottom)||0),width:x(e.width)?e.width:t.width})),nx=()=>{var e,t=nv(),r=eB(nd),n=eB(nb),i=null==(e=eB(ng))?void 0:e.padding;return t&&n&&i?{width:n.width-i.left-i.right,height:n.height-i.top-i.bottom,x:i.left,y:i.top}:r},nO={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},nw=()=>{var e;return null!=(e=eB(np))?e:nO},nP=()=>eB(nt),nj=()=>eB(nr),nE={top:0,right:0,bottom:0,left:0},nS=e=>e.layout.layoutType,nA=()=>eB(nS),nk=e=>{var t=eL();return(0,p.useEffect)(()=>{t(r_(e))},[t,e]),null},nM=e=>{var{margin:t}=e,r=eL();return(0,p.useEffect)(()=>{r(rD(t))},[r,t]),null},nT=t8({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign,e.settings.itemSorter=t.payload.itemSorter},addLegendPayload(e,t){e.payload.push(t.payload)},removeLegendPayload(e,t){var r=tY(e).payload.indexOf(t.payload);r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:nC,setLegendSettings:nD,addLegendPayload:nN,removeLegendPayload:n_}=nT.actions,nI=nT.reducer,nL=["contextPayload"];function nR(){return(nR=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function nK(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nz(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nK(Object(r),!0).forEach(function(t){nB(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nK(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function nB(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function nF(e){return e.value}function nU(e){var{contextPayload:t}=e,r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,nL),n=eD(t,e.payloadUniqBy,nF),i=nz(nz({},r),{},{payload:n});return p.isValidElement(e.content)?p.cloneElement(e.content,i):"function"==typeof e.content?p.createElement(e.content,i):p.createElement(eM,i)}function n$(e){var t=eL();return(0,p.useEffect)(()=>{t(nD(e))},[t,e]),null}function nW(e){var t=eL();return(0,p.useEffect)(()=>(t(nC(e)),()=>{t(nC({width:0,height:0}))}),[t,e]),null}function nV(e){var t,r=eB(e9),n=(0,p.useContext)(G),i=null!=(t=eB(e=>e.layout.margin))?t:nE,{width:a,height:o,wrapperStyle:l,portal:c}=e,[u,s]=te([r]),f=nP(),d=nj(),h=f-(i.left||0)-(i.right||0),y=nX.getWidthOrHeight(e.layout,o,a,h),v=c?l:nz(nz({position:"absolute",width:(null==y?void 0:y.width)||a||"auto",height:(null==y?void 0:y.height)||o||"auto"},function(e,t,r,n,i,a){var o,l,{layout:c,align:u,verticalAlign:s}=t;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(o="center"===u&&"vertical"===c?{left:((n||0)-a.width)/2}:"right"===u?{right:r&&r.right||0}:{left:r&&r.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(l="middle"===s?{top:((i||0)-a.height)/2}:"bottom"===s?{bottom:r&&r.bottom||0}:{top:r&&r.top||0}),nz(nz({},o),l)}(l,e,i,f,d,u)),l),m=null!=c?c:n;if(null==m)return null;var g=p.createElement("div",{className:"recharts-legend-wrapper",style:v,ref:s},p.createElement(n$,{layout:e.layout,align:e.align,verticalAlign:e.verticalAlign,itemSorter:e.itemSorter}),p.createElement(nW,{width:u.width,height:u.height}),p.createElement(nU,nR({},e,y,{margin:i,chartWidth:f,chartHeight:d,contextPayload:r})));return(0,Y.createPortal)(g,m)}class nX extends p.PureComponent{static getWidthOrHeight(e,t,r,n){return"vertical"===e&&x(t)?{height:t}:"horizontal"===e?{width:r||n}:null}render(){return p.createElement(nV,this.props)}}function nH(){return(nH=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function nq(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nY(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nq(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nq(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function nG(e){return Array.isArray(e)&&O(e[0])&&O(e[1])?e.join(" ~ "):e}nB(nX,"displayName","Legend"),nB(nX,"defaultProps",{align:"center",iconSize:14,itemSorter:"value",layout:"horizontal",verticalAlign:"bottom"});var nZ=e=>{var{separator:t=" : ",contentStyle:r={},itemStyle:n={},labelStyle:i={},payload:a,formatter:o,itemSorter:l,wrapperClassName:c,labelClassName:u,label:s,labelFormatter:f,accessibilityLayer:h=!1}=e,y=nY({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),v=nY({margin:0},i),m=null!=s,g=m?s:"",b=d("recharts-default-tooltip",c),x=d("recharts-tooltip-label",u);return m&&f&&null!=a&&(g=f(s,a)),p.createElement("div",nH({className:b,style:y},h?{role:"status","aria-live":"assertive"}:{}),p.createElement("p",{className:x,style:v},p.isValidElement(g)?g:"".concat(g)),(()=>{if(a&&a.length){var e=(l?e8()(a,l):a).map((e,r)=>{if("none"===e.type)return null;var i=e.formatter||o||nG,{value:l,name:c}=e,u=l,s=c;if(i){var f=i(l,c,e,r,a);if(Array.isArray(f))[u,s]=f;else{if(null==f)return null;u=f}}var d=nY({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},n);return p.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(r),style:d},O(s)?p.createElement("span",{className:"recharts-tooltip-item-name"},s):null,O(s)?p.createElement("span",{className:"recharts-tooltip-item-separator"},t):null,p.createElement("span",{className:"recharts-tooltip-item-value"},u),p.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return p.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null})())},nJ="recharts-tooltip-wrapper",nQ={visibility:"hidden"};function n0(e){var{allowEscapeViewBox:t,coordinate:r,key:n,offsetTopLeft:i,position:a,reverseDirection:o,tooltipDimension:l,viewBox:c,viewBoxDimension:u}=e;if(a&&x(a[n]))return a[n];var s=r[n]-l-(i>0?i:0),f=r[n]+i;if(t[n])return o[n]?s:f;var p=c[n];return null==p?0:o[n]?s<p?Math.max(f,p):Math.max(s,p):null==u?0:f+l>p+u?Math.max(s,p):Math.max(f,p)}function n1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function n2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n1(Object(r),!0).forEach(function(t){n5(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n1(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function n5(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class n3 extends p.PureComponent{componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var e,t;this.state.dismissed&&((null==(e=this.props.coordinate)?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null==(t=this.props.coordinate)?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:e,allowEscapeViewBox:t,animationDuration:r,animationEasing:n,children:i,coordinate:a,hasPayload:o,isAnimationActive:l,offset:c,position:u,reverseDirection:s,useTranslate3d:f,viewBox:h,wrapperStyle:y,lastBoundingBox:v,innerRef:m,hasPortalFromProps:g}=this.props,{cssClasses:b,cssProperties:O}=function(e){var t,r,n,{allowEscapeViewBox:i,coordinate:a,offsetTopLeft:o,position:l,reverseDirection:c,tooltipBox:u,useTranslate3d:s,viewBox:f}=e;return{cssProperties:t=u.height>0&&u.width>0&&a?function(e){var{translateX:t,translateY:r,useTranslate3d:n}=e;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}({translateX:r=n0({allowEscapeViewBox:i,coordinate:a,key:"x",offsetTopLeft:o,position:l,reverseDirection:c,tooltipDimension:u.width,viewBox:f,viewBoxDimension:f.width}),translateY:n=n0({allowEscapeViewBox:i,coordinate:a,key:"y",offsetTopLeft:o,position:l,reverseDirection:c,tooltipDimension:u.height,viewBox:f,viewBoxDimension:f.height}),useTranslate3d:s}):nQ,cssClasses:function(e){var{coordinate:t,translateX:r,translateY:n}=e;return d(nJ,{["".concat(nJ,"-right")]:x(r)&&t&&x(t.x)&&r>=t.x,["".concat(nJ,"-left")]:x(r)&&t&&x(t.x)&&r<t.x,["".concat(nJ,"-bottom")]:x(n)&&t&&x(t.y)&&n>=t.y,["".concat(nJ,"-top")]:x(n)&&t&&x(t.y)&&n<t.y})}({translateX:r,translateY:n,coordinate:a})}}({allowEscapeViewBox:t,coordinate:a,offsetTopLeft:c,position:u,reverseDirection:s,tooltipBox:{height:v.height,width:v.width},useTranslate3d:f,viewBox:h}),w=g?{}:n2(n2({transition:l&&e?"transform ".concat(r,"ms ").concat(n):void 0},O),{},{pointerEvents:"none",visibility:!this.state.dismissed&&e&&o?"visible":"hidden",position:"absolute",top:0,left:0}),P=n2(n2({},w),{},{visibility:!this.state.dismissed&&e&&o?"visible":"hidden"},y);return p.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:b,style:P,ref:m},i)}constructor(){super(...arguments),n5(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),n5(this,"handleKeyDown",e=>{if("Escape"===e.key){var t,r,n,i;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(t=null==(r=this.props.coordinate)?void 0:r.x)?t:0,y:null!=(n=null==(i=this.props.coordinate)?void 0:i.y)?n:0}})}})}}var n4={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout)},n6=()=>eB(e=>e.rootProps.accessibilityLayer);function n8(){}function n7(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function n9(e){this._context=e}function ie(e){this._context=e}function it(e){this._context=e}n9.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:n7(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:n7(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},ie.prototype={areaStart:n8,areaEnd:n8,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:n7(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},it.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:n7(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class ir{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function ii(e){this._context=e}function ia(e){this._context=e}function io(e){return new ia(e)}ii.prototype={areaStart:n8,areaEnd:n8,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e*=1,t*=1,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function il(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0);return((a<0?-1:1)+(o<0?-1:1))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs((a*i+o*n)/(n+i)))||0}function ic(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function iu(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,l=(a-n)/3;e._context.bezierCurveTo(n+l,i+l*t,a-l,o-l*r,a,o)}function is(e){this._context=e}function ip(e){this._context=new id(e)}function id(e){this._context=e}function ih(e){this._context=e}function iy(e){var t,r,n=e.length-1,i=Array(n),a=Array(n),o=Array(n);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<n-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[n-1]=2,a[n-1]=7,o[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=i[t]/a[t-1],a[t]-=r,o[t]-=r*o[t-1];for(i[n-1]=o[n-1]/a[n-1],t=n-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(t=0,a[n-1]=(e[n]+i[n-1])/2;t<n-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function iv(e,t){this._context=e,this._t=t}function im(e){return e[0]}function ig(e){return e[1]}function ib(e,t){var r=ep(!0),n=null,i=io,a=null,o=eg(l);function l(l){var c,u,s,f=(l=rK(l)).length,p=!1;for(null==n&&(a=i(s=o())),c=0;c<=f;++c)!(c<f&&r(u=l[c],c,l))===p&&((p=!p)?a.lineStart():a.lineEnd()),p&&a.point(+e(u,c,l),+t(u,c,l));if(s)return a=null,s+""||null}return e="function"==typeof e?e:void 0===e?im:ep(e),t="function"==typeof t?t:void 0===t?ig:ep(t),l.x=function(t){return arguments.length?(e="function"==typeof t?t:ep(+t),l):e},l.y=function(e){return arguments.length?(t="function"==typeof e?e:ep(+e),l):t},l.defined=function(e){return arguments.length?(r="function"==typeof e?e:ep(!!e),l):r},l.curve=function(e){return arguments.length?(i=e,null!=n&&(a=i(n)),l):i},l.context=function(e){return arguments.length?(null==e?n=a=null:a=i(n=e),l):n},l}function ix(e,t,r){var n=null,i=ep(!0),a=null,o=io,l=null,c=eg(u);function u(u){var s,f,p,d,h,y=(u=rK(u)).length,v=!1,m=Array(y),g=Array(y);for(null==a&&(l=o(h=c())),s=0;s<=y;++s){if(!(s<y&&i(d=u[s],s,u))===v)if(v=!v)f=s,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),p=s-1;p>=f;--p)l.point(m[p],g[p]);l.lineEnd(),l.areaEnd()}v&&(m[s]=+e(d,s,u),g[s]=+t(d,s,u),l.point(n?+n(d,s,u):m[s],r?+r(d,s,u):g[s]))}if(h)return l=null,h+""||null}function s(){return ib().defined(i).curve(o).context(a)}return e="function"==typeof e?e:void 0===e?im:ep(+e),t="function"==typeof t?t:void 0===t?ep(0):ep(+t),r="function"==typeof r?r:void 0===r?ig:ep(+r),u.x=function(t){return arguments.length?(e="function"==typeof t?t:ep(+t),n=null,u):e},u.x0=function(t){return arguments.length?(e="function"==typeof t?t:ep(+t),u):e},u.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:ep(+e),u):n},u.y=function(e){return arguments.length?(t="function"==typeof e?e:ep(+e),r=null,u):t},u.y0=function(e){return arguments.length?(t="function"==typeof e?e:ep(+e),u):t},u.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:ep(+e),u):r},u.lineX0=u.lineY0=function(){return s().x(e).y(t)},u.lineY1=function(){return s().x(e).y(r)},u.lineX1=function(){return s().x(n).y(t)},u.defined=function(e){return arguments.length?(i="function"==typeof e?e:ep(!!e),u):i},u.curve=function(e){return arguments.length?(o=e,null!=a&&(l=o(a)),u):o},u.context=function(e){return arguments.length?(null==e?a=l=null:l=o(a=e),u):a},u}function iO(e){return Number.isFinite(e)}function iw(e){return"number"==typeof e&&e>0&&Number.isFinite(e)}function iP(){return(iP=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function ij(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function iE(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ij(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ij(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}ia.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},is.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:iu(this,this._t0,ic(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t*=1,(e*=1)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,iu(this,ic(this,r=il(this,e,t)),r);break;default:iu(this,this._t0,r=il(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(ip.prototype=Object.create(is.prototype)).point=function(e,t){is.prototype.point.call(this,t,e)},id.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}},ih.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=iy(e),i=iy(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},iv.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var iS={curveBasisClosed:function(e){return new ie(e)},curveBasisOpen:function(e){return new it(e)},curveBasis:function(e){return new n9(e)},curveBumpX:function(e){return new ir(e,!0)},curveBumpY:function(e){return new ir(e,!1)},curveLinearClosed:function(e){return new ii(e)},curveLinear:io,curveMonotoneX:function(e){return new is(e)},curveMonotoneY:function(e){return new ip(e)},curveNatural:function(e){return new ih(e)},curveStep:function(e){return new iv(e,.5)},curveStepAfter:function(e){return new iv(e,1)},curveStepBefore:function(e){return new iv(e,0)}},iA=e=>iO(e.x)&&iO(e.y),ik=e=>e.x,iM=e=>e.y,iT=e=>{var{className:t,points:r,path:n,pathRef:i}=e;if((!r||!r.length)&&!n)return null;var a=r&&r.length?(e=>{var t,{type:r="linear",points:n=[],baseLine:i,layout:a,connectNulls:o=!1}=e,l=((e,t)=>{if("function"==typeof e)return e;var r="curve".concat(M(e));return("curveMonotone"===r||"curveBump"===r)&&t?iS["".concat(r).concat("vertical"===t?"Y":"X")]:iS[r]||io})(r,a),c=o?n.filter(iA):n;if(Array.isArray(i)){var u=o?i.filter(e=>iA(e)):i,s=c.map((e,t)=>iE(iE({},e),{},{base:u[t]}));return(t="vertical"===a?ix().y(iM).x1(ik).x0(e=>e.base.x):ix().x(ik).y1(iM).y0(e=>e.base.y)).defined(iA).curve(l),t(s)}return(t="vertical"===a&&x(i)?ix().y(iM).x1(ik).x0(i):x(i)?ix().x(ik).y1(iM).y0(i):ib().x(ik).y(iM)).defined(iA).curve(l),t(c)})(e):n;return p.createElement("path",iP({},U(e,!1),_(e),{className:d("recharts-curve",t),d:null===a?void 0:a,ref:i}))},iC=["x","y","top","left","width","height","className"];function iD(){return(iD=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function iN(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var i_=e=>{var{x:t=0,y:r=0,top:n=0,left:i=0,width:a=0,height:o=0,className:l}=e,c=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?iN(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):iN(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:t,y:r,top:n,left:i,width:a,height:o},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,iC));return x(t)&&x(r)&&x(a)&&x(o)&&x(n)&&x(i)?p.createElement("path",iD({},U(c,!0),{className:d("recharts-cross",l),d:"M".concat(t,",").concat(n,"v").concat(o,"M").concat(i,",").concat(r,"h").concat(a)})):null};function iI(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function iL(e,t){var r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?iI(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):iI(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return Object.keys(t).reduce((e,r)=>(void 0===e[r]&&void 0!==t[r]&&(e[r]=t[r]),e),r)}var iR=r(22188),iK=r.n(iR),iz=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],iB=(e,t)=>e.map((e,r)=>e*t**r).reduce((e,t)=>e+t),iF=(e,t)=>r=>iB(iz(e,t),r),iU=function(){let e,t;for(var r,n,i,a,o=arguments.length,l=Array(o),c=0;c<o;c++)l[c]=arguments[c];if(1===l.length)switch(l[0]){case"linear":[r,i,n,a]=[0,0,1,1];break;case"ease":[r,i,n,a]=[.25,.1,.25,1];break;case"ease-in":[r,i,n,a]=[.42,0,1,1];break;case"ease-out":[r,i,n,a]=[.42,0,.58,1];break;case"ease-in-out":[r,i,n,a]=[0,0,.58,1];break;default:var u=l[0].split("(");"cubic-bezier"===u[0]&&4===u[1].split(")")[0].split(",").length&&([r,i,n,a]=u[1].split(")")[0].split(",").map(e=>parseFloat(e)))}else 4===l.length&&([r,i,n,a]=l);var s=iF(r,n),f=iF(i,a),p=(e=r,t=n,r=>iB([...iz(e,t).map((e,t)=>e*t).slice(1),0],r)),d=e=>e>1?1:e<0?0:e,h=e=>{for(var t=e>1?1:e,r=t,n=0;n<8;++n){var i=s(r)-t,a=p(r);if(1e-4>Math.abs(i-t)||a<1e-4)break;r=d(r-i/a)}return f(r)};return h.isStepper=!1,h},i$=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:t=100,damping:r=8,dt:n=17}=e,i=(e,i,a)=>{var o=a+(-(e-i)*t-a*r)*n/1e3,l=a*n/1e3+e;return 1e-4>Math.abs(l-i)&&1e-4>Math.abs(o)?[i,0]:[l,o]};return i.isStepper=!0,i.dt=n,i};function iW(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function iV(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?iW(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):iW(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var iX=(e,t)=>Object.keys(t).reduce((r,n)=>iV(iV({},r),{},{[n]:e(n,t[n])}),{});function iH(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function iq(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?iH(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):iH(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var iY=(e,t,r)=>e+(t-e)*r,iG=e=>{var{from:t,to:r}=e;return t!==r},iZ=(e,t,r)=>{var n=iX((t,r)=>{if(iG(r)){var[n,i]=e(r.from,r.to,r.velocity);return iq(iq({},r),{},{from:n,velocity:i})}return r},t);return r<1?iX((e,t)=>iG(t)?iq(iq({},t),{},{velocity:iY(t.velocity,n[e].velocity,r),from:iY(t.from,n[e].from,r)}):t,t):iZ(e,n,r-1)};class iJ{setTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,i=a=>{a-r>=t?e(a):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(i))};return n=requestAnimationFrame(i),()=>{cancelAnimationFrame(n)}}}var iQ=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function i0(){return(i0=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function i1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i1(Object(r),!0).forEach(function(t){i5(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i1(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function i5(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class i3 extends p.PureComponent{componentDidMount(){var{isActive:e,canBegin:t}=this.props;this.mounted=!0,e&&t&&this.runAnimation(this.props)}componentDidUpdate(e){var{isActive:t,canBegin:r,attributeName:n,shouldReAnimate:i,to:a,from:o}=this.props,{style:l}=this.state;if(r){if(!t){this.state&&l&&(n&&l[n]!==a||!n&&l!==a)&&this.setState({style:n?{[n]:a}:a});return}if(!iK()(e.to,a)||!e.canBegin||!e.isActive){var c=!e.canBegin||!e.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var u=c||i?o:e.to;this.state&&l&&(n&&l[n]!==u||!n&&l!==u)&&this.setState({style:n?{[n]:u}:u}),this.runAnimation(i2(i2({},this.props),{},{from:u,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:e}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}handleStyleChange(e){this.changeStyle(e)}changeStyle(e){this.mounted&&this.setState({style:e})}runJSAnimation(e){var t,r,n,i,a,o,l,c,u,s,f,p,d,h,y,v,m,g,b,x,O,w,P,j,E,{from:S,to:A,duration:k,easing:M,begin:T,onAnimationEnd:C,onAnimationStart:D}=e,N=(w=(e=>{if("string"==typeof e)switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return iU(e);case"spring":return i$();default:if("cubic-bezier"===e.split("(")[0])return iU(e)}return"function"==typeof e?e:null})(M),P=this.changeStyle,j=this.manager.getTimeoutController(),E=[Object.keys(S),Object.keys(A)].reduce((e,t)=>e.filter(e=>t.includes(e))),!0===w.isStepper?(t=S,r=A,n=w,i=E,a=P,o=j,c=i.reduce((e,n)=>iq(iq({},e),{},{[n]:{from:t[n],velocity:0,to:r[n]}}),{}),u=null,s=e=>{l||(l=e);var i=(e-l)/n.dt;c=iZ(n,c,i),a(iq(iq(iq({},t),r),iX((e,t)=>t.from,c))),l=e,Object.values(c).filter(iG).length&&(u=o.setTimeout(s))},()=>(u=o.setTimeout(s),()=>{u()})):(f=S,p=A,d=w,h=k,y=E,v=P,m=j,b=null,x=y.reduce((e,t)=>iq(iq({},e),{},{[t]:[f[t],p[t]]}),{}),O=e=>{g||(g=e);var t=(e-g)/h,r=iX((e,r)=>iY(...r,d(t)),x);if(v(iq(iq(iq({},f),p),r)),t<1)b=m.setTimeout(O);else{var n=iX((e,t)=>iY(...t,d(1)),x);v(iq(iq(iq({},f),p),n))}},()=>(b=m.setTimeout(O),()=>{b()})));this.manager.start([D,T,()=>{this.stopJSAnimation=N()},k,C])}runAnimation(e){let t;var{begin:r,duration:n,attributeName:i,to:a,easing:o,onAnimationStart:l,onAnimationEnd:c,children:u}=e;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof o||"function"==typeof u||"spring"===o)return void this.runJSAnimation(e);var s=i?{[i]:a}:a,f=(t=Object.keys(s),t.map(e=>"".concat(e.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase()))," ").concat(n,"ms ").concat(o)).join(","));this.manager.start([l,r,i2(i2({},s),{},{transition:f}),n,c])}render(){var e=this.props,{children:t,begin:r,duration:n,attributeName:i,easing:a,isActive:o,from:l,to:c,canBegin:u,onAnimationEnd:s,shouldReAnimate:f,onAnimationReStart:d,animationManager:h}=e,y=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,iQ),v=p.Children.count(t),m=this.state.style;if("function"==typeof t)return t(m);if(!o||0===v||n<=0)return t;var g=e=>{var{style:t={},className:r}=e.props;return(0,p.cloneElement)(e,i2(i2({},y),{},{style:i2(i2({},t),m),className:r}))};return 1===v?g(p.Children.only(t)):p.createElement("div",null,p.Children.map(t,e=>g(e)))}constructor(e,t){super(e,t),i5(this,"mounted",!1),i5(this,"manager",null),i5(this,"stopJSAnimation",null),i5(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:i,to:a,children:o,duration:l,animationManager:c}=this.props;if(this.manager=c,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||l<=0){this.state={style:{}},"function"==typeof o&&(this.state={style:a});return}if(i){if("function"==typeof o){this.state={style:i};return}this.state={style:n?{[n]:i}:i}}else this.state={style:{}}}}i5(i3,"displayName","Animate"),i5(i3,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var i4=(0,p.createContext)(null);function i6(e){var t,r,n,i,a,o,l,c=(0,p.useContext)(i4);return p.createElement(i3,i0({},e,{animationManager:null!=(o=null!=(l=e.animationManager)?l:c)?o:(t=new iJ,r=()=>null,n=!1,i=null,a=e=>{if(!n){if(Array.isArray(e)){if(!e.length)return;var[o,...l]=e;if("number"==typeof o){i=t.setTimeout(a.bind(null,l),o);return}a(o),i=t.setTimeout(a.bind(null,l));return}"object"==typeof e&&r(e),"function"==typeof e&&e()}},{stop:()=>{n=!0},start:e=>{n=!1,i&&(i(),i=null),a(e)},subscribe:e=>(r=e,()=>{r=()=>null}),getTimeoutController:()=>t})}))}function i8(){return(i8=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var i7=(e,t,r,n,i)=>{var a,o=Math.min(Math.abs(r)/2,Math.abs(n)/2),l=n>=0?1:-1,c=r>=0?1:-1,u=+(n>=0&&r>=0||n<0&&r<0);if(o>0&&i instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=i[f]>o?o:i[f];a="M".concat(e,",").concat(t+l*s[0]),s[0]>0&&(a+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(u,",").concat(e+c*s[0],",").concat(t)),a+="L ".concat(e+r-c*s[1],",").concat(t),s[1]>0&&(a+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(u,",\n        ").concat(e+r,",").concat(t+l*s[1])),a+="L ".concat(e+r,",").concat(t+n-l*s[2]),s[2]>0&&(a+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(u,",\n        ").concat(e+r-c*s[2],",").concat(t+n)),a+="L ".concat(e+c*s[3],",").concat(t+n),s[3]>0&&(a+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(u,",\n        ").concat(e,",").concat(t+n-l*s[3])),a+="Z"}else if(o>0&&i===+i&&i>0){var p=Math.min(o,i);a="M ".concat(e,",").concat(t+l*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(u,",").concat(e+c*p,",").concat(t,"\n            L ").concat(e+r-c*p,",").concat(t,"\n            A ").concat(p,",").concat(p,",0,0,").concat(u,",").concat(e+r,",").concat(t+l*p,"\n            L ").concat(e+r,",").concat(t+n-l*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(u,",").concat(e+r-c*p,",").concat(t+n,"\n            L ").concat(e+c*p,",").concat(t+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(u,",").concat(e,",").concat(t+n-l*p," Z")}else a="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return a},i9={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},ae=e=>{var t=iL(e,i9),r=(0,p.useRef)(null),[n,i]=(0,p.useState)(-1);(0,p.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&i(e)}catch(e){}},[]);var{x:a,y:o,width:l,height:c,radius:u,className:s}=t,{animationEasing:f,animationDuration:h,animationBegin:y,isAnimationActive:v,isUpdateAnimationActive:m}=t;if(a!==+a||o!==+o||l!==+l||c!==+c||0===l||0===c)return null;var g=d("recharts-rectangle",s);return m?p.createElement(i6,{canBegin:n>0,from:{width:l,height:c,x:a,y:o},to:{width:l,height:c,x:a,y:o},duration:h,animationEasing:f,isActive:m},e=>{var{width:i,height:a,x:o,y:l}=e;return p.createElement(i6,{canBegin:n>0,from:"0px ".concat(-1===n?1:n,"px"),to:"".concat(n,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:h,isActive:v,easing:f},p.createElement("path",i8({},U(t,!0),{className:g,d:i7(o,l,i,a,u),ref:r})))}):p.createElement("path",i8({},U(t,!0),{className:g,d:i7(a,o,l,c,u)}))};function at(e){var{cx:t,cy:r,radius:n,startAngle:i,endAngle:a}=e;return{points:[rV(t,r,n,i),rV(t,r,n,a)],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function ar(){return(ar=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var an=e=>{var{cx:t,cy:r,radius:n,angle:i,sign:a,isExternal:o,cornerRadius:l,cornerIsExternal:c}=e,u=l*(o?1:-1)+n,s=Math.asin(l/u)/rW,f=c?i:i+a*s,p=rV(t,r,u,f);return{center:p,circleTangency:rV(t,r,n,f),lineTangency:rV(t,r,u*Math.cos(s*rW),c?i-a*s:i),theta:s}},ai=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:a,endAngle:o}=e,l=((e,t)=>m(t-e)*Math.min(Math.abs(t-e),359.999))(a,o),c=a+l,u=rV(t,r,i,a),s=rV(t,r,i,c),f="M ".concat(u.x,",").concat(u.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(a>c),",\n    ").concat(s.x,",").concat(s.y,"\n  ");if(n>0){var p=rV(t,r,n,a),d=rV(t,r,n,c);f+="L ".concat(d.x,",").concat(d.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(a<=c),",\n            ").concat(p.x,",").concat(p.y," Z")}else f+="L ".concat(t,",").concat(r," Z");return f},aa={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},ao=e=>{var t,r=iL(e,aa),{cx:n,cy:i,innerRadius:a,outerRadius:o,cornerRadius:l,forceCornerRadius:c,cornerIsExternal:u,startAngle:s,endAngle:f,className:h}=r;if(o<a||s===f)return null;var y=d("recharts-sector",h),v=o-a,g=j(l,v,0,!0);return t=g>0&&360>Math.abs(s-f)?(e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,cornerRadius:a,forceCornerRadius:o,cornerIsExternal:l,startAngle:c,endAngle:u}=e,s=m(u-c),{circleTangency:f,lineTangency:p,theta:d}=an({cx:t,cy:r,radius:i,angle:c,sign:s,cornerRadius:a,cornerIsExternal:l}),{circleTangency:h,lineTangency:y,theta:v}=an({cx:t,cy:r,radius:i,angle:u,sign:-s,cornerRadius:a,cornerIsExternal:l}),g=l?Math.abs(c-u):Math.abs(c-u)-d-v;if(g<0)return o?"M ".concat(p.x,",").concat(p.y,"\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*a,",0\n        a").concat(a,",").concat(a,",0,0,1,").concat(-(2*a),",0\n      "):ai({cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:c,endAngle:u});var b="M ".concat(p.x,",").concat(p.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(s<0),",").concat(f.x,",").concat(f.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(g>180),",").concat(+(s<0),",").concat(h.x,",").concat(h.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(s<0),",").concat(y.x,",").concat(y.y,"\n  ");if(n>0){var{circleTangency:x,lineTangency:O,theta:w}=an({cx:t,cy:r,radius:n,angle:c,sign:s,isExternal:!0,cornerRadius:a,cornerIsExternal:l}),{circleTangency:P,lineTangency:j,theta:E}=an({cx:t,cy:r,radius:n,angle:u,sign:-s,isExternal:!0,cornerRadius:a,cornerIsExternal:l}),S=l?Math.abs(c-u):Math.abs(c-u)-w-E;if(S<0&&0===a)return"".concat(b,"L").concat(t,",").concat(r,"Z");b+="L".concat(j.x,",").concat(j.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(s<0),",").concat(P.x,",").concat(P.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(S>180),",").concat(+(s>0),",").concat(x.x,",").concat(x.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(s<0),",").concat(O.x,",").concat(O.y,"Z")}else b+="L".concat(t,",").concat(r,"Z");return b})({cx:n,cy:i,innerRadius:a,outerRadius:o,cornerRadius:Math.min(g,v/2),forceCornerRadius:c,cornerIsExternal:u,startAngle:s,endAngle:f}):ai({cx:n,cy:i,innerRadius:a,outerRadius:o,startAngle:s,endAngle:f}),p.createElement("path",ar({},U(r,!0),{className:y,d:t}))},al=r(83949),ac=r.n(al);function au(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function as(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class af extends Map{constructor(e,t=ad){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(ap(this,e))}has(e){return super.has(ap(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function ap({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function ad(e){return null!==e&&"object"==typeof e?e.valueOf():e}let ah=Symbol("implicit");function ay(){var e=new af,t=[],r=[],n=ah;function i(i){let a=e.get(i);if(void 0===a){if(n!==ah)return n;e.set(i,a=t.push(i)-1)}return r[a%r.length]}return i.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new af,r))e.has(n)||e.set(n,t.push(n)-1);return i},i.range=function(e){return arguments.length?(r=Array.from(e),i):r.slice()},i.unknown=function(e){return arguments.length?(n=e,i):n},i.copy=function(){return ay(t,r).unknown(n)},au.apply(i,arguments),i}function av(){var e,t,r=ay().unknown(void 0),n=r.domain,i=r.range,a=0,o=1,l=!1,c=0,u=0,s=.5;function f(){var r=n().length,f=o<a,p=f?o:a,d=f?a:o;e=(d-p)/Math.max(1,r-c+2*u),l&&(e=Math.floor(e)),p+=(d-p-e*(r-c))*s,t=e*(1-c),l&&(p=Math.round(p),t=Math.round(t));var h=(function(e,t,r){e*=1,t*=1,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((t-e)/r)),a=Array(i);++n<i;)a[n]=e+n*r;return a})(r).map(function(t){return p+e*t});return i(f?h.reverse():h)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([a,o]=e,a*=1,o*=1,f()):[a,o]},r.rangeRound=function(e){return[a,o]=e,a*=1,o*=1,l=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(l=!!e,f()):l},r.padding=function(e){return arguments.length?(c=Math.min(1,u=+e),f()):c},r.paddingInner=function(e){return arguments.length?(c=Math.min(1,e),f()):c},r.paddingOuter=function(e){return arguments.length?(u=+e,f()):u},r.align=function(e){return arguments.length?(s=Math.max(0,Math.min(1,e)),f()):s},r.copy=function(){return av(n(),[a,o]).round(l).paddingInner(c).paddingOuter(u).align(s)},au.apply(f(),arguments)}function am(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(av.apply(null,arguments).paddingInner(1))}let ag=Math.sqrt(50),ab=Math.sqrt(10),ax=Math.sqrt(2);function aO(e,t,r){let n,i,a,o=(t-e)/Math.max(0,r),l=Math.floor(Math.log10(o)),c=o/Math.pow(10,l),u=c>=ag?10:c>=ab?5:c>=ax?2:1;return(l<0?(n=Math.round(e*(a=Math.pow(10,-l)/u)),i=Math.round(t*a),n/a<e&&++n,i/a>t&&--i,a=-a):(n=Math.round(e/(a=Math.pow(10,l)*u)),i=Math.round(t/a),n*a<e&&++n,i*a>t&&--i),i<n&&.5<=r&&r<2)?aO(e,t,2*r):[n,i,a]}function aw(e,t,r){if(t*=1,e*=1,!((r*=1)>0))return[];if(e===t)return[e];let n=t<e,[i,a,o]=n?aO(t,e,r):aO(e,t,r);if(!(a>=i))return[];let l=a-i+1,c=Array(l);if(n)if(o<0)for(let e=0;e<l;++e)c[e]=-((a-e)/o);else for(let e=0;e<l;++e)c[e]=(a-e)*o;else if(o<0)for(let e=0;e<l;++e)c[e]=-((i+e)/o);else for(let e=0;e<l;++e)c[e]=(i+e)*o;return c}function aP(e,t,r){return aO(e*=1,t*=1,r*=1)[2]}function aj(e,t,r){t*=1,e*=1,r*=1;let n=t<e,i=n?aP(t,e,r):aP(e,t,r);return(n?-1:1)*(i<0?-(1/i):i)}function aE(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function aS(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function aA(e){let t,r,n;function i(e,n,a=0,o=e.length){if(a<o){if(0!==t(n,n))return o;do{let t=a+o>>>1;0>r(e[t],n)?a=t+1:o=t}while(a<o)}return a}return 2!==e.length?(t=aE,r=(t,r)=>aE(e(t),r),n=(t,r)=>e(t)-r):(t=e===aE||e===aS?e:ak,r=e,n=e),{left:i,center:function(e,t,r=0,a=e.length){let o=i(e,t,r,a-1);return o>r&&n(e[o-1],t)>-n(e[o],t)?o-1:o},right:function(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{let t=i+a>>>1;0>=r(e[t],n)?i=t+1:a=t}while(i<a)}return i}}}function ak(){return 0}function aM(e){return null===e?NaN:+e}let aT=aA(aE),aC=aT.right;function aD(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function aN(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function a_(){}aT.left,aA(aM).center;var aI="\\s*([+-]?\\d+)\\s*",aL="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",aR="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",aK=/^#([0-9a-f]{3,8})$/,az=RegExp(`^rgb\\(${aI},${aI},${aI}\\)$`),aB=RegExp(`^rgb\\(${aR},${aR},${aR}\\)$`),aF=RegExp(`^rgba\\(${aI},${aI},${aI},${aL}\\)$`),aU=RegExp(`^rgba\\(${aR},${aR},${aR},${aL}\\)$`),a$=RegExp(`^hsl\\(${aL},${aR},${aR}\\)$`),aW=RegExp(`^hsla\\(${aL},${aR},${aR},${aL}\\)$`),aV={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function aX(){return this.rgb().formatHex()}function aH(){return this.rgb().formatRgb()}function aq(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=aK.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?aY(t):3===r?new aJ(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?aG(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?aG(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=az.exec(e))?new aJ(t[1],t[2],t[3],1):(t=aB.exec(e))?new aJ(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=aF.exec(e))?aG(t[1],t[2],t[3],t[4]):(t=aU.exec(e))?aG(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=a$.exec(e))?a3(t[1],t[2]/100,t[3]/100,1):(t=aW.exec(e))?a3(t[1],t[2]/100,t[3]/100,t[4]):aV.hasOwnProperty(e)?aY(aV[e]):"transparent"===e?new aJ(NaN,NaN,NaN,0):null}function aY(e){return new aJ(e>>16&255,e>>8&255,255&e,1)}function aG(e,t,r,n){return n<=0&&(e=t=r=NaN),new aJ(e,t,r,n)}function aZ(e,t,r,n){var i;return 1==arguments.length?((i=e)instanceof a_||(i=aq(i)),i)?new aJ((i=i.rgb()).r,i.g,i.b,i.opacity):new aJ:new aJ(e,t,r,null==n?1:n)}function aJ(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function aQ(){return`#${a5(this.r)}${a5(this.g)}${a5(this.b)}`}function a0(){let e=a1(this.opacity);return`${1===e?"rgb(":"rgba("}${a2(this.r)}, ${a2(this.g)}, ${a2(this.b)}${1===e?")":`, ${e})`}`}function a1(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function a2(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function a5(e){return((e=a2(e))<16?"0":"")+e.toString(16)}function a3(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new a6(e,t,r,n)}function a4(e){if(e instanceof a6)return new a6(e.h,e.s,e.l,e.opacity);if(e instanceof a_||(e=aq(e)),!e)return new a6;if(e instanceof a6)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,l=a-i,c=(a+i)/2;return l?(o=t===a?(r-n)/l+(r<n)*6:r===a?(n-t)/l+2:(t-r)/l+4,l/=c<.5?a+i:2-a-i,o*=60):l=c>0&&c<1?0:o,new a6(o,l,c,e.opacity)}function a6(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function a8(e){return(e=(e||0)%360)<0?e+360:e}function a7(e){return Math.max(0,Math.min(1,e||0))}function a9(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function oe(e,t,r,n,i){var a=e*e,o=a*e;return((1-3*e+3*a-o)*t+(4-6*a+3*o)*r+(1+3*e+3*a-3*o)*n+o*i)/6}aD(a_,aq,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:aX,formatHex:aX,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return a4(this).formatHsl()},formatRgb:aH,toString:aH}),aD(aJ,aZ,aN(a_,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new aJ(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new aJ(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new aJ(a2(this.r),a2(this.g),a2(this.b),a1(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:aQ,formatHex:aQ,formatHex8:function(){return`#${a5(this.r)}${a5(this.g)}${a5(this.b)}${a5((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:a0,toString:a0})),aD(a6,function(e,t,r,n){return 1==arguments.length?a4(e):new a6(e,t,r,null==n?1:n)},aN(a_,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new a6(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new a6(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new aJ(a9(e>=240?e-240:e+120,i,n),a9(e,i,n),a9(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new a6(a8(this.h),a7(this.s),a7(this.l),a1(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=a1(this.opacity);return`${1===e?"hsl(":"hsla("}${a8(this.h)}, ${100*a7(this.s)}%, ${100*a7(this.l)}%${1===e?")":`, ${e})`}`}}));let ot=e=>()=>e;function or(e,t){var r=t-e;return r?function(t){return e+t*r}:ot(isNaN(e)?t:e)}let on=function e(t){var r,n=1==(r=+t)?or:function(e,t){var n,i,a;return t-e?(n=e,i=t,n=Math.pow(n,a=r),i=Math.pow(i,a)-n,a=1/a,function(e){return Math.pow(n+e*i,a)}):ot(isNaN(e)?t:e)};function i(e,t){var r=n((e=aZ(e)).r,(t=aZ(t)).r),i=n(e.g,t.g),a=n(e.b,t.b),o=or(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=i(t),e.b=a(t),e.opacity=o(t),e+""}}return i.gamma=e,i}(1);function oi(e){return function(t){var r,n,i=t.length,a=Array(i),o=Array(i),l=Array(i);for(r=0;r<i;++r)n=aZ(t[r]),a[r]=n.r||0,o[r]=n.g||0,l[r]=n.b||0;return a=e(a),o=e(o),l=e(l),n.opacity=1,function(e){return n.r=a(e),n.g=o(e),n.b=l(e),n+""}}}function oa(e,t){return e*=1,t*=1,function(r){return e*(1-r)+t*r}}oi(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),i=e[n],a=e[n+1],o=n>0?e[n-1]:2*i-a,l=n<t-1?e[n+2]:2*a-i;return oe((r-n/t)*t,o,i,a,l)}}),oi(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),i=e[(n+t-1)%t],a=e[n%t],o=e[(n+1)%t],l=e[(n+2)%t];return oe((r-n/t)*t,i,a,o,l)}});var oo=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ol=RegExp(oo.source,"g");function oc(e,t){var r,n,i=typeof t;return null==t||"boolean"===i?ot(t):("number"===i?oa:"string"===i?(n=aq(t))?(t=n,on):function(e,t){var r,n,i,a,o,l=oo.lastIndex=ol.lastIndex=0,c=-1,u=[],s=[];for(e+="",t+="";(i=oo.exec(e))&&(a=ol.exec(t));)(o=a.index)>l&&(o=t.slice(l,o),u[c]?u[c]+=o:u[++c]=o),(i=i[0])===(a=a[0])?u[c]?u[c]+=a:u[++c]=a:(u[++c]=null,s.push({i:c,x:oa(i,a)})),l=ol.lastIndex;return l<t.length&&(o=t.slice(l),u[c]?u[c]+=o:u[++c]=o),u.length<2?s[0]?(r=s[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=s.length,function(e){for(var r,n=0;n<t;++n)u[(r=s[n]).i]=r.x(e);return u.join("")})}:t instanceof aq?on:t instanceof Date?function(e,t){var r=new Date;return e*=1,t*=1,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,i=e?Math.min(n,e.length):0,a=Array(i),o=Array(n);for(r=0;r<i;++r)a[r]=oc(e[r],t[r]);for(;r<n;++r)o[r]=t[r];return function(e){for(r=0;r<i;++r)o[r]=a[r](e);return o}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},i={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=oc(e[r],t[r]):i[r]=t[r];return function(e){for(r in n)i[r]=n[r](e);return i}}:oa:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,i=t.slice();return function(a){for(r=0;r<n;++r)i[r]=e[r]*(1-a)+t[r]*a;return i}})(e,t)}function ou(e,t){return e*=1,t*=1,function(r){return Math.round(e*(1-r)+t*r)}}function os(e){return+e}var of=[0,1];function op(e){return e}function od(e,t){var r;return(t-=e*=1)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function oh(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=od(i,n),a=r(o,a)):(n=od(n,i),a=r(a,o)),function(e){return a(n(e))}}function oy(e,t,r){var n=Math.min(e.length,t.length)-1,i=Array(n),a=Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=od(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(t){var r=aC(e,t,1,n)-1;return a[r](i[r](t))}}function ov(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function om(){var e,t,r,n,i,a,o=of,l=of,c=oc,u=op;function s(){var e,t,r,c=Math.min(o.length,l.length);return u!==op&&(e=o[0],t=o[c-1],e>t&&(r=e,e=t,t=r),u=function(r){return Math.max(e,Math.min(t,r))}),n=c>2?oy:oh,i=a=null,f}function f(t){return null==t||isNaN(t*=1)?r:(i||(i=n(o.map(e),l,c)))(e(u(t)))}return f.invert=function(r){return u(t((a||(a=n(l,o.map(e),oa)))(r)))},f.domain=function(e){return arguments.length?(o=Array.from(e,os),s()):o.slice()},f.range=function(e){return arguments.length?(l=Array.from(e),s()):l.slice()},f.rangeRound=function(e){return l=Array.from(e),c=ou,s()},f.clamp=function(e){return arguments.length?(u=!!e||op,s()):u!==op},f.interpolate=function(e){return arguments.length?(c=e,s()):c},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,s()}}function og(){return om()(op,op)}var ob=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function ox(e){var t;if(!(t=ob.exec(e)))throw Error("invalid format: "+e);return new oO({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function oO(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function ow(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function oP(e){return(e=ow(Math.abs(e)))?e[1]:NaN}function oj(e,t){var r=ow(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}ox.prototype=oO.prototype,oO.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let oE={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>oj(100*e,t),r:oj,s:function(e,t){var r=ow(e,t);if(!r)return e+"";var n=r[0],a=r[1],o=a-(i=3*Math.max(-8,Math.min(8,Math.floor(a/3))))+1,l=n.length;return o===l?n:o>l?n+Array(o-l+1).join("0"):o>0?n.slice(0,o)+"."+n.slice(o):"0."+Array(1-o).join("0")+ow(e,Math.max(0,t+o-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function oS(e){return e}var oA=Array.prototype.map,ok=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function oM(e,t,r,n){var i,a,c=aj(e,t,r);switch((n=ox(null==n?",f":n)).type){case"s":var u=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(a=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(oP(u)/3)))-oP(Math.abs(c))))||(n.precision=a),l(n,u);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(a=Math.max(0,oP(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(i=Math.abs(i=c)))-oP(i))+1)||(n.precision=a-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(a=Math.max(0,-oP(Math.abs(c))))||(n.precision=a-("%"===n.type)*2)}return o(n)}function oT(e){var t=e.domain;return e.ticks=function(e){var r=t();return aw(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return oM(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,i,a=t(),o=0,l=a.length-1,c=a[o],u=a[l],s=10;for(u<c&&(i=c,c=u,u=i,i=o,o=l,l=i);s-- >0;){if((i=aP(c,u,r))===n)return a[o]=c,a[l]=u,t(a);if(i>0)c=Math.floor(c/i)*i,u=Math.ceil(u/i)*i;else if(i<0)c=Math.ceil(c*i)/i,u=Math.floor(u*i)/i;else break;n=i}return e},e}function oC(){var e=og();return e.copy=function(){return ov(e,oC())},au.apply(e,arguments),oT(e)}function oD(e,t){e=e.slice();var r,n=0,i=e.length-1,a=e[n],o=e[i];return o<a&&(r=n,n=i,i=r,r=a,a=o,o=r),e[n]=t.floor(a),e[i]=t.ceil(o),e}function oN(e){return Math.log(e)}function o_(e){return Math.exp(e)}function oI(e){return-Math.log(-e)}function oL(e){return-Math.exp(-e)}function oR(e){return isFinite(e)?+("1e"+e):e<0?0:e}function oK(e){return(t,r)=>-e(-t,r)}function oz(e){let t,r,n=e(oN,o_),i=n.domain,a=10;function l(){var o,l;return t=(o=a)===Math.E?Math.log:10===o&&Math.log10||2===o&&Math.log2||(o=Math.log(o),e=>Math.log(e)/o),r=10===(l=a)?oR:l===Math.E?Math.exp:e=>Math.pow(l,e),i()[0]<0?(t=oK(t),r=oK(r),e(oI,oL)):e(oN,o_),n}return n.base=function(e){return arguments.length?(a=+e,l()):a},n.domain=function(e){return arguments.length?(i(e),l()):i()},n.ticks=e=>{let n,o,l=i(),c=l[0],u=l[l.length-1],s=u<c;s&&([c,u]=[u,c]);let f=t(c),p=t(u),d=null==e?10:+e,h=[];if(!(a%1)&&p-f<d){if(f=Math.floor(f),p=Math.ceil(p),c>0){for(;f<=p;++f)for(n=1;n<a;++n)if(!((o=f<0?n/r(-f):n*r(f))<c)){if(o>u)break;h.push(o)}}else for(;f<=p;++f)for(n=a-1;n>=1;--n)if(!((o=f>0?n/r(-f):n*r(f))<c)){if(o>u)break;h.push(o)}2*h.length<d&&(h=aw(c,u,d))}else h=aw(f,p,Math.min(p-f,d)).map(r);return s?h.reverse():h},n.tickFormat=(e,i)=>{if(null==e&&(e=10),null==i&&(i=10===a?"s":","),"function"!=typeof i&&(a%1||null!=(i=ox(i)).precision||(i.trim=!0),i=o(i)),e===1/0)return i;let l=Math.max(1,a*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*a<a-.5&&(n*=a),n<=l?i(e):""}},n.nice=()=>i(oD(i(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function oB(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function oF(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function oU(e){var t=1,r=e(oB(1),oF(t));return r.constant=function(r){return arguments.length?e(oB(t=+r),oF(t)):t},oT(r)}function o$(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function oW(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function oV(e){return e<0?-e*e:e*e}function oX(e){var t=e(op,op),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(op,op):.5===r?e(oW,oV):e(o$(r),o$(1/r)):r},oT(t)}function oH(){var e=oX(om());return e.copy=function(){return ov(e,oH()).exponent(e.exponent())},au.apply(e,arguments),e}function oq(){return oH.apply(null,arguments).exponent(.5)}function oY(e){return Math.sign(e)*e*e}function oG(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function oZ(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}function oJ(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:+(e>t))}function oQ(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}o=(a=function(e){var t,r,n,a=void 0===e.grouping||void 0===e.thousands?oS:(t=oA.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var i=e.length,a=[],o=0,l=t[0],c=0;i>0&&l>0&&(c+l+1>n&&(l=Math.max(1,n-c)),a.push(e.substring(i-=l,i+l)),!((c+=l+1)>n));)l=t[o=(o+1)%t.length];return a.reverse().join(r)}),o=void 0===e.currency?"":e.currency[0]+"",l=void 0===e.currency?"":e.currency[1]+"",c=void 0===e.decimal?".":e.decimal+"",u=void 0===e.numerals?oS:(n=oA.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return n[+e]})}),s=void 0===e.percent?"%":e.percent+"",f=void 0===e.minus?"−":e.minus+"",p=void 0===e.nan?"NaN":e.nan+"";function d(e){var t=(e=ox(e)).fill,r=e.align,n=e.sign,d=e.symbol,h=e.zero,y=e.width,v=e.comma,m=e.precision,g=e.trim,b=e.type;"n"===b?(v=!0,b="g"):oE[b]||(void 0===m&&(m=12),g=!0,b="g"),(h||"0"===t&&"="===r)&&(h=!0,t="0",r="=");var x="$"===d?o:"#"===d&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",O="$"===d?l:/[%p]/.test(b)?s:"",w=oE[b],P=/[defgprs%]/.test(b);function j(e){var o,l,s,d=x,j=O;if("c"===b)j=w(e)+j,e="";else{var E=(e*=1)<0||1/e<0;if(e=isNaN(e)?p:w(Math.abs(e),m),g&&(e=function(e){e:for(var t,r=e.length,n=1,i=-1;n<r;++n)switch(e[n]){case".":i=t=n;break;case"0":0===i&&(i=n),t=n;break;default:if(!+e[n])break e;i>0&&(i=0)}return i>0?e.slice(0,i)+e.slice(t+1):e}(e)),E&&0==+e&&"+"!==n&&(E=!1),d=(E?"("===n?n:f:"-"===n||"("===n?"":n)+d,j=("s"===b?ok[8+i/3]:"")+j+(E&&"("===n?")":""),P){for(o=-1,l=e.length;++o<l;)if(48>(s=e.charCodeAt(o))||s>57){j=(46===s?c+e.slice(o+1):e.slice(o))+j,e=e.slice(0,o);break}}}v&&!h&&(e=a(e,1/0));var S=d.length+e.length+j.length,A=S<y?Array(y-S+1).join(t):"";switch(v&&h&&(e=a(A+e,A.length?y-j.length:1/0),A=""),r){case"<":e=d+e+j+A;break;case"=":e=d+A+e+j;break;case"^":e=A.slice(0,S=A.length>>1)+d+e+j+A.slice(S);break;default:e=A+d+e+j}return u(e)}return m=void 0===m?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,m)):Math.max(0,Math.min(20,m)),j.toString=function(){return e+""},j}return{format:d,formatPrefix:function(e,t){var r=d(((e=ox(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(oP(t)/3))),i=Math.pow(10,-n),a=ok[8+n/3];return function(e){return r(i*e)+a}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,l=a.formatPrefix;let o0=new Date,o1=new Date;function o2(e,t,r,n){function i(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return i.floor=t=>(e(t=new Date(+t)),t),i.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),i.round=e=>{let t=i(e),r=i.ceil(e);return e-t<r-e?t:r},i.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),i.range=(r,n,a)=>{let o,l=[];if(r=i.ceil(r),a=null==a?1:Math.floor(a),!(r<n)||!(a>0))return l;do l.push(o=new Date(+r)),t(r,a),e(r);while(o<r&&r<n);return l},i.filter=r=>o2(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e)if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}),r&&(i.count=(t,n)=>(o0.setTime(+t),o1.setTime(+n),e(o0),e(o1),Math.floor(r(o0,o1))),i.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?i.filter(n?t=>n(t)%e==0:t=>i.count(0,t)%e==0):i:null),i}let o5=o2(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);o5.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?o2(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):o5:null,o5.range;let o3=o2(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());o3.range;let o4=o2(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());o4.range;let o6=o2(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());o6.range;let o8=o2(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());o8.range;let o7=o2(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());o7.range;let o9=o2(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);o9.range;let le=o2(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);le.range;let lt=o2(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));function lr(e){return o2(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}lt.range;let ln=lr(0),li=lr(1),la=lr(2),lo=lr(3),ll=lr(4),lc=lr(5),lu=lr(6);function ls(e){return o2(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}ln.range,li.range,la.range,lo.range,ll.range,lc.range,lu.range;let lf=ls(0),lp=ls(1),ld=ls(2),lh=ls(3),ly=ls(4),lv=ls(5),lm=ls(6);lf.range,lp.range,ld.range,lh.range,ly.range,lv.range,lm.range;let lg=o2(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());lg.range;let lb=o2(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());lb.range;let lx=o2(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());lx.every=e=>isFinite(e=Math.floor(e))&&e>0?o2(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,lx.range;let lO=o2(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function lw(e,t,r,n,i,a){let o=[[o3,1,1e3],[o3,5,5e3],[o3,15,15e3],[o3,30,3e4],[a,1,6e4],[a,5,3e5],[a,15,9e5],[a,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function l(t,r,n){let i=Math.abs(r-t)/n,a=aA(([,,e])=>e).right(o,i);if(a===o.length)return e.every(aj(t/31536e6,r/31536e6,n));if(0===a)return o5.every(Math.max(aj(t,r,n),1));let[l,c]=o[i/o[a-1][2]<o[a][2]/i?a-1:a];return l.every(c)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let i=r&&"function"==typeof r.range?r:l(e,t,r),a=i?i.range(e,+t+1):[];return n?a.reverse():a},l]}lO.every=e=>isFinite(e=Math.floor(e))&&e>0?o2(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,lO.range;let[lP,lj]=lw(lO,lb,lf,lt,o7,o6),[lE,lS]=lw(lx,lg,ln,o9,o8,o4);function lA(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function lk(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function lM(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var lT={"-":"",_:" ",0:"0"},lC=/^\s*\d+/,lD=/^%/,lN=/[\\^$*+?|[\]().{}]/g;function l_(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?Array(r-a+1).join(t)+i:i)}function lI(e){return e.replace(lN,"\\$&")}function lL(e){return RegExp("^(?:"+e.map(lI).join("|")+")","i")}function lR(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function lK(e,t,r){var n=lC.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function lz(e,t,r){var n=lC.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function lB(e,t,r){var n=lC.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function lF(e,t,r){var n=lC.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function lU(e,t,r){var n=lC.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function l$(e,t,r){var n=lC.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function lW(e,t,r){var n=lC.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function lV(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function lX(e,t,r){var n=lC.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function lH(e,t,r){var n=lC.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function lq(e,t,r){var n=lC.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function lY(e,t,r){var n=lC.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function lG(e,t,r){var n=lC.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function lZ(e,t,r){var n=lC.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function lJ(e,t,r){var n=lC.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function lQ(e,t,r){var n=lC.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function l0(e,t,r){var n=lC.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function l1(e,t,r){var n=lD.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function l2(e,t,r){var n=lC.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function l5(e,t,r){var n=lC.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function l3(e,t){return l_(e.getDate(),t,2)}function l4(e,t){return l_(e.getHours(),t,2)}function l6(e,t){return l_(e.getHours()%12||12,t,2)}function l8(e,t){return l_(1+o9.count(lx(e),e),t,3)}function l7(e,t){return l_(e.getMilliseconds(),t,3)}function l9(e,t){return l7(e,t)+"000"}function ce(e,t){return l_(e.getMonth()+1,t,2)}function ct(e,t){return l_(e.getMinutes(),t,2)}function cr(e,t){return l_(e.getSeconds(),t,2)}function cn(e){var t=e.getDay();return 0===t?7:t}function ci(e,t){return l_(ln.count(lx(e)-1,e),t,2)}function ca(e){var t=e.getDay();return t>=4||0===t?ll(e):ll.ceil(e)}function co(e,t){return e=ca(e),l_(ll.count(lx(e),e)+(4===lx(e).getDay()),t,2)}function cl(e){return e.getDay()}function cc(e,t){return l_(li.count(lx(e)-1,e),t,2)}function cu(e,t){return l_(e.getFullYear()%100,t,2)}function cs(e,t){return l_((e=ca(e)).getFullYear()%100,t,2)}function cf(e,t){return l_(e.getFullYear()%1e4,t,4)}function cp(e,t){var r=e.getDay();return l_((e=r>=4||0===r?ll(e):ll.ceil(e)).getFullYear()%1e4,t,4)}function cd(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+l_(t/60|0,"0",2)+l_(t%60,"0",2)}function ch(e,t){return l_(e.getUTCDate(),t,2)}function cy(e,t){return l_(e.getUTCHours(),t,2)}function cv(e,t){return l_(e.getUTCHours()%12||12,t,2)}function cm(e,t){return l_(1+le.count(lO(e),e),t,3)}function cg(e,t){return l_(e.getUTCMilliseconds(),t,3)}function cb(e,t){return cg(e,t)+"000"}function cx(e,t){return l_(e.getUTCMonth()+1,t,2)}function cO(e,t){return l_(e.getUTCMinutes(),t,2)}function cw(e,t){return l_(e.getUTCSeconds(),t,2)}function cP(e){var t=e.getUTCDay();return 0===t?7:t}function cj(e,t){return l_(lf.count(lO(e)-1,e),t,2)}function cE(e){var t=e.getUTCDay();return t>=4||0===t?ly(e):ly.ceil(e)}function cS(e,t){return e=cE(e),l_(ly.count(lO(e),e)+(4===lO(e).getUTCDay()),t,2)}function cA(e){return e.getUTCDay()}function ck(e,t){return l_(lp.count(lO(e)-1,e),t,2)}function cM(e,t){return l_(e.getUTCFullYear()%100,t,2)}function cT(e,t){return l_((e=cE(e)).getUTCFullYear()%100,t,2)}function cC(e,t){return l_(e.getUTCFullYear()%1e4,t,4)}function cD(e,t){var r=e.getUTCDay();return l_((e=r>=4||0===r?ly(e):ly.ceil(e)).getUTCFullYear()%1e4,t,4)}function cN(){return"+0000"}function c_(){return"%"}function cI(e){return+e}function cL(e){return Math.floor(e/1e3)}function cR(e){return new Date(e)}function cK(e){return e instanceof Date?+e:+new Date(+e)}function cz(e,t,r,n,i,a,o,l,c,u){var s=og(),f=s.invert,p=s.domain,d=u(".%L"),h=u(":%S"),y=u("%I:%M"),v=u("%I %p"),m=u("%a %d"),g=u("%b %d"),b=u("%B"),x=u("%Y");function O(e){return(c(e)<e?d:l(e)<e?h:o(e)<e?y:a(e)<e?v:n(e)<e?i(e)<e?m:g:r(e)<e?b:x)(e)}return s.invert=function(e){return new Date(f(e))},s.domain=function(e){return arguments.length?p(Array.from(e,cK)):p().map(cR)},s.ticks=function(t){var r=p();return e(r[0],r[r.length-1],null==t?10:t)},s.tickFormat=function(e,t){return null==t?O:u(t)},s.nice=function(e){var r=p();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?p(oD(r,e)):s},s.copy=function(){return ov(s,cz(e,t,r,n,i,a,o,l,c,u))},s}function cB(){return au.apply(cz(lE,lS,lx,lg,ln,o9,o8,o4,o3,u).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function cF(){return au.apply(cz(lP,lj,lO,lb,lf,le,o7,o6,o3,s).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function cU(){var e,t,r,n,i,a=0,o=1,l=op,c=!1;function u(t){return null==t||isNaN(t*=1)?i:l(0===r?.5:(t=(n(t)-e)*r,c?Math.max(0,Math.min(1,t)):t))}function s(e){return function(t){var r,n;return arguments.length?([r,n]=t,l=e(r,n),u):[l(0),l(1)]}}return u.domain=function(i){return arguments.length?([a,o]=i,e=n(a*=1),t=n(o*=1),r=e===t?0:1/(t-e),u):[a,o]},u.clamp=function(e){return arguments.length?(c=!!e,u):c},u.interpolator=function(e){return arguments.length?(l=e,u):l},u.range=s(oc),u.rangeRound=s(ou),u.unknown=function(e){return arguments.length?(i=e,u):i},function(i){return n=i,e=i(a),t=i(o),r=e===t?0:1/(t-e),u}}function c$(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function cW(){var e=oX(cU());return e.copy=function(){return c$(e,cW()).exponent(e.exponent())},as.apply(e,arguments)}function cV(){return cW.apply(null,arguments).exponent(.5)}function cX(){var e,t,r,n,i,a,o,l=0,c=.5,u=1,s=1,f=op,p=!1;function d(e){return isNaN(e*=1)?o:(e=.5+((e=+a(e))-t)*(s*e<s*t?n:i),f(p?Math.max(0,Math.min(1,e)):e))}function h(e){return function(t){var r,n,i;return arguments.length?([r,n,i]=t,f=function(e,t){void 0===t&&(t=e,e=oc);for(var r=0,n=t.length-1,i=t[0],a=Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return a[t](e-t)}}(e,[r,n,i]),d):[f(0),f(.5),f(1)]}}return d.domain=function(o){return arguments.length?([l,c,u]=o,e=a(l*=1),t=a(c*=1),r=a(u*=1),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),s=t<e?-1:1,d):[l,c,u]},d.clamp=function(e){return arguments.length?(p=!!e,d):p},d.interpolator=function(e){return arguments.length?(f=e,d):f},d.range=h(oc),d.rangeRound=h(ou),d.unknown=function(e){return arguments.length?(o=e,d):o},function(o){return a=o,e=o(l),t=o(c),r=o(u),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),s=t<e?-1:1,d}}function cH(){var e=oX(cX());return e.copy=function(){return c$(e,cH()).exponent(e.exponent())},as.apply(e,arguments)}function cq(){return cH.apply(null,arguments).exponent(.5)}u=(c=function(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,l=e.months,c=e.shortMonths,u=lL(i),s=lR(i),f=lL(a),p=lR(a),d=lL(o),h=lR(o),y=lL(l),v=lR(l),m=lL(c),g=lR(c),b={a:function(e){return o[e.getDay()]},A:function(e){return a[e.getDay()]},b:function(e){return c[e.getMonth()]},B:function(e){return l[e.getMonth()]},c:null,d:l3,e:l3,f:l9,g:cs,G:cp,H:l4,I:l6,j:l8,L:l7,m:ce,M:ct,p:function(e){return i[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:cI,s:cL,S:cr,u:cn,U:ci,V:co,w:cl,W:cc,x:null,X:null,y:cu,Y:cf,Z:cd,"%":c_},x={a:function(e){return o[e.getUTCDay()]},A:function(e){return a[e.getUTCDay()]},b:function(e){return c[e.getUTCMonth()]},B:function(e){return l[e.getUTCMonth()]},c:null,d:ch,e:ch,f:cb,g:cT,G:cD,H:cy,I:cv,j:cm,L:cg,m:cx,M:cO,p:function(e){return i[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:cI,s:cL,S:cw,u:cP,U:cj,V:cS,w:cA,W:ck,x:null,X:null,y:cM,Y:cC,Z:cN,"%":c_},O={a:function(e,t,r){var n=d.exec(t.slice(r));return n?(e.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=m.exec(t.slice(r));return n?(e.m=g.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return j(e,t,r,n)},d:lq,e:lq,f:l0,g:lW,G:l$,H:lG,I:lG,j:lY,L:lQ,m:lH,M:lZ,p:function(e,t,r){var n=u.exec(t.slice(r));return n?(e.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:lX,Q:l2,s:l5,S:lJ,u:lz,U:lB,V:lF,w:lK,W:lU,x:function(e,t,n){return j(e,r,t,n)},X:function(e,t,r){return j(e,n,t,r)},y:lW,Y:l$,Z:lV,"%":l1};function w(e,t){return function(r){var n,i,a,o=[],l=-1,c=0,u=e.length;for(r instanceof Date||(r=new Date(+r));++l<u;)37===e.charCodeAt(l)&&(o.push(e.slice(c,l)),null!=(i=lT[n=e.charAt(++l)])?n=e.charAt(++l):i="e"===n?" ":"0",(a=t[n])&&(n=a(r,i)),o.push(n),c=l+1);return o.push(e.slice(c,l)),o.join("")}}function P(e,t){return function(r){var n,i,a=lM(1900,void 0,1);if(j(a,e,r+="",0)!=r.length)return null;if("Q"in a)return new Date(a.Q);if("s"in a)return new Date(1e3*a.s+("L"in a?a.L:0));if(!t||"Z"in a||(a.Z=0),"p"in a&&(a.H=a.H%12+12*a.p),void 0===a.m&&(a.m="q"in a?a.q:0),"V"in a){if(a.V<1||a.V>53)return null;"w"in a||(a.w=1),"Z"in a?(n=(i=(n=lk(lM(a.y,0,1))).getUTCDay())>4||0===i?lp.ceil(n):lp(n),n=le.offset(n,(a.V-1)*7),a.y=n.getUTCFullYear(),a.m=n.getUTCMonth(),a.d=n.getUTCDate()+(a.w+6)%7):(n=(i=(n=lA(lM(a.y,0,1))).getDay())>4||0===i?li.ceil(n):li(n),n=o9.offset(n,(a.V-1)*7),a.y=n.getFullYear(),a.m=n.getMonth(),a.d=n.getDate()+(a.w+6)%7)}else("W"in a||"U"in a)&&("w"in a||(a.w="u"in a?a.u%7:+("W"in a)),i="Z"in a?lk(lM(a.y,0,1)).getUTCDay():lA(lM(a.y,0,1)).getDay(),a.m=0,a.d="W"in a?(a.w+6)%7+7*a.W-(i+5)%7:a.w+7*a.U-(i+6)%7);return"Z"in a?(a.H+=a.Z/100|0,a.M+=a.Z%100,lk(a)):lA(a)}}function j(e,t,r,n){for(var i,a,o=0,l=t.length,c=r.length;o<l;){if(n>=c)return -1;if(37===(i=t.charCodeAt(o++))){if(!(a=O[(i=t.charAt(o++))in lT?t.charAt(o++):i])||(n=a(e,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return b.x=w(r,b),b.X=w(n,b),b.c=w(t,b),x.x=w(r,x),x.X=w(n,x),x.c=w(t,x),{format:function(e){var t=w(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=P(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=w(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=P(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,c.parse,s=c.utcFormat,c.utcParse;var cY=e=>e.chartData,cG=e3([cY],e=>{var t=null!=e.chartData?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}}),cZ=(e,t,r,n)=>n?cG(e):cY(e);function cJ(e){if(Array.isArray(e)&&2===e.length){var[t,r]=e;if(iO(t)&&iO(r))return!0}return!1}function cQ(e,t,r){return r?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}var c0=r(8870),c1=r.n(c0),c2=e=>e,c5={},c3=e=>function t(){let r;return 0==arguments.length||1==arguments.length&&(r=arguments.length<=0?void 0:arguments[0],r===c5)?t:e(...arguments)},c4=(e,t)=>1===e?t:c3(function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var a=n.filter(e=>e!==c5).length;return a>=e?t(...n):c4(e-a,c3(function(){for(var e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];return t(...n.map(e=>e===c5?r.shift():e),...r)}))}),c6=e=>c4(e.length,e),c8=(e,t)=>{for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},c7=c6((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map(e=>t[e]).map(e)),c9=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return c2;var n=t.reverse(),i=n[0],a=n.slice(1);return function(){return a.reduce((e,t)=>t(e),i(...arguments))}},ue=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),ut=e=>{var t=null,r=null;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return t&&i.every((e,r)=>{var n;return e===(null==(n=t)?void 0:n[r])})?r:(t=i,r=e(...i))}};function ur(e){return 0===e?1:Math.floor(new(c1())(e).abs().log(10).toNumber())+1}function un(e,t,r){for(var n=new(c1())(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}c6((e,t,r)=>{var n=+e;return n+r*(t-n)}),c6((e,t,r)=>{var n=t-e;return(r-e)/(n=n||1/0)}),c6((e,t,r)=>{var n=t-e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});var ui=e=>{var[t,r]=e,[n,i]=[t,r];return t>r&&([n,i]=[r,t]),[n,i]},ua=(e,t,r)=>{if(e.lte(0))return new(c1())(0);var n=ur(e.toNumber()),i=new(c1())(10).pow(n),a=e.div(i),o=1!==n?.05:.1,l=new(c1())(Math.ceil(a.div(o).toNumber())).add(r).mul(o).mul(i);return new(c1())(t?l.toNumber():Math.ceil(l.toNumber()))},uo=function(e,t,r,n){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new(c1())(0),tickMin:new(c1())(0),tickMax:new(c1())(0)};var o=ua(new(c1())(t).sub(e).div(r-1),n,a),l=Math.ceil((i=e<=0&&t>=0?new(c1())(0):(i=new(c1())(e).add(t).div(2)).sub(new(c1())(i).mod(o))).sub(e).div(o).toNumber()),c=Math.ceil(new(c1())(t).sub(i).div(o).toNumber()),u=l+c+1;return u>r?uo(e,t,r,n,a+1):(u<r&&(c=t>0?c+(r-u):c,l=t>0?l:l+(r-u)),{step:o,tickMin:i.sub(new(c1())(l).mul(o)),tickMax:i.add(new(c1())(c).mul(o))})},ul=ut(function(e){var[t,r]=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(n,2),[o,l]=ui([t,r]);if(o===-1/0||l===1/0){var c=l===1/0?[o,...c8(0,n-1).map(()=>1/0)]:[...c8(0,n-1).map(()=>-1/0),l];return t>r?ue(c):c}if(o===l){var u=new(c1())(1),s=new(c1())(o);if(!s.isint()&&i){var f=Math.abs(o);f<1?(u=new(c1())(10).pow(ur(o)-1),s=new(c1())(Math.floor(s.div(u).toNumber())).mul(u)):f>1&&(s=new(c1())(Math.floor(o)))}else 0===o?s=new(c1())(Math.floor((n-1)/2)):i||(s=new(c1())(Math.floor(o)));var p=Math.floor((n-1)/2);return c9(c7(e=>s.add(new(c1())(e-p).mul(u)).toNumber()),c8)(0,n)}var{step:d,tickMin:h,tickMax:y}=uo(o,l,a,i,0),v=un(h,y.add(new(c1())(.1).mul(d)),d);return t>r?ue(v):v}),uc=ut(function(e,t){var[r,n]=e,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[a,o]=ui([r,n]);if(a===-1/0||o===1/0)return[r,n];if(a===o)return[a];var l=Math.max(t,2),c=ua(new(c1())(o).sub(a).div(l-1),i,0),u=[...un(new(c1())(a),new(c1())(o),c),o];return!1===i&&(u=u.map(e=>Math.round(e))),r>n?ue(u):u}),uu=e=>e.rootProps.maxBarSize,us=e=>e.rootProps.barGap,uf=e=>e.rootProps.barCategoryGap,up=e=>e.rootProps.barSize,ud=e=>e.rootProps.stackOffset,uh=e=>e.options.chartName,uy=e=>e.rootProps.syncId,uv=e=>e.rootProps.syncMethod,um=e=>e.options.eventEmitter,ug={allowDuplicatedCategory:!0,angleAxisId:0,axisLine:!0,cx:0,cy:0,orientation:"outer",reversed:!1,scale:"auto",tick:!0,tickLine:!0,tickSize:8,type:"category"},ub={allowDataOverflow:!1,allowDuplicatedCategory:!0,angle:0,axisLine:!0,cx:0,cy:0,orientation:"right",radiusAxisId:0,scale:"auto",stroke:"#ccc",tick:!0,tickCount:5,type:"number"},ux=(e,t)=>{if(e&&t)return null!=e&&e.reversed?[t[1],t[0]]:t},uO={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:ug.angleAxisId,includeHidden:!1,name:void 0,reversed:ug.reversed,scale:ug.scale,tick:ug.tick,tickCount:void 0,ticks:void 0,type:ug.type,unit:void 0},uw={allowDataOverflow:ub.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:ub.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:ub.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:ub.scale,tick:ub.tick,tickCount:ub.tickCount,ticks:void 0,type:ub.type,unit:void 0},uP={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:ug.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:ug.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:ug.scale,tick:ug.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},uj={allowDataOverflow:ub.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:ub.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:ub.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:ub.scale,tick:ub.tick,tickCount:ub.tickCount,ticks:void 0,type:"category",unit:void 0},uE=(e,t)=>null!=e.polarAxis.angleAxis[t]?e.polarAxis.angleAxis[t]:"radial"===e.layout.layoutType?uP:uO,uS=(e,t)=>null!=e.polarAxis.radiusAxis[t]?e.polarAxis.radiusAxis[t]:"radial"===e.layout.layoutType?uj:uw,uA=e=>e.polarOptions,uk=e3([nt,nr,np],rX),uM=e3([uA,uk],(e,t)=>{if(null!=e)return j(e.innerRadius,t,0)}),uT=e3([uA,uk],(e,t)=>{if(null!=e)return j(e.outerRadius,t,.8*t)}),uC=e3([uA],e=>{if(null==e)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]}),uD=e3([uE,uC],ux),uN=e3([uk,uM,uT],(e,t,r)=>{if(null!=e&&null!=t&&null!=r)return[t,r]}),u_=e3([uS,uN],ux),uI=e3([nS,uA,uM,uT,nt,nr],(e,t,r,n,i,a)=>{if(("centric"===e||"radial"===e)&&null!=t&&null!=r&&null!=n){var{cx:o,cy:l,startAngle:c,endAngle:u}=t;return{cx:j(o,i,i/2),cy:j(l,a,a/2),innerRadius:r,outerRadius:n,startAngle:c,endAngle:u,clockWise:!1}}}),uL=(e,t)=>t,uR=(e,t,r)=>r;function uK(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function uz(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?uK(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):uK(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var uB=[0,"auto"],uF={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},uU=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return null==r?uF:r},u$={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:uB,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:60},uW=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return null==r?u$:r},uV={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},uX=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return null==r?uV:r},uH=(e,t,r)=>{switch(t){case"xAxis":return uU(e,r);case"yAxis":return uW(e,r);case"zAxis":return uX(e,r);case"angleAxis":return uE(e,r);case"radiusAxis":return uS(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},uq=(e,t,r)=>{switch(t){case"xAxis":return uU(e,r);case"yAxis":return uW(e,r);case"angleAxis":return uE(e,r);case"radiusAxis":return uS(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},uY=e=>e.graphicalItems.countOfBars>0;function uG(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var uZ=e=>e.graphicalItems.cartesianItems,uJ=e3([uL,uR],uG),uQ=(e,t,r)=>e.filter(r).filter(e=>(null==t?void 0:t.includeHidden)===!0||!e.hide),u0=e3([uZ,uH,uJ],uQ),u1=e=>e.filter(e=>void 0===e.stackId),u2=e3([u0],u1),u5=e=>e.map(e=>e.data).filter(Boolean).flat(1),u3=e3([u0],u5),u4=(e,t)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=t;return e.length>0?e:r.slice(n,i+1)},u6=e3([u3,cZ],u4),u8=(e,t,r)=>(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:rG(e,t.dataKey)})):r.length>0?r.map(e=>e.dataKey).flatMap(t=>e.map(e=>({value:rG(e,t)}))):e.map(e=>({value:e})),u7=e3([u6,uH,u0],u8);function u9(e,t){switch(e){case"xAxis":return"x"===t.direction;case"yAxis":return"y"===t.direction;default:return!1}}function se(e){return e.filter(e=>O(e)||e instanceof Date).map(Number).filter(e=>!1===g(e))}var st=(e,t,r)=>Object.fromEntries(Object.entries(t.reduce((e,t)=>(null==t.stackId||(null==e[t.stackId]&&(e[t.stackId]=[]),e[t.stackId].push(t)),e),{})).map(t=>{var[n,i]=t;return[n,{stackedData:((e,t,r)=>{var n=r1[r];return(function(){var e=ep([]),t=rz,r=rR,n=rB;function i(i){var a,o,l=Array.from(e.apply(this,arguments),rF),c=l.length,u=-1;for(let e of i)for(a=0,++u;a<c;++a)(l[a][u]=[0,+n(e,l[a].key,u,i)]).data=e;for(a=0,o=rK(t(l));a<c;++a)l[o[a]].index=a;return r(l,o),l}return i.keys=function(t){return arguments.length?(e="function"==typeof t?t:ep(Array.from(t)),i):e},i.value=function(e){return arguments.length?(n="function"==typeof e?e:ep(+e),i):n},i.order=function(e){return arguments.length?(t=null==e?rz:"function"==typeof e?e:ep(Array.from(e)),i):t},i.offset=function(e){return arguments.length?(r=null==e?rR:e,i):r},i})().keys(t).value((e,t)=>+rG(e,t,0)).order(rz).offset(n)(e)})(e,i.map(e=>e.dataKey),r),graphicalItems:i}]})),sr=e3([u6,u0,ud],st),sn=(e,t,r)=>{var{dataStartIndex:n,dataEndIndex:i}=t;if("zAxis"!==r){var a=((e,t,r)=>{if(null!=e)return(e=>[e[0]===1/0?0:e[0],e[1]===-1/0?0:e[1]])(Object.keys(e).reduce((n,i)=>{var{stackedData:a}=e[i],o=a.reduce((e,n)=>{var i=(e=>{var t=e.flat(2).filter(x);return[Math.min(...t),Math.max(...t)]})(n.slice(t,r+1));return[Math.min(e[0],i[0]),Math.max(e[1],i[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]))})(e,n,i);if(null==a||0!==a[0]||0!==a[1])return a}},si=e3([sr,cY,uL],sn),sa=(e,t,r,n)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var i,a,o=null==(i=r.errorBars)?void 0:i.filter(e=>u9(n,e)),l=rG(e,null!=(a=t.dataKey)?a:r.dataKey);return{value:l,errorDomain:function(e,t,r){return!r||"number"!=typeof t||g(t)||!r.length?[]:se(r.flatMap(r=>{var n,i,a=rG(e,r.dataKey);if(Array.isArray(a)?[n,i]=a:n=i=a,iO(n)&&iO(i))return[t-n,t+i]}))}(e,l,o)}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:rG(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]})),so=e3(u6,uH,u2,uL,sa);function sl(e){var{value:t}=e;if(O(t)||t instanceof Date)return t}var sc=e=>{var t;if(null==e||!("domain"in e))return uB;if(null!=e.domain)return e.domain;if(null!=e.ticks){if("number"===e.type){var r=se(e.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===e.type)return e.ticks.map(String)}return null!=(t=null==e?void 0:e.domain)?t:uB},su=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.filter(Boolean);if(0!==n.length){var i=n.flat();return[Math.min(...i),Math.max(...i)]}},ss=e=>e.referenceElements.dots,sf=(e,t,r)=>e.filter(e=>"extendDomain"===e.ifOverflow).filter(e=>"xAxis"===t?e.xAxisId===r:e.yAxisId===r),sp=e3([ss,uL,uR],sf),sd=e=>e.referenceElements.areas,sh=e3([sd,uL,uR],sf),sy=e=>e.referenceElements.lines,sv=e3([sy,uL,uR],sf),sm=(e,t)=>{var r=se(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},sg=e3(sp,uL,sm),sb=(e,t)=>{var r=se(e.flatMap(e=>["xAxis"===t?e.x1:e.y1,"xAxis"===t?e.x2:e.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},sx=e3([sh,uL],sb),sO=(e,t)=>{var r=se(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},sw=e3(sv,uL,sO),sP=e3(sg,sw,sx,(e,t,r)=>su(e,r,t)),sj=e3([uH],sc),sE=(e,t,r,n,i)=>{var a=function(e,t){if(t&&"function"!=typeof e&&Array.isArray(e)&&2===e.length){var r,n,[i,a]=e;if(iO(i))r=i;else if("function"==typeof i)return;if(iO(a))n=a;else if("function"==typeof a)return;var o=[r,n];if(cJ(o))return o}}(t,e.allowDataOverflow);return null!=a?a:function(e,t,r){if(r||null!=t){if("function"==typeof e&&null!=t)try{var n=e(t,r);if(cJ(n))return cQ(n,t,r)}catch(e){}if(Array.isArray(e)&&2===e.length){var i,a,[o,l]=e;if("auto"===o)null!=t&&(i=Math.min(...t));else if(x(o))i=o;else if("function"==typeof o)try{null!=t&&(i=o(null==t?void 0:t[0]))}catch(e){}else if("string"==typeof o&&r6.test(o)){var c=r6.exec(o);if(null==c||null==t)i=void 0;else{var u=+c[1];i=t[0]-u}}else i=null==t?void 0:t[0];if("auto"===l)null!=t&&(a=Math.max(...t));else if(x(l))a=l;else if("function"==typeof l)try{null!=t&&(a=l(null==t?void 0:t[1]))}catch(e){}else if("string"==typeof l&&r8.test(l)){var s=r8.exec(l);if(null==s||null==t)a=void 0;else{var f=+s[1];a=t[1]+f}}else a=null==t?void 0:t[1];var p=[i,a];if(cJ(p))return null==t?p:cQ(p,t,r)}}}(t,su(r,i,(e=>{var t=se(e.flatMap(e=>[e.value,e.errorDomain]).flat(1));if(0!==t.length)return[Math.min(...t),Math.max(...t)]})(n)),e.allowDataOverflow)},sS=e3([uH,sj,si,so,sP],sE),sA=[0,1],sk=(e,t,r,n,i,a,o)=>{if(null!=e&&null!=r&&0!==r.length){var{dataKey:l,type:c}=e,u=rZ(t,a);return u&&null==l?ac()(0,r.length):"category"===c?((e,t,r)=>{var n=e.map(sl).filter(e=>null!=e);return r&&(null==t.dataKey||t.allowDuplicatedCategory&&E(n))?ac()(0,e.length):t.allowDuplicatedCategory?n:Array.from(new Set(n))})(n,e,u):"expand"===i?sA:o}},sM=e3([uH,nS,u6,u7,ud,uL,sS],sk),sT=(e,t,r,n,i)=>{if(null!=e){var{scale:a,type:o}=e;if("auto"===a)return"radial"===t&&"radiusAxis"===i?"band":"radial"===t&&"angleAxis"===i?"linear":"category"===o&&n&&(n.indexOf("LineChart")>=0||n.indexOf("AreaChart")>=0||n.indexOf("ComposedChart")>=0&&!r)?"point":"category"===o?"band":"linear";if("string"==typeof a){var l="scale".concat(M(a));return l in f?l:"point"}}},sC=e3([uH,nS,uY,uh,uL],sT);function sD(e,t,r,n){if(null!=r&&null!=n){if("function"==typeof e.scale)return e.scale.copy().domain(r).range(n);var i=function(e){if(null!=e){if(e in f)return f[e]();var t="scale".concat(M(e));if(t in f)return f[t]()}}(t);if(null!=i){var a=i.domain(r).range(n);return(e=>{var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),i=Math.min(n[0],n[1])-1e-4,a=Math.max(n[0],n[1])+1e-4,o=e(t[0]),l=e(t[r-1]);(o<i||o>a||l<i||l>a)&&e.domain([t[0],t[r-1]])}})(a),a}}}var sN=(e,t,r)=>{var n=sc(t);if("auto"===r||"linear"===r){if(null!=t&&t.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&cJ(e))return ul(e,t.tickCount,t.allowDecimals);if(null!=t&&t.tickCount&&"number"===t.type&&cJ(e))return uc(e,t.tickCount,t.allowDecimals)}},s_=e3([sM,uq,sC],sN),sI=(e,t,r,n)=>"angleAxis"!==n&&(null==e?void 0:e.type)==="number"&&cJ(t)&&Array.isArray(r)&&r.length>0?[Math.min(t[0],r[0]),Math.max(t[1],r[r.length-1])]:t,sL=e3([uH,sM,s_,uL],sI),sR=e3(u7,uH,(e,t)=>{if(t&&"number"===t.type){var r=1/0,n=Array.from(se(e.map(e=>e.value))).sort((e,t)=>e-t);if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(0===i)return 1/0;for(var a=0;a<n.length-1;a++)r=Math.min(r,n[a+1]-n[a]);return r/i}}),sK=e3(sR,nS,uf,np,(e,t,r,n)=>n,(e,t,r,n,i)=>{if(!iO(e))return 0;var a="vertical"===t?n.height:n.width;if("gap"===i)return e*a/2;if("no-gap"===i){var o=j(r,e*a),l=e*a/2;return l-o-(l-o)/a*o}return 0}),sz=e3(uU,(e,t)=>{var r=uU(e,t);return null==r||"string"!=typeof r.padding?0:sK(e,"xAxis",t,r.padding)},(e,t)=>{if(null==e)return{left:0,right:0};var r,n,{padding:i}=e;return"string"==typeof i?{left:t,right:t}:{left:(null!=(r=i.left)?r:0)+t,right:(null!=(n=i.right)?n:0)+t}}),sB=e3(uW,(e,t)=>{var r=uW(e,t);return null==r||"string"!=typeof r.padding?0:sK(e,"yAxis",t,r.padding)},(e,t)=>{if(null==e)return{top:0,bottom:0};var r,n,{padding:i}=e;return"string"==typeof i?{top:t,bottom:t}:{top:(null!=(r=i.top)?r:0)+t,bottom:(null!=(n=i.bottom)?n:0)+t}}),sF=e3([np,sz,nb,ng,(e,t,r)=>r],(e,t,r,n,i)=>{var{padding:a}=n;return i?[a.left,r.width-a.right]:[e.left+t.left,e.left+e.width-t.right]}),sU=e3([np,nS,sB,nb,ng,(e,t,r)=>r],(e,t,r,n,i,a)=>{var{padding:o}=i;return a?[n.height-o.bottom,o.top]:"horizontal"===t?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]}),s$=(e,t,r,n)=>{var i;switch(t){case"xAxis":return sF(e,r,n);case"yAxis":return sU(e,r,n);case"zAxis":return null==(i=uX(e,r))?void 0:i.range;case"angleAxis":return uC(e);case"radiusAxis":return uN(e,r);default:return}},sW=e3([uH,s$],ux),sV=e3([uH,sC,sL,sW],sD);function sX(e,t){return e.id<t.id?-1:+(e.id>t.id)}e3(u0,uL,(e,t)=>e.flatMap(e=>{var t;return null!=(t=e.errorBars)?t:[]}).filter(e=>u9(t,e)));var sH=(e,t)=>t,sq=(e,t,r)=>r,sY=e3(na,sH,sq,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(sX)),sG=e3(no,sH,sq,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(sX)),sZ=(e,t)=>({width:e.width,height:t.height}),sJ=e3(np,uU,sZ),sQ=e3(nr,np,sY,sH,sq,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=sZ(t,r);null==a&&(a=((e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}})(t,n,e));var c="top"===n&&!i||"bottom"===n&&i;o[r.id]=a-Number(c)*l.height,a+=(c?-1:1)*l.height}),o}),s0=e3(nt,np,sG,sH,sq,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=((e,t)=>({width:"number"==typeof t.width?t.width:60,height:e.height}))(t,r);null==a&&(a=((e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}})(t,n,e));var c="left"===n&&!i||"right"===n&&i;o[r.id]=a-Number(c)*l.width,a+=(c?-1:1)*l.width}),o}),s1=e3(np,uW,(e,t)=>({width:"number"==typeof t.width?t.width:60,height:e.height})),s2=(e,t,r)=>{switch(t){case"xAxis":return sJ(e,r).width;case"yAxis":return s1(e,r).height;default:return}},s5=(e,t,r,n)=>{if(null!=r){var{allowDuplicatedCategory:i,type:a,dataKey:o}=r,l=rZ(e,n),c=t.map(e=>e.value);if(o&&l&&"category"===a&&i&&E(c))return c}},s3=e3([nS,u7,uH,uL],s5),s4=(e,t,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:a}=r;if(rZ(e,n)&&("number"===i||"auto"!==a))return t.map(e=>e.value)}},s6=e3([nS,u7,uq,uL],s4),s8=e3([nS,(e,t,r)=>{switch(t){case"xAxis":return uU(e,r);case"yAxis":return uW(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},sC,sV,s3,s6,s$,s_,uL],(e,t,r,n,i,a,o,l,c)=>{if(null==t)return null;var u=rZ(e,c);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:c,categoricalDomain:a,duplicateDomain:i,isCategorical:u,niceTicks:l,range:o,realScaleType:r,scale:n}}),s7=(e,t,r,n,i,a,o,l,c)=>{if(null!=t&&null!=n){var u=rZ(e,c),{type:s,ticks:f,tickCount:p}=t,d="scaleBand"===r&&"function"==typeof n.bandwidth?n.bandwidth()/2:2,h="category"===s&&n.bandwidth?n.bandwidth()/d:0;h="angleAxis"===c&&null!=a&&a.length>=2?2*m(a[0]-a[1])*h:h;var y=f||i;return y?y.map((e,t)=>({index:t,coordinate:n(o?o.indexOf(e):e)+h,value:e,offset:h})).filter(e=>!g(e.coordinate)):u&&l?l.map((e,t)=>({coordinate:n(e)+h,value:e,index:t,offset:h})):n.ticks?n.ticks(p).map(e=>({coordinate:n(e)+h,value:e,offset:h})):n.domain().map((e,t)=>({coordinate:n(e)+h,value:o?o[e]:e,index:t,offset:h}))}},s9=e3([nS,uq,sC,sV,s_,s$,s3,s6,uL],s7),fe=(e,t,r,n,i,a,o)=>{if(null!=t&&null!=r&&null!=n&&n[0]!==n[1]){var l=rZ(e,o),{tickCount:c}=t,u=0;return(u="angleAxis"===o&&(null==n?void 0:n.length)>=2?2*m(n[0]-n[1])*u:u,l&&a)?a.map((e,t)=>({coordinate:r(e)+u,value:e,index:t,offset:u})):r.ticks?r.ticks(c).map(e=>({coordinate:r(e)+u,value:e,offset:u})):r.domain().map((e,t)=>({coordinate:r(e)+u,value:i?i[e]:e,index:t,offset:u}))}},ft=e3([nS,uq,sV,s$,s3,s6,uL],fe),fr=e3(uH,sV,(e,t)=>{if(null!=e&&null!=t)return uz(uz({},e),{},{scale:t})}),fn=e3([uH,sC,sM,sW],sD),fi=e3((e,t,r)=>uX(e,r),fn,(e,t)=>{if(null!=e&&null!=t)return uz(uz({},e),{},{scale:t})}),fa=e3([nS,na,no],(e,t,r)=>{switch(e){case"horizontal":return t.some(e=>e.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(e=>e.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}}),fo=e=>e.options.defaultTooltipEventType,fl=e=>e.options.validateTooltipEventTypes;function fc(e,t,r){if(null==e)return t;var n=e?"axis":"item";return null==r?t:r.includes(n)?n:t}function fu(e,t){return fc(t,fo(e),fl(e))}var fs=(e,t)=>{var r,n=Number(t);if(!g(n)&&null!=t)return n>=0?null==e||null==(r=e[n])?void 0:r.value:void 0},ff={active:!1,index:null,dataKey:void 0,coordinate:void 0},fp=t8({name:"tooltip",initialState:{itemInteraction:{click:ff,hover:ff},axisInteraction:{click:ff,hover:ff},keyboardInteraction:ff,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push(t.payload)},removeTooltipEntrySettings(e,t){var r=tY(e).tooltipItemPayloads.indexOf(t.payload);r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:fd,removeTooltipEntrySettings:fh,setTooltipSettingsState:fy,setActiveMouseOverItemIndex:fv,mouseLeaveItem:fm,mouseLeaveChart:fg,setActiveClickItemIndex:fb,setMouseOverAxisIndex:fx,setMouseClickAxisIndex:fO,setSyncInteraction:fw,setKeyboardInteraction:fP}=fp.actions,fj=fp.reducer;function fE(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fS(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fE(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fE(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var fA=(e,t,r,n)=>{if(null==t)return ff;var i=function(e,t,r){return"axis"===t?"click"===r?e.axisInteraction.click:e.axisInteraction.hover:"click"===r?e.itemInteraction.click:e.itemInteraction.hover}(e,t,r);if(null==i)return ff;if(i.active)return i;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&null!=e.syncInteraction.index)return e.syncInteraction;var a=!0===e.settings.active;if(null!=i.index){if(a)return fS(fS({},i),{},{active:!0})}else if(null!=n)return{active:!0,coordinate:void 0,dataKey:void 0,index:n};return fS(fS({},ff),{},{coordinate:i.coordinate})},fk=(e,t)=>{var r=null==e?void 0:e.index;if(null==r)return null;var n=Number(r);if(!iO(n))return r;var i=Infinity;return t.length>0&&(i=t.length-1),String(Math.max(0,Math.min(n,i)))},fM=(e,t,r,n,i,a,o,l)=>{if(null!=a&&null!=l){var c=o[0],u=null==c?void 0:l(c.positions,a);if(null!=u)return u;var s=null==i?void 0:i[Number(a)];if(s)if("horizontal"===r)return{x:s.coordinate,y:(n.top+t)/2};else return{x:(n.left+e)/2,y:s.coordinate}}},fT=(e,t,r,n)=>{var i;return"axis"===t?e.tooltipItemPayloads:0===e.tooltipItemPayloads.length?[]:null==(i="hover"===r?e.itemInteraction.hover.dataKey:e.itemInteraction.click.dataKey)&&null!=n?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter(e=>{var t;return(null==(t=e.settings)?void 0:t.dataKey)===i})},fC=e=>e.options.tooltipPayloadSearcher,fD=e=>e.tooltip;function fN(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f_(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fN(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fN(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var fI=(e,t,r,n,i,a,o)=>{if(null!=t&&null!=a){var{chartData:l,computedData:c,dataStartIndex:u,dataEndIndex:s}=r;return e.reduce((e,r)=>{var f,p,d,h,y,{dataDefinedOnItem:v,settings:m}=r,g=function(e,t,r){return Array.isArray(e)&&e&&t+r!==0?e.slice(t,r+1):e}((f=v,p=l,null!=f?f:p),u,s),b=null!=(d=null==m?void 0:m.dataKey)?d:null==n?void 0:n.dataKey,x=null==m?void 0:m.nameKey;return Array.isArray(h=null!=n&&n.dataKey&&Array.isArray(g)&&!Array.isArray(g[0])&&"axis"===o?k(g,n.dataKey,i):a(g,t,c,x))?h.forEach(t=>{var r=f_(f_({},m),{},{name:t.name,unit:t.unit,color:void 0,fill:void 0});e.push(r9({tooltipEntrySettings:r,dataKey:t.dataKey,payload:t.payload,value:rG(t.payload,t.dataKey),name:t.name}))}):e.push(r9({tooltipEntrySettings:m,dataKey:b,payload:h,value:rG(h,b),name:null!=(y=rG(h,x))?y:null==m?void 0:m.name})),e},[])}},fL=e=>{var t=nS(e);return"horizontal"===t?"xAxis":"vertical"===t?"yAxis":"centric"===t?"angleAxis":"radiusAxis"},fR=e=>e.tooltip.settings.axisId,fK=e=>{var t=fL(e),r=fR(e);return uq(e,t,r)},fz=e3([fK,nS,uY,uh,fL],sT),fB=e3([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],(e,t)=>[...e,...t]),fF=e3([fL,fR],uG),fU=e3([fB,fK,fF],uQ),f$=e3([fU],u5),fW=e3([f$,cY],u4),fV=e3([fW,fK,fU],u8),fX=e3([fK],sc),fH=e3([fW,fU,ud],st),fq=e3([fH,cY,fL],sn),fY=e3([fU],u1),fG=e3([fW,fK,fY,fL],sa),fZ=e3([ss,fL,fR],sf),fJ=e3([fZ,fL],sm),fQ=e3([sd,fL,fR],sf),f0=e3([fQ,fL],sb),f1=e3([sy,fL,fR],sf),f2=e3([f1,fL],sO),f5=e3([fJ,f2,f0],su),f3=e3([fK,fX,fq,fG,f5],sE),f4=e3([fK,nS,fW,fV,ud,fL,f3],sk),f6=e3([f4,fK,fz],sN),f8=e3([fK,f4,f6,fL],sI),f7=e=>{var t=fL(e),r=fR(e);return s$(e,t,r,!1)},f9=e3([fK,f7],ux),pe=e3([fK,fz,f8,f9],sD),pt=e3([nS,fV,fK,fL],s5),pr=e3([nS,fV,fK,fL],s4),pn=e3([nS,fK,fz,pe,f7,pt,pr,fL],(e,t,r,n,i,a,o,l)=>{if(t){var{type:c}=t,u=rZ(e,l);if(n){var s="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,f="category"===c&&n.bandwidth?n.bandwidth()/s:0;return(f="angleAxis"===l&&null!=i&&(null==i?void 0:i.length)>=2?2*m(i[0]-i[1])*f:f,u&&o)?o.map((e,t)=>({coordinate:n(e)+f,value:e,index:t,offset:f})):n.domain().map((e,t)=>({coordinate:n(e)+f,value:a?a[e]:e,index:t,offset:f}))}}}),pi=e3([fo,fl,e=>e.tooltip.settings],(e,t,r)=>fc(r.shared,e,t)),pa=e=>e.tooltip.settings.trigger,po=e=>e.tooltip.settings.defaultIndex,pl=e3([fD,pi,pa,po],fA),pc=e3([pl,fW],fk),pu=e3([pn,pc],fs),ps=e3([pl],e=>{if(e)return e.dataKey}),pf=e3([fD,pi,pa,po],fT),pp=e3([nt,nr,nS,np,pn,po,pf,fC],fM),pd=e3([pl,pp],(e,t)=>null!=e&&e.coordinate?e.coordinate:t),ph=e3([pl],e=>e.active),py=e3([pf,pc,cY,fK,pu,fC,pi],fI),pv=e3([py],e=>{if(null!=e)return Array.from(new Set(e.map(e=>e.payload).filter(e=>null!=e)))});function pm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pg(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pm(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pm(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var pb=()=>eB(uh),px=(e,t)=>t,pO=(e,t,r)=>r,pw=(e,t,r,n)=>n,pP=e3(pn,e=>e8()(e,e=>e.coordinate)),pj=e3([fD,px,pO,pw],fA),pE=e3([pj,fW],fk),pS=e3([fD,px,pO,pw],fT),pA=e3([nt,nr,nS,np,pn,pw,pS,fC],fM),pk=e3([pj,pA],(e,t)=>{var r;return null!=(r=e.coordinate)?r:t}),pM=e3(pn,pE,fs),pT=e3([pS,pE,cY,fK,pM,fC,px],fI),pC=e3([pj],e=>({isActive:e.active,activeIndex:e.index}));function pD(){return(pD=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function pN(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p_(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pN(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pN(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pI(e){var t,r,n,{coordinate:i,payload:a,index:o,offset:l,tooltipAxisBandSize:c,layout:u,cursor:s,tooltipEventType:f,chartName:h}=e;if(!s||!i||"ScatterChart"!==h&&"axis"!==f)return null;if("ScatterChart"===h)r=i,n=i_;else if("BarChart"===h)t=c/2,r={stroke:"none",fill:"#ccc",x:"horizontal"===u?i.x-t:l.left+.5,y:"horizontal"===u?l.top+.5:i.y-t,width:"horizontal"===u?c:l.width-1,height:"horizontal"===u?l.height-1:c},n=ae;else if("radial"===u){var{cx:y,cy:v,radius:m,startAngle:g,endAngle:b}=at(i);r={cx:y,cy:v,startAngle:g,endAngle:b,innerRadius:m,outerRadius:m},n=ao}else r={points:function(e,t,r){var n,i,a,o;if("horizontal"===e)a=n=t.x,i=r.top,o=r.top+r.height;else if("vertical"===e)o=i=t.y,n=r.left,a=r.left+r.width;else if(null!=t.cx&&null!=t.cy)if("centric"!==e)return at(t);else{var{cx:l,cy:c,innerRadius:u,outerRadius:s,angle:f}=t,p=rV(l,c,u,f),d=rV(l,c,s,f);n=p.x,i=p.y,a=d.x,o=d.y}return[{x:n,y:i},{x:a,y:o}]}(u,i,l)},n=iT;var x="object"==typeof s&&"className"in s?s.className:void 0,O=p_(p_(p_(p_({stroke:"#ccc",pointerEvents:"none"},l),r),U(s,!1)),{},{payload:a,payloadIndex:o,className:d("recharts-tooltip-cursor",x)});return(0,p.isValidElement)(s)?(0,p.cloneElement)(s,O):(0,p.createElement)(n,O)}function pL(e){var t,r,n,i=(t=eB(fK),r=eB(pn),n=eB(pe),r7(pg(pg({},t),{},{scale:n}),r)),a=nw(),o=nA(),l=pb();return p.createElement(pI,pD({},e,{coordinate:e.coordinate,index:e.index,payload:e.payload,offset:a,layout:o,tooltipAxisBandSize:i,chartName:l}))}var pR=(0,p.createContext)(null),pK=new(r(82661)),pz="recharts.syncEvent.tooltip",pB="recharts.syncEvent.brush";function pF(e,t){if(t){var r=Number.parseInt(t,10);if(!g(r))return null==e?void 0:e[r]}}var pU=t8({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:e=>{null==e.eventEmitter&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),p$=pU.reducer,{createEventEmitter:pW}=pU.actions;function pV(e){return e.tooltip.syncInteraction}var pX=t8({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(e,t){if(e.chartData=t.payload,null==t.payload){e.dataStartIndex=0,e.dataEndIndex=0;return}t.payload.length>0&&e.dataEndIndex!==t.payload.length-1&&(e.dataEndIndex=t.payload.length-1)},setComputedData(e,t){e.computedData=t.payload},setDataStartEndIndexes(e,t){var{startIndex:r,endIndex:n}=t.payload;null!=r&&(e.dataStartIndex=r),null!=n&&(e.dataEndIndex=n)}}}),{setChartData:pH,setDataStartEndIndexes:pq,setComputedData:pY}=pX.actions,pG=pX.reducer,pZ=()=>{};function pJ(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pQ(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pJ(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pJ(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function p0(e){return e.dataKey}var p1=[],p2={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!n4.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function p5(e){var t,r,n,i,a,o,l,c=iL(e,p2),{active:u,allowEscapeViewBox:s,animationDuration:f,animationEasing:d,content:h,filterNull:y,isAnimationActive:v,offset:m,payloadUniqBy:g,position:b,reverseDirection:x,useTranslate3d:O,wrapperStyle:w,cursor:P,shared:j,trigger:E,defaultIndex:S,portal:A,axisId:k}=c,M=eL(),T="number"==typeof S?String(S):S;(0,p.useEffect)(()=>{M(fy({shared:j,trigger:E,axisId:k,active:u,defaultIndex:T}))},[M,j,E,k,u,T]);var C=nx(),D=n6(),N=eB(e=>fu(e,j)),{activeIndex:_,isActive:I}=eB(e=>pC(e,N,E,T)),L=eB(e=>pT(e,N,E,T)),R=eB(e=>pM(e,N,E,T)),K=eB(e=>pk(e,N,E,T)),z=(0,p.useContext)(pR),B=null!=u?u:I,[F,U]=te([L,B]),$="axis"===N?R:void 0;t=eB(e=>((e,t,r)=>{if(null!=t){var n=fD(e);return"axis"===t?"hover"===r?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:"hover"===r?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}})(e,N,E)),r=eB(um),n=eB(uy),i=eB(uv),o=null==(a=eB(pV))?void 0:a.active,(0,p.useEffect)(()=>{if(!o&&null!=n&&null!=r){var e=fw({active:B,coordinate:K,dataKey:t,index:_,label:"number"==typeof $?String($):$});pK.emit(pz,n,e,r)}},[o,K,t,_,$,r,n,i,B]);var W=null!=A?A:z;if(null==W)return null;var V=null!=L?L:p1;B||(V=p1),y&&V.length&&(V=eD(L.filter(e=>null!=e.value&&(!0!==e.hide||c.includeHidden)),g,p0));var X=V.length>0,H=p.createElement(n3,{allowEscapeViewBox:s,animationDuration:f,animationEasing:d,isAnimationActive:v,active:B,coordinate:K,hasPayload:X,offset:m,position:b,reverseDirection:x,useTranslate3d:O,viewBox:C,wrapperStyle:w,lastBoundingBox:F,innerRef:U,hasPortalFromProps:!!A},(l=pQ(pQ({},c),{},{payload:V,label:$,active:B,coordinate:K,accessibilityLayer:D}),p.isValidElement(h)?p.cloneElement(h,l):"function"==typeof h?p.createElement(h,l):p.createElement(nZ,l)));return p.createElement(p.Fragment,null,(0,Y.createPortal)(H,W),B&&p.createElement(pL,{cursor:P,tooltipEventType:N,coordinate:K,payload:L,index:_}))}var p3=r(20400),p4=r.n(p3),p6=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]};function p8(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p7(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p8(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p8(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var p9=(0,p.forwardRef)((e,t)=>{var{aspect:r,initialDimension:n={width:-1,height:-1},width:i="100%",height:a="100%",minWidth:o=0,minHeight:l,maxHeight:c,children:u,debounce:s=0,id:f,className:h,onResize:y,style:v={}}=e,m=(0,p.useRef)(null),g=(0,p.useRef)();g.current=y,(0,p.useImperativeHandle)(t,()=>m.current);var[x,O]=(0,p.useState)({containerWidth:n.width,containerHeight:n.height}),w=(0,p.useCallback)((e,t)=>{O(r=>{var n=Math.round(e),i=Math.round(t);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,p.useEffect)(()=>{var e=e=>{var t,{width:r,height:n}=e[0].contentRect;w(r,n),null==(t=g.current)||t.call(g,r,n)};s>0&&(e=p4()(e,s,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),{width:r,height:n}=m.current.getBoundingClientRect();return w(r,n),t.observe(m.current),()=>{t.disconnect()}},[w,s]);var P=(0,p.useMemo)(()=>{var{containerWidth:e,containerHeight:t}=x;if(e<0||t<0)return null;p6(b(i)||b(a),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",i,a),p6(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=b(i)?e:i,s=b(a)?t:a;return r&&r>0&&(n?s=n/r:s&&(n=s*r),c&&s>c&&(s=c)),p6(n>0||s>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,s,i,a,o,l,r),p.Children.map(u,e=>(0,p.cloneElement)(e,{width:n,height:s,style:p7({width:n,height:s},e.props.style)}))},[r,u,a,c,l,o,x,i]);return p.createElement("div",{id:f?"".concat(f):void 0,className:d("recharts-responsive-container",h),style:p7(p7({},v),{},{width:i,height:a,minWidth:o,minHeight:l,maxHeight:c}),ref:m},p.createElement("div",{style:{width:0,height:0,overflow:"visible"}},P))}),de=e=>null;function dt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dr(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dt(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dt(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}de.displayName="Cell";var dn={widthCache:{},cacheCount:0},di={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},da="recharts_measurement_span",dl=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||n4.isSsr)return{width:0,height:0};var n=(Object.keys(t=dr({},r)).forEach(e=>{t[e]||delete t[e]}),t),i=JSON.stringify({text:e,copyStyle:n});if(dn.widthCache[i])return dn.widthCache[i];try{var a=document.getElementById(da);a||((a=document.createElement("span")).setAttribute("id",da),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=dr(dr({},di),n);Object.assign(a.style,o),a.textContent="".concat(e);var l=a.getBoundingClientRect(),c={width:l.width,height:l.height};return dn.widthCache[i]=c,++dn.cacheCount>2e3&&(dn.cacheCount=0,dn.widthCache={}),c}catch(e){return{width:0,height:0}}},dc=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,du=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,ds=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,df=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,dp={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},dd=Object.keys(dp);class dh{static parse(e){var t,[,r,n]=null!=(t=df.exec(e))?t:[];return new dh(parseFloat(r),null!=n?n:"")}add(e){return this.unit!==e.unit?new dh(NaN,""):new dh(this.num+e.num,this.unit)}subtract(e){return this.unit!==e.unit?new dh(NaN,""):new dh(this.num-e.num,this.unit)}multiply(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new dh(NaN,""):new dh(this.num*e.num,this.unit||e.unit)}divide(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new dh(NaN,""):new dh(this.num/e.num,this.unit||e.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return g(this.num)}constructor(e,t){this.num=e,this.unit=t,this.num=e,this.unit=t,g(e)&&(this.unit=""),""===t||ds.test(t)||(this.num=NaN,this.unit=""),dd.includes(t)&&(this.num=e*dp[t],this.unit="px")}}function dy(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,[,n,i,a]=null!=(r=dc.exec(t))?r:[],o=dh.parse(null!=n?n:""),l=dh.parse(null!=a?a:""),c="*"===i?o.multiply(l):o.divide(l);if(c.isNaN())return"NaN";t=t.replace(dc,c.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var u,[,s,f,p]=null!=(u=du.exec(t))?u:[],d=dh.parse(null!=s?s:""),h=dh.parse(null!=p?p:""),y="+"===f?d.add(h):d.subtract(h);if(y.isNaN())return"NaN";t=t.replace(du,y.toString())}return t}var dv=/\(([^()]*)\)/;function dm(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t,r=e;null!=(t=dv.exec(r));){var[,n]=t;r=r.replace(dv,dy(n))}return r}(t),t=dy(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var dg=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],db=["dx","dy","angle","className","breakAll"];function dx(){return(dx=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function dO(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var dw=/[ \f\n\r\t\v\u2028\u2029]+/,dP=e=>{var{children:t,breakAll:r,style:n}=e;try{let e;var i=[];e=t,null==e||(i=r?t.toString().split(""):t.toString().split(dw));var a=i.map(e=>({word:e,width:dl(e,n).width})),o=r?0:dl("\xa0",n).width;return{wordsWithComputedWidth:a,spaceWidth:o}}catch(e){return null}},dj=e=>[{words:null==e?[]:e.toString().split(dw)}],dE="#808080",dS=(0,p.forwardRef)((e,t)=>{var r,{x:n=0,y:i=0,lineHeight:a="1em",capHeight:o="0.71em",scaleToFit:l=!1,textAnchor:c="start",verticalAnchor:u="end",fill:s=dE}=e,f=dO(e,dg),h=(0,p.useMemo)(()=>(e=>{var{width:t,scaleToFit:r,children:n,style:i,breakAll:a,maxLines:o}=e;if((t||r)&&!n4.isSsr){var l=dP({breakAll:a,children:n,style:i});if(!l)return dj(n);var{wordsWithComputedWidth:c,spaceWidth:u}=l;return((e,t,r,n,i)=>{var a,{maxLines:o,children:l,style:c,breakAll:u}=e,s=x(o),f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce((e,t)=>{var{word:a,width:o}=t,l=e[e.length-1];return l&&(null==n||i||l.width+o+r<Number(n))?(l.words.push(a),l.width+=o+r):e.push({words:[a],width:o}),e},[])},p=f(t),d=e=>e.reduce((e,t)=>e.width>t.width?e:t);if(!s||i||!(p.length>o||d(p).width>Number(n)))return p;for(var h=e=>{var t=f(dP({breakAll:u,style:c,children:l.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>o||d(t).width>Number(n),t]},y=0,v=l.length-1,m=0;y<=v&&m<=l.length-1;){var g=Math.floor((y+v)/2),[b,O]=h(g-1),[w]=h(g);if(b||w||(y=g+1),b&&w&&(v=g-1),!b&&w){a=O;break}m++}return a||p})({breakAll:a,children:n,maxLines:o,style:i},c,u,t,r)}return dj(n)})({breakAll:f.breakAll,children:f.children,maxLines:f.maxLines,scaleToFit:l,style:f.style,width:f.width}),[f.breakAll,f.children,f.maxLines,l,f.style,f.width]),{dx:y,dy:v,angle:m,className:g,breakAll:b}=f,w=dO(f,db);if(!O(n)||!O(i))return null;var P=n+(x(y)?y:0),j=i+(x(v)?v:0);switch(u){case"start":r=dm("calc(".concat(o,")"));break;case"middle":r=dm("calc(".concat((h.length-1)/2," * -").concat(a," + (").concat(o," / 2))"));break;default:r=dm("calc(".concat(h.length-1," * -").concat(a,")"))}var E=[];if(l){var S=h[0].width,{width:A}=f;E.push("scale(".concat(x(A)?A/S:1,")"))}return m&&E.push("rotate(".concat(m,", ").concat(P,", ").concat(j,")")),E.length&&(w.transform=E.join(" ")),p.createElement("text",dx({},U(w,!0),{ref:t,x:P,y:j,className:d("recharts-text",g),textAnchor:c,fill:s.includes("url")?dE:s}),h.map((e,t)=>{var n=e.words.join(b?"":" ");return p.createElement("tspan",{x:P,dy:0===t?r:a,key:"".concat(n,"-").concat(t)},n)}))});dS.displayName="Text";var dA=["offset"],dk=["labelRef"];function dM(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function dT(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dC(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dT(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dT(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function dD(){return(dD=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var dN=e=>null!=e&&"function"==typeof e;function d_(e){var t,{offset:r=5}=e,n=dC({offset:r},dM(e,dA)),{viewBox:i,position:a,value:o,children:l,content:c,className:u="",textBreakAll:s,labelRef:f}=n,h=nx(),y=i||h;if(!y||null==o&&null==l&&!(0,p.isValidElement)(c)&&"function"!=typeof c)return null;if((0,p.isValidElement)(c)){var{labelRef:v}=n,g=dM(n,dk);return(0,p.cloneElement)(c,g)}if("function"==typeof c){if(t=(0,p.createElement)(c,n),(0,p.isValidElement)(t))return t}else t=(e=>{var{value:t,formatter:r}=e,n=null==e.children?t:e.children;return"function"==typeof r?r(n):n})(n);var O="cx"in y&&x(y.cx),w=U(n,!0);if(O&&("insideStart"===a||"insideEnd"===a||"end"===a))return((e,t,r)=>{let n,i;var a,o,{position:l,viewBox:c,offset:u,className:s}=e,{cx:f,cy:h,innerRadius:y,outerRadius:v,startAngle:g,endAngle:b,clockWise:x}=c,O=(y+v)/2,w=(n=g,m((i=b)-n)*Math.min(Math.abs(i-n),360)),j=w>=0?1:-1;"insideStart"===l?(a=g+j*u,o=x):"insideEnd"===l?(a=b-j*u,o=!x):"end"===l&&(a=b+j*u,o=x),o=w<=0?o:!o;var E=rV(f,h,O,a),S=rV(f,h,O,a+(o?1:-1)*359),A="M".concat(E.x,",").concat(E.y,"\n    A").concat(O,",").concat(O,",0,1,").concat(+!o,",\n    ").concat(S.x,",").concat(S.y),k=null==e.id?P("recharts-radial-line-"):e.id;return p.createElement("text",dD({},r,{dominantBaseline:"central",className:d("recharts-radial-bar-label",s)}),p.createElement("defs",null,p.createElement("path",{id:k,d:A})),p.createElement("textPath",{xlinkHref:"#".concat(k)},t))})(n,t,w);var E=O?(e=>{var{viewBox:t,offset:r,position:n}=e,{cx:i,cy:a,innerRadius:o,outerRadius:l,startAngle:c,endAngle:u}=t,s=(c+u)/2;if("outside"===n){var{x:f,y:p}=rV(i,a,l+r,s);return{x:f,y:p,textAnchor:f>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var{x:d,y:h}=rV(i,a,(o+l)/2,s);return{x:d,y:h,textAnchor:"middle",verticalAnchor:"middle"}})(n):((e,t)=>{var{parentViewBox:r,offset:n,position:i}=e,{x:a,y:o,width:l,height:c}=t,u=c>=0?1:-1,s=u*n,f=u>0?"end":"start",p=u>0?"start":"end",d=l>=0?1:-1,h=d*n,y=d>0?"end":"start",v=d>0?"start":"end";if("top"===i)return dC(dC({},{x:a+l/2,y:o-u*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(o-r.y,0),width:l}:{});if("bottom"===i)return dC(dC({},{x:a+l/2,y:o+c+s,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(o+c),0),width:l}:{});if("left"===i){var m={x:a-h,y:o+c/2,textAnchor:y,verticalAnchor:"middle"};return dC(dC({},m),r?{width:Math.max(m.x-r.x,0),height:c}:{})}if("right"===i){var g={x:a+l+h,y:o+c/2,textAnchor:v,verticalAnchor:"middle"};return dC(dC({},g),r?{width:Math.max(r.x+r.width-g.x,0),height:c}:{})}var O=r?{width:l,height:c}:{};return"insideLeft"===i?dC({x:a+h,y:o+c/2,textAnchor:v,verticalAnchor:"middle"},O):"insideRight"===i?dC({x:a+l-h,y:o+c/2,textAnchor:y,verticalAnchor:"middle"},O):"insideTop"===i?dC({x:a+l/2,y:o+s,textAnchor:"middle",verticalAnchor:p},O):"insideBottom"===i?dC({x:a+l/2,y:o+c-s,textAnchor:"middle",verticalAnchor:f},O):"insideTopLeft"===i?dC({x:a+h,y:o+s,textAnchor:v,verticalAnchor:p},O):"insideTopRight"===i?dC({x:a+l-h,y:o+s,textAnchor:y,verticalAnchor:p},O):"insideBottomLeft"===i?dC({x:a+h,y:o+c-s,textAnchor:v,verticalAnchor:f},O):"insideBottomRight"===i?dC({x:a+l-h,y:o+c-s,textAnchor:y,verticalAnchor:f},O):i&&"object"==typeof i&&(x(i.x)||b(i.x))&&(x(i.y)||b(i.y))?dC({x:a+j(i.x,l),y:o+j(i.y,c),textAnchor:"end",verticalAnchor:"end"},O):dC({x:a+l/2,y:o+c/2,textAnchor:"middle",verticalAnchor:"middle"},O)})(n,y);return p.createElement(dS,dD({ref:f,className:d("recharts-label",u)},w,E,{breakAll:s}),t)}d_.displayName="Label";var dI=e=>{var{cx:t,cy:r,angle:n,startAngle:i,endAngle:a,r:o,radius:l,innerRadius:c,outerRadius:u,x:s,y:f,top:p,left:d,width:h,height:y,clockWise:v,labelViewBox:m}=e;if(m)return m;if(x(h)&&x(y)){if(x(s)&&x(f))return{x:s,y:f,width:h,height:y};if(x(p)&&x(d))return{x:p,y:d,width:h,height:y}}return x(s)&&x(f)?{x:s,y:f,width:0,height:0}:x(t)&&x(r)?{cx:t,cy:r,startAngle:i||n||0,endAngle:a||n||0,innerRadius:c||0,outerRadius:u||l||o||0,clockWise:v}:e.viewBox?e.viewBox:void 0};d_.parseViewBox=dI,d_.renderCallByParent=function(e,t){var r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&r&&!e.label)return null;var{children:n,labelRef:i}=e,a=dI(e),o=B(n,d_).map((e,r)=>(0,p.cloneElement)(e,{viewBox:t||a,key:"label-".concat(r)}));return r?[((e,t,r)=>{if(!e)return null;var n={viewBox:t,labelRef:r};return!0===e?p.createElement(d_,dD({key:"label-implicit"},n)):O(e)?p.createElement(d_,dD({key:"label-implicit",value:e},n)):(0,p.isValidElement)(e)?e.type===d_?(0,p.cloneElement)(e,dC({key:"label-implicit"},n)):p.createElement(d_,dD({key:"label-implicit",content:e},n)):dN(e)?p.createElement(d_,dD({key:"label-implicit",content:e},n)):e&&"object"==typeof e?p.createElement(d_,dD({},e,{key:"label-implicit"},n)):null})(e.label,t||a,i),...o]:o};var dL=r(58080),dR=r.n(dL),dK=["valueAccessor"],dz=["data","dataKey","clockWise","id","textBreakAll"];function dB(){return(dB=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function dF(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dU(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dF(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dF(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d$(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var dW=e=>Array.isArray(e.value)?dR()(e.value):e.value;function dV(e){var{valueAccessor:t=dW}=e,r=d$(e,dK),{data:n,dataKey:i,clockWise:a,id:o,textBreakAll:l}=r,c=d$(r,dz);return n&&n.length?p.createElement(q,{className:"recharts-label-list"},n.map((e,r)=>{var n=null==i?t(e,r):rG(e&&e.payload,i),u=null==o?{}:{id:"".concat(o,"-").concat(r)};return p.createElement(d_,dB({},U(e,!0),c,u,{parentViewBox:e.parentViewBox,value:n,textBreakAll:l,viewBox:d_.parseViewBox(null==a?e:dU(dU({},e),{},{clockWise:a})),key:"label-".concat(r),index:r}))})):null}dV.displayName="LabelList",dV.renderCallByParent=function(e,t){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&n&&!e.label)return null;var{children:i}=e,a=B(i,dV).map((e,r)=>(0,p.cloneElement)(e,{data:t,key:"labelList-".concat(r)}));return n?[(r=e.label,r?!0===r?p.createElement(dV,{key:"labelList-implicit",data:t}):p.isValidElement(r)||dN(r)?p.createElement(dV,{key:"labelList-implicit",data:t,content:r}):"object"==typeof r?p.createElement(dV,dB({data:t},r,{key:"labelList-implicit"})):null:null),...a]:a};var dX=["component"];function dH(e){var t,{component:r}=e,n=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,dX);return(0,p.isValidElement)(r)?t=(0,p.cloneElement)(r,n):"function"==typeof r?t=(0,p.createElement)(r,n):p6(!1,"Customized's props `component` must be React.element or Function, but got %s.",typeof r),p.createElement(q,{className:"recharts-customized-wrapper"},t)}dH.displayName="Customized";var dq=["points","className","baseLinePoints","connectNulls"];function dY(){return(dY=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var dG=e=>e&&e.x===+e.x&&e.y===+e.y,dZ=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[[]];return e.forEach(e=>{dG(e)?t[t.length-1].push(e):t[t.length-1].length>0&&t.push([])}),dG(e[0])&&t[t.length-1].push(e[0]),t[t.length-1].length<=0&&(t=t.slice(0,-1)),t},dJ=(e,t)=>{var r=dZ(e);t&&(r=[r.reduce((e,t)=>[...e,...t],[])]);var n=r.map(e=>e.reduce((e,t,r)=>"".concat(e).concat(0===r?"M":"L").concat(t.x,",").concat(t.y),"")).join("");return 1===r.length?"".concat(n,"Z"):n},dQ=e=>{var{points:t,className:r,baseLinePoints:n,connectNulls:i}=e,a=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,dq);if(!t||!t.length)return null;var o=d("recharts-polygon",r);if(n&&n.length){var l=a.stroke&&"none"!==a.stroke,c=((e,t,r)=>{var n=dJ(e,r);return"".concat("Z"===n.slice(-1)?n.slice(0,-1):n,"L").concat(dJ(t.reverse(),r).slice(1))})(t,n,i);return p.createElement("g",{className:o},p.createElement("path",dY({},U(a,!0),{fill:"Z"===c.slice(-1)?a.fill:"none",stroke:"none",d:c})),l?p.createElement("path",dY({},U(a,!0),{fill:"none",d:dJ(t,i)})):null,l?p.createElement("path",dY({},U(a,!0),{fill:"none",d:dJ(n,i)})):null)}var u=dJ(t,i);return p.createElement("path",dY({},U(a,!0),{fill:"Z"===u.slice(-1)?a.fill:"none",className:o,d:u}))};function d0(){return(d0=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var d1=e=>{var{cx:t,cy:r,r:n,className:i}=e,a=d("recharts-dot",i);return t===+t&&r===+r&&n===+n?p.createElement("circle",d0({},U(e,!1),_(e),{className:a,cx:t,cy:r,r:n})):null},d2=e=>e.graphicalItems.polarItems,d5=e3([uL,uR],uG),d3=e3([d2,uH,d5],uQ),d4=e3([d3],u5),d6=e3([d4,cG],u4),d8=e3([d6,uH,d3],u8),d7=e3([d6,uH,d3],(e,t,r)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var n;return{value:rG(e,null!=(n=t.dataKey)?n:r.dataKey),errorDomain:[]}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:rG(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]}))),d9=()=>void 0,he=e3([uH,sj,d9,d7,d9],sE),ht=e3([uH,nS,d6,d8,ud,uL,he],sk),hr=e3([ht,uH,sC],sN),hn=e3([uH,ht,hr,uL],sI),hi=(e,t,r)=>{switch(t){case"angleAxis":return uE(e,r);case"radiusAxis":return uS(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},ha=(e,t,r)=>{switch(t){case"angleAxis":return uD(e,r);case"radiusAxis":return u_(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},ho=e3([hi,sC,hn,ha],sD),hl=e3([nS,d8,uq,uL],s4),hc=e3([nS,hi,sC,ho,hr,ha,s3,hl,uL],s7),hu=e3([nS,hi,ho,ha,s3,hl,uL],fe),hs=e3([(e,t)=>hc(e,"angleAxis",t,!1)],e=>{if(e)return e.map(e=>e.coordinate)}),hf=e3([(e,t)=>hc(e,"radiusAxis",t,!1)],e=>{if(e)return e.map(e=>e.coordinate)}),hp=["gridType","radialLines","angleAxisId","radiusAxisId","cx","cy","innerRadius","outerRadius"];function hd(){return(hd=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function hh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hy(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hh(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hh(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var hv=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,polarAngles:a,radialLines:o}=e;if(!a||!a.length||!o)return null;var l=hy({stroke:"#ccc"},U(e,!1));return p.createElement("g",{className:"recharts-polar-grid-angle"},a.map(e=>{var a=rV(t,r,n,e),o=rV(t,r,i,e);return p.createElement("line",hd({},l,{key:"line-".concat(e),x1:a.x,y1:a.y,x2:o.x,y2:o.y}))}))},hm=e=>{var{cx:t,cy:r,radius:n,index:i}=e,a=hy(hy({stroke:"#ccc"},U(e,!1)),{},{fill:"none"});return p.createElement("circle",hd({},a,{className:d("recharts-polar-grid-concentric-circle",e.className),key:"circle-".concat(i),cx:t,cy:r,r:n}))},hg=e=>{var{radius:t,index:r}=e,n=hy(hy({stroke:"#ccc"},U(e,!1)),{},{fill:"none"});return p.createElement("path",hd({},n,{className:d("recharts-polar-grid-concentric-polygon",e.className),key:"path-".concat(r),d:((e,t,r,n)=>{var i="";return n.forEach((n,a)=>{var o=rV(t,r,e,n);a?i+="L ".concat(o.x,",").concat(o.y):i+="M ".concat(o.x,",").concat(o.y)}),i+="Z"})(t,e.cx,e.cy,e.polarAngles)}))},hb=e=>{var{polarRadius:t,gridType:r}=e;return t&&t.length?p.createElement("g",{className:"recharts-polar-grid-concentric"},t.map((t,n)=>"circle"===r?p.createElement(hm,hd({key:n},e,{radius:t,index:n})):p.createElement(hg,hd({key:n},e,{radius:t,index:n})))):null},hx=e=>{var t,r,n,i,a,o,l,c,{gridType:u="polygon",radialLines:s=!0,angleAxisId:f=0,radiusAxisId:d=0,cx:h,cy:y,innerRadius:v,outerRadius:m}=e,g=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,hp),b=eB(uI),x=hy({cx:null!=(t=null!=(r=null==b?void 0:b.cx)?r:h)?t:0,cy:null!=(n=null!=(i=null==b?void 0:b.cy)?i:y)?n:0,innerRadius:null!=(a=null!=(o=null==b?void 0:b.innerRadius)?o:v)?a:0,outerRadius:null!=(l=null!=(c=null==b?void 0:b.outerRadius)?c:m)?l:0},g),{polarAngles:O,polarRadius:w,cx:P,cy:j,innerRadius:E,outerRadius:S}=x,A=eB(e=>hs(e,f)),k=eB(e=>hf(e,d)),M=Array.isArray(O)?O:A,T=Array.isArray(w)?w:k;return S<=0||null==M||null==T?null:p.createElement("g",{className:"recharts-polar-grid"},p.createElement(hv,hd({cx:P,cy:j,innerRadius:E,outerRadius:S,gridType:u,radialLines:s},x,{polarAngles:M,polarRadius:T})),p.createElement(hb,hd({cx:P,cy:j,innerRadius:E,outerRadius:S,gridType:u,radialLines:s},x,{polarAngles:M,polarRadius:T})))};hx.displayName="PolarGrid";var hO=r(70027),hw=r.n(hO),hP=r(31901),hj=r.n(hP),hE=t8({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=t.payload},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=t.payload},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}}),{addRadiusAxis:hS,removeRadiusAxis:hA,addAngleAxis:hk,removeAngleAxis:hM}=hE.actions,hT=hE.reducer,hC=["cx","cy","angle","axisLine"],hD=["angle","tickFormatter","stroke","tick"];function hN(){return(hN=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function h_(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hI(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h_(Object(r),!0).forEach(function(t){hL(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h_(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function hL(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hR(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var hK="radiusAxis";function hz(e){var t=eL();return(0,p.useEffect)(()=>(t(hS(e)),()=>{t(hA(e))})),null}var hB=e=>{var{radiusAxisId:t}=e,r=eB(uI),n=eB(e=>ho(e,"radiusAxis",t)),i=eB(e=>hc(e,"radiusAxis",t,!1));if(null==r||!i||!i.length)return null;var a=hI(hI(hI({},e),{},{scale:n},r),{},{radius:r.outerRadius}),{tick:o,axisLine:l}=a;return p.createElement(q,{className:d("recharts-polar-radius-axis",hK,a.className)},l&&((e,t)=>{var{cx:r,cy:n,angle:i,axisLine:a}=e,o=hR(e,hC),l=t.reduce((e,t)=>[Math.min(e[0],t.coordinate),Math.max(e[1],t.coordinate)],[1/0,-1/0]),c=rV(r,n,l[0],i),u=rV(r,n,l[1],i),s=hI(hI(hI({},U(o,!1)),{},{fill:"none"},U(a,!1)),{},{x1:c.x,y1:c.y,x2:u.x,y2:u.y});return p.createElement("line",hN({className:"recharts-polar-radius-axis-line"},s))})(a,i),o&&((e,t)=>{var{angle:r,tickFormatter:n,stroke:i,tick:a}=e,o=hR(e,hD),l=(e=>{var t;switch(e){case"left":t="end";break;case"right":t="start";break;default:t="middle"}return t})(e.orientation),c=U(o,!1),u=U(a,!1),s=t.map((t,o)=>{var s=((e,t,r,n)=>{var{coordinate:i}=e;return rV(r,n,i,t)})(t,e.angle,e.cx,e.cy),f=hI(hI(hI(hI({textAnchor:l,transform:"rotate(".concat(90-r,", ").concat(s.x,", ").concat(s.y,")")},c),{},{stroke:"none",fill:i},u),{},{index:o},s),{},{payload:t});return p.createElement(q,hN({className:d("recharts-polar-radius-axis-tick",rH(a)),key:"tick-".concat(t.coordinate)},I(e,t,o)),((e,t,r)=>p.isValidElement(e)?p.cloneElement(e,t):"function"==typeof e?e(t):p.createElement(dS,hN({},t,{className:"recharts-polar-radius-axis-tick-value"}),r))(a,f,n?n(t.value,o):t.value))});return p.createElement(q,{className:"recharts-polar-radius-axis-ticks"},s)})(a,i),d_.renderCallByParent(a,((e,t,r,n)=>{var i=hw()(n,e=>e.coordinate||0);return{cx:t,cy:r,startAngle:e,endAngle:e,innerRadius:hj()(n,e=>e.coordinate||0).coordinate||0,outerRadius:i.coordinate||0}})(a.angle,a.cx,a.cy,i)))};class hF extends p.PureComponent{render(){return p.createElement(p.Fragment,null,p.createElement(hz,{domain:this.props.domain,id:this.props.radiusAxisId,scale:this.props.scale,type:this.props.type,dataKey:this.props.dataKey,unit:void 0,name:this.props.name,allowDuplicatedCategory:this.props.allowDuplicatedCategory,allowDataOverflow:this.props.allowDataOverflow,reversed:this.props.reversed,includeHidden:this.props.includeHidden,allowDecimals:this.props.allowDecimals,tickCount:this.props.tickCount,ticks:this.props.ticks,tick:this.props.tick}),p.createElement(hB,this.props))}}hL(hF,"displayName","PolarRadiusAxis"),hL(hF,"axisType",hK),hL(hF,"defaultProps",ub);var hU=["children"];function h$(){return(h$=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function hW(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hV(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hW(Object(r),!0).forEach(function(t){hX(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hW(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function hX(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var hH=Math.PI/180,hq="angleAxis";function hY(e){var t=eL(),r=(0,p.useMemo)(()=>{var{children:t}=e,r=e,n=hU;if(null==r)return{};var i,a,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(r,n);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(r);for(a=0;a<l.length;a++)i=l[a],-1===n.indexOf(i)&&({}).propertyIsEnumerable.call(r,i)&&(o[i]=r[i])}return o},[e]),n=eB(e=>uE(e,r.id)),i=r===n;return((0,p.useEffect)(()=>(t(hk(r)),()=>{t(hM(r))}),[t,r]),i)?e.children:null}var hG=e=>{var{cx:t,cy:r,radius:n,axisLineType:i,axisLine:a,ticks:o}=e;if(!a)return null;var l=hV(hV({},U(e,!1)),{},{fill:"none"},U(a,!1));if("circle"===i)return p.createElement(d1,h$({className:"recharts-polar-angle-axis-line"},l,{cx:t,cy:r,r:n}));var c=o.map(e=>rV(t,r,n,e.coordinate));return p.createElement(dQ,h$({className:"recharts-polar-angle-axis-line"},l,{points:c}))},hZ=e=>{var{tick:t,tickProps:r,value:n}=e;return t?p.isValidElement(t)?p.cloneElement(t,r):"function"==typeof t?t(r):p.createElement(dS,h$({},r,{className:"recharts-polar-angle-axis-tick-value"}),n):null},hJ=e=>{var{tick:t,tickLine:r,tickFormatter:n,stroke:i,ticks:a}=e,o=U(e,!1),l=U(t,!1),c=hV(hV({},o),{},{fill:"none"},U(r,!1)),u=a.map((a,u)=>{var s=((e,t)=>{var{cx:r,cy:n,radius:i,orientation:a,tickSize:o}=t,l=rV(r,n,i,e.coordinate),c=rV(r,n,i+("inner"===a?-1:1)*(o||8),e.coordinate);return{x1:l.x,y1:l.y,x2:c.x,y2:c.y}})(a,e),f=hV(hV(hV({textAnchor:((e,t)=>{var r=Math.cos(-e.coordinate*hH);return r>1e-5?"outer"===t?"start":"end":r<-1e-5?"outer"===t?"end":"start":"middle"})(a,e.orientation)},o),{},{stroke:"none",fill:i},l),{},{index:u,payload:a,x:s.x2,y:s.y2});return p.createElement(q,h$({className:d("recharts-polar-angle-axis-tick",rH(t)),key:"tick-".concat(a.coordinate)},I(e,a,u)),r&&p.createElement("line",h$({className:"recharts-polar-angle-axis-tick-line"},c,s)),p.createElement(hZ,{tick:t,tickProps:f,value:n?n(a.value,u):a.value}))});return p.createElement(q,{className:"recharts-polar-angle-axis-ticks"},u)},hQ=e=>{var{angleAxisId:t}=e,r=eB(uI),n=eB(e=>ho(e,"angleAxis",t)),i=nv(),a=eB(e=>hc(e,"angleAxis",t,i));if(null==r||!a||!a.length)return null;var o=hV(hV(hV({},e),{},{scale:n},r),{},{radius:r.outerRadius});return p.createElement(q,{className:d("recharts-polar-angle-axis",hq,o.className)},p.createElement(hG,h$({},o,{ticks:a})),p.createElement(hJ,h$({},o,{ticks:a})))};class h0 extends p.PureComponent{render(){return this.props.radius<=0?null:p.createElement(hY,{id:this.props.angleAxisId,scale:this.props.scale,type:this.props.type,dataKey:this.props.dataKey,unit:void 0,name:this.props.name,allowDuplicatedCategory:!1,allowDataOverflow:!1,reversed:this.props.reversed,includeHidden:!1,allowDecimals:this.props.allowDecimals,tickCount:this.props.tickCount,ticks:this.props.ticks,tick:this.props.tick,domain:this.props.domain},p.createElement(hQ,this.props))}}function h1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function h2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h1(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h1(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}hX(h0,"displayName","PolarAngleAxis"),hX(h0,"axisType",hq),hX(h0,"defaultProps",ug);var h5=(e,t)=>t,h3=[],h4=(e,t,r)=>(null==r?void 0:r.length)===0?h3:r,h6=e3([cG,h5,h4],(e,t,r)=>{var n,{chartData:i}=e;if((n=(null==t?void 0:t.data)!=null&&t.data.length>0?t.data:i)&&n.length||null==r||(n=r.map(e=>h2(h2({},t.presentationProps),e.props))),null!=n)return n}),h8=e3([h6,h5,h4],(e,t,r)=>{if(null!=e)return e.map((e,n)=>{var i,a,o=rG(e,t.nameKey,t.name);return a=null!=r&&null!=(i=r[n])&&null!=(i=i.props)&&i.fill?r[n].props.fill:"object"==typeof e&&null!=e&&"fill"in e?e.fill:t.fill,{value:ne(o,t.dataKey),color:a,payload:e,type:t.legendType}})}),h7=e3([d2,h5],(e,t)=>{if(e.some(e=>"pie"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),h9=e3([h6,h7,h4,np],(e,t,r,n)=>{if(null!=t&&null!=e)return function(e){var t,r,n,{pieSettings:i,displayedData:a,cells:o,offset:l}=e,{cornerRadius:c,startAngle:u,endAngle:s,dataKey:f,nameKey:p,tooltipType:d}=i,h=Math.abs(i.minAngle),y=m(s-u)*Math.min(Math.abs(s-u),360),v=Math.abs(y),g=a.length<=1?0:null!=(t=i.paddingAngle)?t:0,b=a.filter(e=>0!==rG(e,f,0)).length,O=v-b*h-(v>=360?b:b-1)*g,w=a.reduce((e,t)=>{var r=rG(t,f,0);return e+(x(r)?r:0)},0);return w>0&&(r=a.map((e,t)=>{var r,a=rG(e,f,0),s=rG(e,p,t),v=((e,t,r)=>{let n,i,a;var{top:o,left:l,width:c,height:u}=t,s=rX(c,u),f=l+j(e.cx,c,c/2),p=o+j(e.cy,u,u/2),d=j(e.innerRadius,s,0);return{cx:f,cy:p,innerRadius:d,outerRadius:(n=r,i=e.outerRadius,a=s,"function"==typeof i?i(n):j(i,a,.8*a)),maxRadius:e.maxRadius||Math.sqrt(c*c+u*u)/2}})(i,l,e),b=(x(a)?a:0)/w,P=yL(yL({},e),o&&o[t]&&o[t].props),E=(r=t?n.endAngle+m(y)*g*(0!==a):u)+m(y)*((0!==a?h:0)+b*O),S=(r+E)/2,A=(v.innerRadius+v.outerRadius)/2,k=[{name:s,value:a,payload:P,dataKey:f,type:d}],M=rV(v.cx,v.cy,A,S);return n=yL(yL(yL(yL({},i.presentationProps),{},{percent:b,cornerRadius:c,name:s,tooltipPayload:k,midAngle:S,middleRadius:A,tooltipPosition:M},P),v),{},{value:rG(e,f),startAngle:r,endAngle:E,payload:P,paddingAngle:m(y)*g})})),r}({offset:n,pieSettings:t,displayedData:e,cells:r})}),ye=t8({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(e){e.countOfBars+=1},removeBar(e){e.countOfBars-=1},addCartesianGraphicalItem(e,t){e.cartesianItems.push(t.payload)},replaceCartesianGraphicalItem(e,t){var{prev:r,next:n}=t.payload,i=tY(e).cartesianItems.indexOf(r);i>-1&&(e.cartesianItems[i]=n)},removeCartesianGraphicalItem(e,t){var r=tY(e).cartesianItems.indexOf(t.payload);r>-1&&e.cartesianItems.splice(r,1)},addPolarGraphicalItem(e,t){e.polarItems.push(t.payload)},removePolarGraphicalItem(e,t){var r=tY(e).polarItems.indexOf(t.payload);r>-1&&e.polarItems.splice(r,1)}}}),{addBar:yt,removeBar:yr,addCartesianGraphicalItem:yn,replaceCartesianGraphicalItem:yi,removeCartesianGraphicalItem:ya,addPolarGraphicalItem:yo,removePolarGraphicalItem:yl}=ye.actions,yc=ye.reducer;function yu(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ys(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yu(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yu(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function yf(e){var t=eL(),r=(0,p.useRef)(null);return(0,p.useEffect)(()=>{var n=ys(ys({},e),{},{stackId:r2(e.stackId)});null===r.current?t(yn(n)):r.current!==n&&t(yi({prev:r.current,next:n})),r.current=n},[t,e]),(0,p.useEffect)(()=>()=>{r.current&&(t(ya(r.current)),r.current=null)},[t]),null}function yp(e){var t=eL();return(0,p.useEffect)(()=>(t(yo(e)),()=>{t(yl(e))}),[t,e]),null}var yd=r(80931),yh=r.n(yd);function yy(){return(yy=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var yv=(e,t,r,n,i)=>{var a=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-a/2,",").concat(t+i)+"L ".concat(e+r-a/2-n,",").concat(t+i)+"L ".concat(e,",").concat(t," Z")},ym={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},yg=e=>{var t=iL(e,ym),r=(0,p.useRef)(),[n,i]=(0,p.useState)(-1);(0,p.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&i(e)}catch(e){}},[]);var{x:a,y:o,upperWidth:l,lowerWidth:c,height:u,className:s}=t,{animationEasing:f,animationDuration:h,animationBegin:y,isUpdateAnimationActive:v}=t;if(a!==+a||o!==+o||l!==+l||c!==+c||u!==+u||0===l&&0===c||0===u)return null;var m=d("recharts-trapezoid",s);return v?p.createElement(i6,{canBegin:n>0,from:{upperWidth:0,lowerWidth:0,height:u,x:a,y:o},to:{upperWidth:l,lowerWidth:c,height:u,x:a,y:o},duration:h,animationEasing:f,isActive:v},e=>{var{upperWidth:i,lowerWidth:a,height:o,x:l,y:c}=e;return p.createElement(i6,{canBegin:n>0,from:"0px ".concat(-1===n?1:n,"px"),to:"".concat(n,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:h,easing:f},p.createElement("path",yy({},U(t,!0),{className:m,d:yv(l,c,i,a,o),ref:r})))}):p.createElement("g",null,p.createElement("path",yy({},U(t,!0),{className:m,d:yv(a,o,l,c,u)})))},yb=["option","shapeType","propTransformer","activeClassName","isActive"];function yx(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yO(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yx(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yx(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function yw(e,t){return yO(yO({},t),e)}function yP(e){var{shapeType:t,elementProps:r}=e;switch(t){case"rectangle":return p.createElement(ae,r);case"trapezoid":return p.createElement(yg,r);case"sector":return p.createElement(ao,r);case"symbols":if("symbols"===t)return p.createElement(eE,r);break;default:return null}}function yj(e){return(0,p.isValidElement)(e)?e.props:e}function yE(e){var t,{option:r,shapeType:n,propTransformer:i=yw,activeClassName:a="recharts-active-shape",isActive:o}=e,l=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,yb);if((0,p.isValidElement)(r))t=(0,p.cloneElement)(r,yO(yO({},l),yj(r)));else if("function"==typeof r)t=r(l);else if(yh()(r)&&"boolean"!=typeof r){var c=i(r,l);t=p.createElement(yP,{shapeType:n,elementProps:c})}else t=p.createElement(yP,{shapeType:n,elementProps:l});return o?p.createElement(q,{className:a},t):t}var yS=(e,t)=>{var r=eL();return(n,i)=>a=>{null==e||e(n,i,a),r(fv({activeIndex:String(i),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},yA=e=>{var t=eL();return(r,n)=>i=>{null==e||e(r,n,i),t(fm())}},yk=(e,t)=>{var r=eL();return(n,i)=>a=>{null==e||e(n,i,a),r(fb({activeIndex:String(i),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}};function yM(e){var{fn:t,args:r}=e,n=eL(),i=nv();return(0,p.useEffect)(()=>{if(!i){var e=t(r);return n(fd(e)),()=>{n(fh(e))}}},[t,r,n,i]),null}var yT=()=>{};function yC(e){var{legendPayload:t}=e,r=eL(),n=nv();return(0,p.useEffect)(()=>n?yT:(r(nN(t)),()=>{r(n_(t))}),[r,n,t]),null}function yD(e){var{legendPayload:t}=e,r=eL(),n=eB(nS);return(0,p.useEffect)(()=>"centric"!==n&&"radial"!==n?yT:(r(nN(t)),()=>{r(n_(t))}),[r,n,t]),null}function yN(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",r=(0,p.useRef)(P(t)),n=(0,p.useRef)(e);return n.current!==e&&(r.current=P(t),n.current=e),r.current}var y_=["onMouseEnter","onClick","onMouseLeave"];function yI(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yL(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yI(Object(r),!0).forEach(function(t){yR(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yI(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function yR(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yK(){return(yK=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function yz(e){var t=(0,p.useMemo)(()=>U(e,!1),[e]),r=(0,p.useMemo)(()=>B(e.children,de),[e.children]),n=(0,p.useMemo)(()=>({name:e.name,nameKey:e.nameKey,tooltipType:e.tooltipType,data:e.data,dataKey:e.dataKey,cx:e.cx,cy:e.cy,startAngle:e.startAngle,endAngle:e.endAngle,minAngle:e.minAngle,paddingAngle:e.paddingAngle,innerRadius:e.innerRadius,outerRadius:e.outerRadius,cornerRadius:e.cornerRadius,legendType:e.legendType,fill:e.fill,presentationProps:t}),[e.cornerRadius,e.cx,e.cy,e.data,e.dataKey,e.endAngle,e.innerRadius,e.minAngle,e.name,e.nameKey,e.outerRadius,e.paddingAngle,e.startAngle,e.tooltipType,e.legendType,e.fill,t]),i=eB(e=>h8(e,n,r));return p.createElement(yD,{legendPayload:i})}function yB(e){var{dataKey:t,nameKey:r,sectors:n,stroke:i,strokeWidth:a,fill:o,name:l,hide:c,tooltipType:u}=e;return{dataDefinedOnItem:null==n?void 0:n.map(e=>e.tooltipPayload),positions:null==n?void 0:n.map(e=>e.tooltipPosition),settings:{stroke:i,strokeWidth:a,fill:o,dataKey:t,nameKey:r,name:ne(l,t),hide:c,type:u,color:o,unit:""}}}function yF(e){var{sectors:t,props:r,showLabels:n}=e,{label:i,labelLine:a,dataKey:o}=r;if(!n||!i||!t)return null;var l=U(r,!1),c=U(i,!1),u=U(a,!1),s="object"==typeof i&&"offsetRadius"in i&&i.offsetRadius||20,f=t.map((e,t)=>{var r,n,f=(e.startAngle+e.endAngle)/2,h=rV(e.cx,e.cy,e.outerRadius+s,f),y=yL(yL(yL(yL({},l),e),{},{stroke:"none"},c),{},{index:t,textAnchor:(r=h.x)>(n=e.cx)?"start":r<n?"end":"middle"},h),v=yL(yL(yL(yL({},l),e),{},{fill:"none",stroke:e.fill},u),{},{index:t,points:[rV(e.cx,e.cy,e.outerRadius,f),h],key:"line"});return p.createElement(q,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},a&&((e,t)=>{if(p.isValidElement(e))return p.cloneElement(e,t);if("function"==typeof e)return e(t);var r=d("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return p.createElement(iT,yK({},t,{type:"linear",className:r}))})(a,v),((e,t,r)=>{if(p.isValidElement(e))return p.cloneElement(e,t);var n=r;if("function"==typeof e&&(n=e(t),p.isValidElement(n)))return n;var i=d("recharts-pie-label-text","boolean"!=typeof e&&"function"!=typeof e?e.className:"");return p.createElement(dS,yK({},t,{alignmentBaseline:"middle",className:i}),n)})(i,y,rG(e,o)))});return p.createElement(q,{className:"recharts-pie-labels"},f)}function yU(e){var{sectors:t,activeShape:r,inactiveShape:n,allOtherPieProps:i,showLabels:a}=e,o=eB(pc),{onMouseEnter:l,onClick:c,onMouseLeave:u}=i,s=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,y_),f=yS(l,i.dataKey),d=yA(u),h=yk(c,i.dataKey);return null==t?null:p.createElement(p.Fragment,null,t.map((e,a)=>{if((null==e?void 0:e.startAngle)===0&&(null==e?void 0:e.endAngle)===0&&1!==t.length)return null;var l=r&&String(a)===o,c=l?r:o?n:null,u=yL(yL({},e),{},{stroke:e.stroke,tabIndex:-1,[nc]:a,[nu]:i.dataKey});return p.createElement(q,yK({tabIndex:-1,className:"recharts-pie-sector"},I(s,e,a),{onMouseEnter:f(e,a),onMouseLeave:d(e,a),onClick:h(e,a),key:"sector-".concat(null==e?void 0:e.startAngle,"-").concat(null==e?void 0:e.endAngle,"-").concat(e.midAngle,"-").concat(a)}),p.createElement(yE,yK({option:c,isActive:l,shapeType:"sector"},u)))}),p.createElement(yF,{sectors:t,props:i,showLabels:a}))}function y$(e){var{props:t,previousSectorsRef:r}=e,{sectors:n,isAnimationActive:i,animationBegin:a,animationDuration:o,animationEasing:l,activeShape:c,inactiveShape:u,onAnimationStart:s,onAnimationEnd:f}=t,d=yN(t,"recharts-pie-"),h=r.current,[v,m]=(0,p.useState)(!0),g=(0,p.useCallback)(()=>{"function"==typeof f&&f(),m(!1)},[f]),b=(0,p.useCallback)(()=>{"function"==typeof s&&s(),m(!0)},[s]);return p.createElement(i6,{begin:a,duration:o,isActive:i,easing:l,from:{t:0},to:{t:1},onAnimationStart:b,onAnimationEnd:g,key:d},e=>{var{t:i}=e,a=[],o=(n&&n[0]).startAngle;return n.forEach((e,t)=>{var r=h&&h[t],n=t>0?y()(e,"paddingAngle",0):0;if(r){var l=S(r.endAngle-r.startAngle,e.endAngle-e.startAngle),c=yL(yL({},e),{},{startAngle:o+n,endAngle:o+l(i)+n});a.push(c),o=c.endAngle}else{var{endAngle:u,startAngle:s}=e,f=S(0,u-s)(i),p=yL(yL({},e),{},{startAngle:o+n,endAngle:o+f+n});a.push(p),o=p.endAngle}}),r.current=a,p.createElement(q,null,p.createElement(yU,{sectors:a,activeShape:c,inactiveShape:u,allOtherPieProps:t,showLabels:!v}))})}function yW(e){var{sectors:t,isAnimationActive:r,activeShape:n,inactiveShape:i}=e,a=(0,p.useRef)(null),o=a.current;return r&&t&&t.length&&(!o||o!==t)?p.createElement(y$,{props:e,previousSectorsRef:a}):p.createElement(yU,{sectors:t,activeShape:n,inactiveShape:i,allOtherPieProps:e,showLabels:!0})}function yV(e){var{hide:t,className:r,rootTabIndex:n}=e,i=d("recharts-pie",r);return t?null:p.createElement(q,{tabIndex:n,className:i},p.createElement(yW,e))}var yX={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!n4.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function yH(e){var t=iL(e,yX),r=(0,p.useMemo)(()=>B(e.children,de),[e.children]),n=U(t,!1),i=(0,p.useMemo)(()=>({name:t.name,nameKey:t.nameKey,tooltipType:t.tooltipType,data:t.data,dataKey:t.dataKey,cx:t.cx,cy:t.cy,startAngle:t.startAngle,endAngle:t.endAngle,minAngle:t.minAngle,paddingAngle:t.paddingAngle,innerRadius:t.innerRadius,outerRadius:t.outerRadius,cornerRadius:t.cornerRadius,legendType:t.legendType,fill:t.fill,presentationProps:n}),[t.cornerRadius,t.cx,t.cy,t.data,t.dataKey,t.endAngle,t.innerRadius,t.minAngle,t.name,t.nameKey,t.outerRadius,t.paddingAngle,t.startAngle,t.tooltipType,t.legendType,t.fill,n]),a=eB(e=>h9(e,i,r));return p.createElement(p.Fragment,null,p.createElement(yM,{fn:yB,args:yL(yL({},t),{},{sectors:a})}),p.createElement(yV,yK({},t,{sectors:a})))}class yq extends p.PureComponent{render(){return p.createElement(p.Fragment,null,p.createElement(yp,{data:this.props.data,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:0,radiusAxisId:0,stackId:void 0,barSize:void 0,type:"pie"}),p.createElement(yz,this.props),p.createElement(yH,this.props),this.props.children)}constructor(){super(...arguments),yR(this,"id",P("recharts-pie-"))}}yR(yq,"displayName","Pie"),yR(yq,"defaultProps",yX);var yY=e3([np],e=>{if(e)return{top:e.top,bottom:e.bottom,left:e.left,right:e.right}}),yG=e3([yY,nt,nr],(e,t,r)=>{if(e&&null!=t&&null!=r)return{x:e.left,y:e.top,width:Math.max(0,t-e.left-e.right),height:Math.max(0,r-e.top-e.bottom)}}),yZ=()=>eB(pu),yJ=()=>eB(yY),yQ=()=>eB(yG),y0=()=>eB(pv);function y1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y1(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y1(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function y5(e){var{points:t,mainColor:r,activeDot:n,itemDataKey:i}=e,a=eB(pc),o=y0();if(null==t||null==o)return null;var l=t.find(e=>o.includes(e.payload));return null==l?null:(e=>{var t,{point:r,childIndex:n,mainColor:i,activeDot:a,dataKey:o}=e;if(!1===a||null==r.x||null==r.y)return null;var l=y2(y2({index:n,dataKey:o,cx:r.x,cy:r.y,r:4,fill:null!=i?i:"none",strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},U(a,!1)),_(a));return t=(0,p.isValidElement)(a)?(0,p.cloneElement)(a,l):"function"==typeof a?a(l):p.createElement(d1,l),p.createElement(q,{className:"recharts-active-dot"},t)})({point:l,childIndex:Number(a),mainColor:r,dataKey:i,activeDot:n})}var y3=e=>p.createElement(yp,e);function y4(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y6(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y4(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y4(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y8=(e,t)=>ho(e,"radiusAxis",t),y7=e3([y8],e=>{if(null!=e)return{scale:e}}),y9=e3([uS,y8],(e,t)=>{if(null!=e&&null!=t)return y6(y6({},e),{},{scale:t})}),ve=(e,t,r)=>uE(e,r),vt=(e,t,r)=>ho(e,"angleAxis",r),vr=e3([ve,vt],(e,t)=>{if(null!=e&&null!=t)return y6(y6({},e),{},{scale:t})}),vn=e3([ve,vt,uI],(e,t,r)=>{if(null!=r&&null!=t)return{scale:t,type:e.type,dataKey:e.dataKey,cx:r.cx,cy:r.cy}}),vi=e3([nS,y9,(e,t,r,n)=>hc(e,"radiusAxis",t,n),vr,(e,t,r,n)=>hc(e,"angleAxis",r,n)],(e,t,r,n,i)=>rZ(e,"radiusAxis")?r7(t,r,!1):r7(n,i,!1)),va=e3([d2,(e,t,r,n,i)=>i],(e,t)=>{if(e.some(e=>"radar"===e.type&&t===e.dataKey))return t}),vo=e3([y7,vn,cG,va,vi],(e,t,r,n,i)=>{var{chartData:a,dataStartIndex:o,dataEndIndex:l}=r;if(null!=e&&null!=t&&null!=a&&null!=i&&null!=n)return function(e){var{radiusAxis:t,angleAxis:r,displayedData:n,dataKey:i,bandSize:a}=e,{cx:o,cy:l}=r,c=!1,u=[],s="number"!==r.type&&null!=a?a:0;n.forEach((e,n)=>{var a=rG(e,r.dataKey,n),f=rG(e,i),p=r.scale(a)+s,d=Array.isArray(f)?dR()(f):f,h=null==d?void 0:t.scale(d);Array.isArray(f)&&f.length>=2&&(c=!0),u.push(vc(vc({},rV(o,l,h,p)),{},{name:a,value:f,cx:o,cy:l,radius:h,angle:p,payload:e}))});var f=[];return c&&u.forEach(e=>{if(Array.isArray(e.value)){var r=e.value[0],n=null==r?void 0:t.scale(r);f.push(vc(vc({},e),{},{radius:n},rV(o,l,n,e.angle)))}else f.push(e)}),{points:u,isRange:c,baseLinePoints:f}}({radiusAxis:e,angleAxis:t,displayedData:a.slice(o,l+1),dataKey:n,bandSize:i})});function vl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function vc(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vl(Object(r),!0).forEach(function(t){vu(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vl(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function vu(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function vs(){return(vs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function vf(e,t){return e&&"none"!==e?e:t}function vp(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:i,name:a,hide:o,tooltipType:l}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:i,nameKey:void 0,dataKey:t,name:ne(a,t),hide:o,type:l,color:vf(r,i),unit:""}}}function vd(e){var{points:t,props:r}=e,{dot:n,dataKey:i}=r;if(!n)return null;var a=U(r,!1),o=U(n,!0),l=t.map((e,t)=>{var r=vc(vc(vc({key:"dot-".concat(t),r:3},a),o),{},{dataKey:i,cx:e.x,cy:e.y,index:t,payload:e});return p.isValidElement(n)?p.cloneElement(n,r):"function"==typeof n?n(r):p.createElement(d1,vs({},r,{className:d("recharts-radar-dot","boolean"!=typeof n?n.className:"")}))});return p.createElement(q,{className:"recharts-radar-dots"},l)}function vh(e){var t,{points:r,props:n,showLabels:i}=e;if(null==r)return null;var{shape:a,isRange:o,baseLinePoints:l,connectNulls:c}=n;return t=p.isValidElement(a)?p.cloneElement(a,vc(vc({},n),{},{points:r})):"function"==typeof a?a(vc(vc({},n),{},{points:r})):p.createElement(dQ,vs({},U(n,!0),{onMouseEnter:e=>{var{onMouseEnter:t}=n;t&&t(n,e)},onMouseLeave:e=>{var{onMouseLeave:t}=n;t&&t(n,e)},points:r,baseLinePoints:o?l:null,connectNulls:c})),p.createElement(q,{className:"recharts-radar-polygon"},t,p.createElement(vd,{props:n,points:r}),i&&dV.renderCallByParent(n,r))}function vy(e){var{props:t,previousPointsRef:r}=e,{points:n,isAnimationActive:i,animationBegin:a,animationDuration:o,animationEasing:l,onAnimationEnd:c,onAnimationStart:u}=t,s=r.current,f=yN(t,"recharts-radar-"),[d,h]=(0,p.useState)(!0),y=(0,p.useCallback)(()=>{"function"==typeof c&&c(),h(!1)},[c]),v=(0,p.useCallback)(()=>{"function"==typeof u&&u(),h(!0)},[u]);return p.createElement(i6,{begin:a,duration:o,isActive:i,easing:l,from:{t:0},to:{t:1},key:"radar-".concat(f),onAnimationEnd:y,onAnimationStart:v},e=>{var{t:i}=e,a=s&&s.length/n.length,o=1===i?n:n.map((e,t)=>{var r=s&&s[Math.floor(t*a)];if(r){var n=S(r.x,e.x),o=S(r.y,e.y);return vc(vc({},e),{},{x:n(i),y:o(i)})}var l=S(e.cx,e.x),c=S(e.cy,e.y);return vc(vc({},e),{},{x:l(i),y:c(i)})});return i>0&&(r.current=o),p.createElement(vh,{points:o,props:t,showLabels:!d})})}function vv(e){var{points:t,isAnimationActive:r,isRange:n}=e,i=(0,p.useRef)(void 0),a=i.current;return r&&t&&t.length&&!n&&(!a||a!==t)?p.createElement(vy,{props:e,previousPointsRef:i}):p.createElement(vh,{points:t,props:e,showLabels:!0})}var vm={angleAxisId:0,radiusAxisId:0,hide:!1,activeDot:!0,dot:!1,legendType:"rect",isAnimationActive:!n4.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"};class vg extends p.PureComponent{render(){var{hide:e,className:t,points:r}=this.props;if(e)return null;var n=d("recharts-radar",t);return p.createElement(p.Fragment,null,p.createElement(q,{className:n},p.createElement(vv,this.props)),p.createElement(y5,{points:r,mainColor:vf(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}))}}function vb(e){var t=nv(),r=eB(r=>vo(r,e.radiusAxisId,e.angleAxisId,t,e.dataKey));return p.createElement(vg,vs({},e,{points:null==r?void 0:r.points,baseLinePoints:null==r?void 0:r.baseLinePoints,isRange:null==r?void 0:r.isRange}))}class vx extends p.PureComponent{render(){return p.createElement(p.Fragment,null,p.createElement(y3,{data:void 0,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:this.props.angleAxisId,radiusAxisId:this.props.radiusAxisId,stackId:void 0,barSize:void 0,type:"radar"}),p.createElement(yD,{legendPayload:(e=>{var{dataKey:t,name:r,stroke:n,fill:i,legendType:a,hide:o}=e;return[{inactive:o,dataKey:t,type:a,color:vf(n,i),value:ne(r,t),payload:e}]})(this.props)}),p.createElement(yM,{fn:vp,args:this.props}),p.createElement(vb,this.props))}}function vO(){return(vO=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function vw(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function vP(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vw(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vw(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function vj(e){return"string"==typeof e?parseInt(e,10):e}function vE(e,t){var r=Number("".concat(t.cx||e.cx)),n=Number("".concat(t.cy||e.cy));return vP(vP(vP({},t),e),{},{cx:r,cy:n})}function vS(e){return p.createElement(yE,vO({shapeType:"sector",propTransformer:vE},e))}vu(vx,"displayName","Radar"),vu(vx,"defaultProps",vm);var vA=()=>{var e=eL();return(0,p.useEffect)(()=>(e(yt()),()=>{e(yr())})),null},vk=["children"],vM=()=>{},vT=(0,p.createContext)({addErrorBar:vM,removeErrorBar:vM}),vC=(0,p.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function vD(e){var{children:t}=e,r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,vk);return p.createElement(vC.Provider,{value:r},t)}var vN=e=>{var{children:t,xAxisId:r,yAxisId:n,zAxisId:i,dataKey:a,data:o,stackId:l,hide:c,type:u,barSize:s}=e,[f,d]=p.useState([]),h=(0,p.useCallback)(e=>{d(t=>[...t,e])},[d]),y=(0,p.useCallback)(e=>{d(t=>t.filter(t=>t!==e))},[d]),v=nv();return p.createElement(vT.Provider,{value:{addErrorBar:h,removeErrorBar:y}},p.createElement(yf,{type:u,data:o,xAxisId:r,yAxisId:n,zAxisId:i,dataKey:a,errorBars:f,stackId:l,hide:c,barSize:s,isPanorama:v}),t)};function v_(e){var{addErrorBar:t,removeErrorBar:r}=(0,p.useContext)(vT);return(0,p.useEffect)(()=>(t(e),()=>{r(e)}),[t,r,e]),null}var vI=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function vL(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function vR(){return(vR=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function vK(e){var t,r,{direction:n,width:i,dataKey:a,isAnimationActive:o,animationBegin:l,animationDuration:c,animationEasing:u}=e,s=U(function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,vI),!1),{data:f,dataPointFormatter:d,xAxisId:h,yAxisId:y,errorBarOffset:v}=(0,p.useContext)(vC),m=(t=nv(),eB(e=>fr(e,"xAxis",h,t))),g=(r=nv(),eB(e=>fr(e,"yAxis",y,r)));if((null==m?void 0:m.scale)==null||(null==g?void 0:g.scale)==null||null==f||"x"===n&&"number"!==m.type)return null;var b=f.map(e=>{var t,r,{x:f,y:h,value:y,errorVal:b}=d(e,a,n);if(!b)return null;var x=[];if(Array.isArray(b)?[t,r]=b:t=r=b,"x"===n){var{scale:O}=m,w=h+v,P=w+i,j=w-i,E=O(y-t),S=O(y+r);x.push({x1:S,y1:P,x2:S,y2:j}),x.push({x1:E,y1:w,x2:S,y2:w}),x.push({x1:E,y1:P,x2:E,y2:j})}else if("y"===n){var{scale:A}=g,k=f+v,M=k-i,T=k+i,C=A(y-t),D=A(y+r);x.push({x1:M,y1:D,x2:T,y2:D}),x.push({x1:k,y1:C,x2:k,y2:D}),x.push({x1:M,y1:C,x2:T,y2:C})}var N="".concat(f+v,"px ").concat(h+v,"px");return p.createElement(q,vR({className:"recharts-errorBar",key:"bar-".concat(x.map(e=>"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)))},s),x.map(e=>{var t=o?{transformOrigin:"".concat(e.x1-5,"px")}:void 0;return p.createElement(i6,{from:{transform:"scaleY(0)",transformOrigin:N},to:{transform:"scaleY(1)",transformOrigin:N},begin:l,easing:u,isActive:o,duration:c,key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2),style:{transformOrigin:N}},p.createElement("line",vR({},e,{style:t})))}))});return p.createElement(q,{className:"recharts-errorBars"},b)}var vz=(0,p.createContext)(void 0);function vB(e){var{direction:t,children:r}=e;return p.createElement(vz.Provider,{value:t},r)}var vF={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function vU(e){var t,r,n=(t=e.direction,r=(0,p.useContext)(vz),null!=t?t:null!=r?r:"x"),{width:i,isAnimationActive:a,animationBegin:o,animationDuration:l,animationEasing:c}=iL(e,vF);return p.createElement(p.Fragment,null,p.createElement(v_,{dataKey:e.dataKey,direction:n}),p.createElement(vK,vR({},e,{direction:n,width:i,isAnimationActive:a,animationBegin:o,animationDuration:l,animationEasing:c})))}class v$ extends p.Component{render(){return p.createElement(vU,this.props)}}vL(v$,"defaultProps",vF),vL(v$,"displayName","ErrorBar");var vW=["x","y"];function vV(){return(vV=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function vX(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function vH(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vX(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vX(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function vq(e,t){var{x:r,y:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,vW),a=parseInt("".concat(r),10),o=parseInt("".concat(n),10),l=parseInt("".concat(t.height||i.height),10),c=parseInt("".concat(t.width||i.width),10);return vH(vH(vH(vH(vH({},t),i),a?{x:a}:{}),o?{y:o}:{}),{},{height:l,width:c,name:t.name,radius:t.radius})}function vY(e){return p.createElement(yE,vV({shapeType:"rectangle",propTransformer:vq,activeClassName:"recharts-active-bar"},e))}var vG=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(r,n)=>{if(x(e))return e;var i=x(r)||null==r;return i?e(r,n):(i||function(e,t){if(!e)throw Error("Invariant failed")}(!1),t)}};function vZ(e,t){var r,n,i=eB(t=>uU(t,e)),a=eB(e=>uW(e,t)),o=null!=(r=null==i?void 0:i.allowDataOverflow)?r:uF.allowDataOverflow,l=null!=(n=null==a?void 0:a.allowDataOverflow)?n:u$.allowDataOverflow;return{needClip:o||l,needClipX:o,needClipY:l}}function vJ(e){var{xAxisId:t,yAxisId:r,clipPathId:n}=e,i=yQ(),{needClipX:a,needClipY:o,needClip:l}=vZ(t,r);if(!l)return null;var{x:c,y:u,width:s,height:f}=i;return p.createElement("clipPath",{id:"clipPath-".concat(n)},p.createElement("rect",{x:a?c:c-s/2,y:o?u:u-f/2,width:a?s:2*s,height:o?f:2*f}))}var vQ=["onMouseEnter","onMouseLeave","onClick"],v0=["value","background","tooltipPosition"],v1=["onMouseEnter","onClick","onMouseLeave"];function v2(){return(v2=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function v5(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function v3(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v5(Object(r),!0).forEach(function(t){v4(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v5(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function v4(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function v6(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function v8(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:i,name:a,hide:o,unit:l}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:i,dataKey:t,nameKey:void 0,name:ne(a,t),hide:o,type:e.tooltipType,color:e.fill,unit:l}}}function v7(e){var t=eB(pc),{data:r,dataKey:n,background:i,allOtherBarProps:a}=e,{onMouseEnter:o,onMouseLeave:l,onClick:c}=a,u=v6(a,vQ),s=yS(o,n),f=yA(l),d=yk(c,n);if(!i||null==r)return null;var h=U(i,!1);return p.createElement(p.Fragment,null,r.map((e,r)=>{var{value:a,background:o,tooltipPosition:l}=e,c=v6(e,v0);if(!o)return null;var y=s(e,r),v=f(e,r),m=d(e,r),g=v3(v3(v3(v3(v3({option:i,isActive:String(r)===t},c),{},{fill:"#eee"},o),h),I(u,e,r)),{},{onMouseEnter:y,onMouseLeave:v,onClick:m,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return p.createElement(vY,v2({key:"background-bar-".concat(r)},g))}))}function v9(e){var{data:t,props:r,showLabels:n}=e,i=U(r,!1),{shape:a,dataKey:o,activeBar:l}=r,c=eB(pc),u=eB(ps),{onMouseEnter:s,onClick:f,onMouseLeave:d}=r,h=v6(r,v1),y=yS(s,o),v=yA(d),m=yk(f,o);return t?p.createElement(p.Fragment,null,t.map((e,t)=>{var r=l&&String(t)===c&&(null==u||o===u),n=v3(v3(v3({},i),e),{},{isActive:r,option:r?l:a,index:t,dataKey:o});return p.createElement(q,v2({className:"recharts-bar-rectangle"},I(h,e,t),{onMouseEnter:y(e,t),onMouseLeave:v(e,t),onClick:m(e,t),key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(t)}),p.createElement(vY,n))}),n&&dV.renderCallByParent(r,t)):null}function me(e){var{props:t,previousRectanglesRef:r}=e,{data:n,layout:i,isAnimationActive:a,animationBegin:o,animationDuration:l,animationEasing:c,onAnimationEnd:u,onAnimationStart:s}=t,f=r.current,d=yN(t,"recharts-bar-"),[h,y]=(0,p.useState)(!1),v=(0,p.useCallback)(()=>{"function"==typeof u&&u(),y(!1)},[u]),m=(0,p.useCallback)(()=>{"function"==typeof s&&s(),y(!0)},[s]);return p.createElement(i6,{begin:o,duration:l,isActive:a,easing:c,from:{t:0},to:{t:1},onAnimationEnd:v,onAnimationStart:m,key:d},e=>{var{t:a}=e,o=1===a?n:n.map((e,t)=>{var r=f&&f[t];if(r){var n=S(r.x,e.x),o=S(r.y,e.y),l=S(r.width,e.width),c=S(r.height,e.height);return v3(v3({},e),{},{x:n(a),y:o(a),width:l(a),height:c(a)})}if("horizontal"===i){var u=S(0,e.height)(a);return v3(v3({},e),{},{y:e.y+e.height-u,height:u})}var s=S(0,e.width)(a);return v3(v3({},e),{},{width:s})});return a>0&&(r.current=o),p.createElement(q,null,p.createElement(v9,{props:t,data:o,showLabels:!h}))})}function mt(e){var{data:t,isAnimationActive:r}=e,n=(0,p.useRef)(null);return r&&t&&t.length&&(null==n.current||n.current!==t)?p.createElement(me,{previousRectanglesRef:n,props:e}):p.createElement(v9,{props:e,data:t,showLabels:!0})}var mr=(e,t)=>{var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:rG(e,t)}};class mn extends p.PureComponent{render(){var{hide:e,data:t,dataKey:r,className:n,xAxisId:i,yAxisId:a,needClip:o,background:l,id:c,layout:u}=this.props;if(e)return null;var s=d("recharts-bar",n),f=null==c?this.id:c;return p.createElement(q,{className:s},o&&p.createElement("defs",null,p.createElement(vJ,{clipPathId:f,xAxisId:i,yAxisId:a})),p.createElement(q,{className:"recharts-bar-rectangles",clipPath:o?"url(#clipPath-".concat(f,")"):null},p.createElement(v7,{data:t,dataKey:r,background:l,allOtherBarProps:this.props}),p.createElement(mt,this.props)),p.createElement(vB,{direction:"horizontal"===u?"y":"x"},this.props.children))}constructor(){super(...arguments),v4(this,"id",P("recharts-bar-"))}}var mi={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!n4.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function ma(e){var t,{xAxisId:r,yAxisId:n,hide:i,legendType:a,minPointSize:o,activeBar:l,animationBegin:c,animationDuration:u,animationEasing:s,isAnimationActive:f}=iL(e,mi),{needClip:d}=vZ(r,n),h=nA(),y=nv(),v=(0,p.useMemo)(()=>({barSize:e.barSize,data:void 0,dataKey:e.dataKey,maxBarSize:e.maxBarSize,minPointSize:o,stackId:r2(e.stackId)}),[e.barSize,e.dataKey,e.maxBarSize,o,e.stackId]),m=B(e.children,de),g=eB(e=>mw(e,r,n,y,v,m));if("vertical"!==h&&"horizontal"!==h)return null;var b=null==g?void 0:g[0];return t=null==b||null==b.height||null==b.width?0:"vertical"===h?b.height/2:b.width/2,p.createElement(vD,{xAxisId:r,yAxisId:n,data:g,dataPointFormatter:mr,errorBarOffset:t},p.createElement(mn,v2({},e,{layout:h,needClip:d,data:g,xAxisId:r,yAxisId:n,hide:i,legendType:a,minPointSize:o,activeBar:l,animationBegin:c,animationDuration:u,animationEasing:s,isAnimationActive:f})))}class mo extends p.PureComponent{render(){return p.createElement(vN,{type:"bar",data:null,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:this.props.stackId,hide:this.props.hide,barSize:this.props.barSize},p.createElement(vA,null),p.createElement(yC,{legendPayload:(e=>{var{dataKey:t,name:r,fill:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:ne(r,t),payload:e}]})(this.props)}),p.createElement(yM,{fn:v8,args:this.props}),p.createElement(ma,this.props))}}function ml(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function mc(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ml(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ml(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}v4(mo,"displayName","Bar"),v4(mo,"defaultProps",mi);var mu=(e,t,r,n,i)=>i,ms=(e,t,r)=>{var n=null!=r?r:e;if(null!=n)return j(n,t,0)},mf=e3([nS,uZ,(e,t)=>t,(e,t,r)=>r,(e,t,r,n)=>n],(e,t,r,n,i)=>t.filter(t=>"horizontal"===e?t.xAxisId===r:t.yAxisId===n).filter(e=>e.isPanorama===i).filter(e=>!1===e.hide).filter(e=>"bar"===e.type));function mp(e){return null!=e.stackId&&null!=e.dataKey}var md=(e,t,r)=>{var n=e.filter(mp),i=e.filter(e=>null==e.stackId);return[...Object.entries(n.reduce((e,t)=>(e[t.stackId]||(e[t.stackId]=[]),e[t.stackId].push(t),e),{})).map(e=>{var[n,i]=e;return{stackId:n,dataKeys:i.map(e=>e.dataKey),barSize:ms(t,r,i[0].barSize)}}),...i.map(e=>({stackId:void 0,dataKeys:[e.dataKey].filter(e=>null!=e),barSize:ms(t,r,e.barSize)}))]},mh=e3([mf,up,(e,t,r)=>"horizontal"===nS(e)?s2(e,"xAxis",t):s2(e,"yAxis",r)],md),my=(e,t,r,n)=>{var i,a;return"horizontal"===nS(e)?(i=fr(e,"xAxis",t,n),a=ft(e,"xAxis",t,n)):(i=fr(e,"yAxis",r,n),a=ft(e,"yAxis",r,n)),r7(i,a)},mv=(e,t,r,n,i,a,o)=>{var l=function(e,t,r,n,i){var a,o=n.length;if(!(o<1)){var l=j(e,r,0,!0),c=[];if(iO(n[0].barSize)){var u=!1,s=r/o,f=n.reduce((e,t)=>e+(t.barSize||0),0);(f+=(o-1)*l)>=r&&(f-=(o-1)*l,l=0),f>=r&&s>0&&(u=!0,s*=.9,f=o*s);var p={offset:((r-f)/2|0)-l,size:0};a=n.reduce((e,t)=>{var r,n=[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:p.offset+p.size+l,size:u?s:null!=(r=t.barSize)?r:0}}];return p=n[n.length-1].position,n},c)}else{var d=j(t,r,0,!0);r-2*d-(o-1)*l<=0&&(l=0);var h=(r-2*d-(o-1)*l)/o;h>1&&(h>>=0);var y=iO(i)?Math.min(h,i):h;a=n.reduce((e,t,r)=>[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:d+(h+l)*r+(h-y)/2,size:y}}],c)}return a}}(r,n,i!==a?i:a,e,null==o?t:o);return i!==a&&null!=l&&(l=l.map(e=>mc(mc({},e),{},{position:mc(mc({},e.position),{},{offset:e.position.offset-i/2})}))),l},mm=e3([mh,uu,us,uf,(e,t,r,n,i)=>{var a,o,l,c,u=nS(e),s=uu(e),{maxBarSize:f}=i,p=null==f?s:f;return"horizontal"===u?(l=fr(e,"xAxis",t,n),c=ft(e,"xAxis",t,n)):(l=fr(e,"yAxis",r,n),c=ft(e,"yAxis",r,n)),null!=(a=null!=(o=r7(l,c,!0))?o:p)?a:0},my,(e,t,r,n,i)=>i.maxBarSize],mv),mg=e3([mm,mu],(e,t)=>{if(null!=e){var r=e.find(e=>e.stackId===t.stackId&&e.dataKeys.includes(t.dataKey));if(null!=r)return r.position}}),mb=(e,t)=>{if(!e||(null==t?void 0:t.dataKey)==null)return;var{stackId:r}=t;if(null!=r){var n=e[r];if(n){var{stackedData:i}=n;if(i)return i.find(e=>e.key===t.dataKey)}}},mx=e3([uZ,mu],(e,t)=>{if(e.some(e=>"bar"===e.type&&t.dataKey===e.dataKey&&t.stackId===e.stackId&&t.stackId===e.stackId))return t}),mO=e3([(e,t,r,n)=>"horizontal"===nS(e)?sr(e,"yAxis",r,n):sr(e,"xAxis",t,n),mu],mb),mw=e3([np,(e,t,r,n)=>fr(e,"xAxis",t,n),(e,t,r,n)=>fr(e,"yAxis",r,n),(e,t,r,n)=>ft(e,"xAxis",t,n),(e,t,r,n)=>ft(e,"yAxis",r,n),mg,nS,cZ,my,mO,mx,(e,t,r,n,i,a)=>a],(e,t,r,n,i,a,o,l,c,u,s,f)=>{var p,{chartData:d,dataStartIndex:h,dataEndIndex:y}=l;if(null!=s&&null!=a&&("horizontal"===o||"vertical"===o)&&null!=t&&null!=r&&null!=n&&null!=i&&null!=c){var{data:v}=s;if(null!=(p=null!=v&&v.length>0?v:null==d?void 0:d.slice(h,y+1)))return function(e){var{layout:t,barSettings:{dataKey:r,minPointSize:n},pos:i,bandSize:a,xAxis:o,yAxis:l,xAxisTicks:c,yAxisTicks:u,stackedData:s,displayedData:f,offset:p,cells:d}=e,h="horizontal"===t?l:o,y=s?h.scale.domain():null,v=r4({numericAxis:h});return f.map((e,f)=>{s?b=r0(s[f],y):Array.isArray(b=rG(e,r))||(b=[v,b]);var h=vG(n,0)(b[1],f);if("horizontal"===t){var b,x,O,w,P,j,E,[S,A]=[l.scale(b[0]),l.scale(b[1])];x=r3({axis:o,ticks:c,bandSize:a,offset:i.offset,entry:e,index:f}),O=null!=(E=null!=A?A:S)?E:void 0,w=i.size;var k=S-A;if(P=g(k)?0:k,j={x,y:p.top,width:w,height:p.height},Math.abs(h)>0&&Math.abs(P)<Math.abs(h)){var M=m(P||h)*(Math.abs(h)-Math.abs(P));O-=M,P+=M}}else{var[T,C]=[o.scale(b[0]),o.scale(b[1])];if(x=T,O=r3({axis:l,ticks:u,bandSize:a,offset:i.offset,entry:e,index:f}),w=C-T,P=i.size,j={x:p.left,y:O,width:p.width,height:P},Math.abs(h)>0&&Math.abs(w)<Math.abs(h)){var D=m(w||h)*(Math.abs(h)-Math.abs(w));w+=D}}return v3(v3({},e),{},{x,y:O,width:w,height:P,value:s?b:b[1],payload:e,background:j,tooltipPosition:{x:x+w/2,y:O+P/2}},d&&d[f]&&d[f].props)})}({layout:o,barSettings:s,pos:a,bandSize:c,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,stackedData:u,displayedData:p,offset:e,cells:f})}});function mP(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function mj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mP(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mP(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var mE=e3([(e,t)=>uS(e,t),(e,t)=>ho(e,"radiusAxis",t)],(e,t)=>{if(null!=e&&null!=t)return mj(mj({},e),{},{scale:t})}),mS=(e,t,r,n)=>hu(e,"radiusAxis",t,n),mA=e3([(e,t,r)=>uE(e,r),(e,t,r)=>ho(e,"angleAxis",r)],(e,t)=>{if(null!=e&&null!=t)return mj(mj({},e),{},{scale:t})}),mk=(e,t,r,n)=>hc(e,"angleAxis",r,n),mM=e3([d2,(e,t,r,n)=>n],(e,t)=>{if(e.some(e=>"radialBar"===e.type&&t.dataKey===e.dataKey&&t.stackId===e.stackId))return t}),mT=e3([nS,mE,mS,mA,mk],(e,t,r,n,i)=>rZ(e,"radiusAxis")?r7(t,r,!1):r7(n,i,!1)),mC=e3([mA,mE,nS],(e,t,r)=>{var n="radial"===r?e:t;if(null!=n&&null!=n.scale)return r4({numericAxis:n})}),mD=(e,t,r,n,i)=>n.maxBarSize,mN=e3([nS,d2,(e,t,r,n,i)=>r,(e,t,r,n,i)=>t],(e,t,r,n)=>t.filter(t=>"centric"===e?t.angleAxisId===r:t.radiusAxisId===n).filter(e=>!1===e.hide).filter(e=>"radialBar"===e.type)),m_=e3([mN,up,()=>void 0],md),mI=e3([nS,uu,mA,mk,mE,mS,mD],(e,t,r,n,i,a,o)=>{var l,c,u,s,f=null==o?t:o;return"centric"===e?null!=(u=null!=(s=r7(r,n,!0))?s:f)?u:0:null!=(l=null!=(c=r7(i,a,!0))?c:f)?l:0}),mL=e3([m_,uu,us,uf,mI,mT,mD],mv),mR=e3([mL,mM],(e,t)=>{if(null!=e&&null!=t){var r=e.find(e=>e.stackId===t.stackId&&null!=t.dataKey&&e.dataKeys.includes(t.dataKey));if(null!=r)return r.position}}),mK=e3([d6,d3,ud],st),mz=e3([(e,t,r)=>"centric"===nS(e)?mK(e,"radiusAxis",t):mK(e,"angleAxis",r),mM],mb),mB=e3([mA,mk,mE,mS,cY,mM,mT,nS,mC,uI,(e,t,r,n,i)=>i,mR,mz],(e,t,r,n,i,a,o,l,c,u,s,f,p)=>{var{chartData:d,dataStartIndex:h,dataEndIndex:y}=i;if(null==a||null==r||null==e||null==d||null==o||null==f||"centric"!==l&&"radial"!==l||null==n)return[];var{dataKey:v,minPointSize:g}=a,{cx:b,cy:x,startAngle:O,endAngle:w}=u,P=d.slice(h,y+1),j="centric"===l?r:e,E=p?j.scale.domain():null;return function(e){var{displayedData:t,stackedData:r,dataStartIndex:n,stackedDomain:i,dataKey:a,baseValue:o,layout:l,radiusAxis:c,radiusAxisTicks:u,bandSize:s,pos:f,angleAxis:p,minPointSize:d,cx:h,cy:y,angleAxisTicks:v,cells:g,startAngle:b,endAngle:x}=e;return(null!=t?t:[]).map((e,t)=>{var O,w,P,j,E,S;if(r?O=r0(r[n+t],i):Array.isArray(O=rG(e,a))||(O=[o,O]),"radial"===l){w=r3({axis:c,ticks:u,bandSize:s,offset:f.offset,entry:e,index:t}),E=p.scale(O[1]),j=p.scale(O[0]),P=(null!=w?w:0)+f.size;var A=E-j;Math.abs(d)>0&&Math.abs(A)<Math.abs(d)&&(E+=m(A||d)*(Math.abs(d)-Math.abs(A))),S={background:{cx:h,cy:y,innerRadius:w,outerRadius:P,startAngle:b,endAngle:x}}}else{w=c.scale(O[0]),P=c.scale(O[1]),E=(null!=(j=r3({axis:p,ticks:v,bandSize:s,offset:f.offset,entry:e,index:t}))?j:0)+f.size;var k=P-w;Math.abs(d)>0&&Math.abs(k)<Math.abs(d)&&(P+=m(k||d)*(Math.abs(d)-Math.abs(k)))}return mH(mH(mH({},e),S),{},{payload:e,value:r?O:O[1],cx:h,cy:y,innerRadius:w,outerRadius:P,startAngle:j,endAngle:E},g&&g[t]&&g[t].props)})}({angleAxis:e,angleAxisTicks:t,bandSize:o,baseValue:c,cells:s,cx:b,cy:x,dataKey:v,dataStartIndex:h,displayedData:P,endAngle:w,layout:l,minPointSize:g,pos:f,radiusAxis:r,radiusAxisTicks:n,stackedData:p,stackedDomain:E,startAngle:O})}),mF=e3([cG,(e,t)=>t],(e,t)=>{var{chartData:r,dataStartIndex:n,dataEndIndex:i}=e;if(null==r)return[];var a=r.slice(n,i+1);return 0===a.length?[]:a.map(e=>({type:t,value:e.name,color:e.fill,payload:e}))}),mU=["shape","activeShape","cornerRadius"],m$=["onMouseEnter","onClick","onMouseLeave"],mW=["value","background"];function mV(){return(mV=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function mX(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function mH(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mX(Object(r),!0).forEach(function(t){mq(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mX(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function mq(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function mY(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var mG=[];function mZ(e){var{sectors:t,allOtherRadialBarProps:r,showLabels:n}=e,{shape:i,activeShape:a,cornerRadius:o}=r,l=mY(r,mU),c=U(l,!1),u=eB(pc),{onMouseEnter:s,onClick:f,onMouseLeave:d}=r,h=mY(r,m$),y=yS(s,r.dataKey),v=yA(d),m=yk(f,r.dataKey);return null==t?null:p.createElement(p.Fragment,null,t.map((e,t)=>{var r=a&&u===String(t),n=y(e,t),s=v(e,t),f=m(e,t),d=mH(mH(mH(mH({},c),{},{cornerRadius:vj(o)},e),I(h,e,t)),{},{onMouseEnter:n,onMouseLeave:s,onClick:f,key:"sector-".concat(t),className:"recharts-radial-bar-sector ".concat(e.className),forceCornerRadius:l.forceCornerRadius,cornerIsExternal:l.cornerIsExternal,isActive:r,option:r?a:i});return p.createElement(vS,d)}),n&&dV.renderCallByParent(r,t))}function mJ(e){var{props:t,previousSectorsRef:r}=e,{data:n,isAnimationActive:i,animationBegin:a,animationDuration:o,animationEasing:l,onAnimationEnd:c,onAnimationStart:u}=t,s=yN(t,"recharts-radialbar-"),f=r.current,[d,h]=(0,p.useState)(!0),y=(0,p.useCallback)(()=>{"function"==typeof c&&c(),h(!1)},[c]),v=(0,p.useCallback)(()=>{"function"==typeof u&&u(),h(!0)},[u]);return p.createElement(i6,{begin:a,duration:o,isActive:i,easing:l,from:{t:0},to:{t:1},onAnimationStart:v,onAnimationEnd:y,key:s},e=>{var{t:i}=e,a=1===i?n:(null!=n?n:mG).map((e,t)=>{var r=f&&f[t];if(r){var n=S(r.startAngle,e.startAngle),a=S(r.endAngle,e.endAngle);return mH(mH({},e),{},{startAngle:n(i),endAngle:a(i)})}var{endAngle:o,startAngle:l}=e,c=S(l,o);return mH(mH({},e),{},{endAngle:c(i)})});return i>0&&(r.current=null!=a?a:null),p.createElement(q,null,p.createElement(mZ,{sectors:null!=a?a:mG,allOtherRadialBarProps:t,showLabels:!d}))})}function mQ(e){var{data:t=[],isAnimationActive:r}=e,n=(0,p.useRef)(null),i=n.current;return r&&t&&t.length&&(!i||i!==t)?p.createElement(mJ,{props:e,previousSectorsRef:n}):p.createElement(mZ,{sectors:t,allOtherRadialBarProps:e,showLabels:!0})}function m0(e){var t=eB(t=>mF(t,e.legendType));return p.createElement(yD,{legendPayload:null!=t?t:[]})}function m1(e){var{dataKey:t,data:r,stroke:n,strokeWidth:i,name:a,hide:o,fill:l,tooltipType:c}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:l,nameKey:void 0,dataKey:t,name:ne(a,t),hide:o,type:c,color:l,unit:""}}}class m2 extends p.PureComponent{renderBackground(e){if(null==e)return null;var{cornerRadius:t}=this.props,r=U(this.props.background,!1);return e.map((e,n)=>{var{value:i,background:a}=e,o=mY(e,mW);if(!a)return null;var l=mH(mH(mH(mH(mH({cornerRadius:vj(t)},o),{},{fill:"#eee"},a),r),I(this.props,e,n)),{},{index:n,key:"sector-".concat(n),className:d("recharts-radial-bar-background-sector",null==r?void 0:r.className),option:a,isActive:!1});return p.createElement(vS,l)})}render(){var{hide:e,data:t,className:r,background:n}=this.props;if(e)return null;var i=d("recharts-area",r);return p.createElement(q,{className:i},n&&p.createElement(q,{className:"recharts-radial-bar-background"},this.renderBackground(t)),p.createElement(q,{className:"recharts-radial-bar-sectors"},p.createElement(mQ,this.props)))}}function m5(e){var t,r=B(e.children,de),n={dataKey:e.dataKey,minPointSize:e.minPointSize,stackId:e.stackId,maxBarSize:e.maxBarSize,barSize:e.barSize},i=null!=(t=eB(t=>mB(t,e.radiusAxisId,e.angleAxisId,n,r)))?t:mG;return p.createElement(p.Fragment,null,p.createElement(yM,{fn:m1,args:mH(mH({},e),{},{data:i})}),p.createElement(m2,mV({},e,{data:i})))}var m3={angleAxisId:0,radiusAxisId:0,minPointSize:0,hide:!1,legendType:"rect",data:[],isAnimationActive:!n4.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease",forceCornerRadius:!1,cornerIsExternal:!1};class m4 extends p.PureComponent{render(){var e,t,r;return p.createElement(p.Fragment,null,p.createElement(vA,null),p.createElement(y3,{data:void 0,dataKey:this.props.dataKey,hide:null!=(e=this.props.hide)?e:m3.hide,angleAxisId:null!=(t=this.props.angleAxisId)?t:m3.angleAxisId,radiusAxisId:null!=(r=this.props.radiusAxisId)?r:m3.radiusAxisId,stackId:this.props.stackId,barSize:this.props.barSize,type:"radialBar"}),p.createElement(m0,this.props),p.createElement(m5,this.props))}}function m6(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m8(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m6(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m6(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}mq(m4,"displayName","RadialBar"),mq(m4,"defaultProps",m3);var m7=["Webkit","Moz","O","ms"],m9=e=>{var{chartData:t}=e,r=eL(),n=nv();return(0,p.useEffect)(()=>n?()=>{}:(r(pH(t)),()=>{r(pH(void 0))}),[t,r,n]),null},ge=e=>{var{computedData:t}=e,r=eL();return(0,p.useEffect)(()=>(r(pY(t)),()=>{r(pH(void 0))}),[t,r]),null},gt=e=>e.chartData.chartData,gr=e=>{var{dataStartIndex:t,dataEndIndex:r}=e.chartData;return{startIndex:t,endIndex:r}},gn=(0,p.createContext)(()=>{}),gi={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},ga=t8({name:"brush",initialState:gi,reducers:{setBrushSettings:(e,t)=>null==t.payload?gi:t.payload}}),{setBrushSettings:go}=ga.actions,gl=ga.reducer;function gc(){return(gc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function gu(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gs(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gu(Object(r),!0).forEach(function(t){gf(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gu(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function gf(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function gp(e){var{x:t,y:r,width:n,height:i,stroke:a}=e,o=Math.floor(r+i/2)-1;return p.createElement(p.Fragment,null,p.createElement("rect",{x:t,y:r,width:n,height:i,fill:a,stroke:"none"}),p.createElement("line",{x1:t+1,y1:o,x2:t+n-1,y2:o,fill:"none",stroke:"#fff"}),p.createElement("line",{x1:t+1,y1:o+2,x2:t+n-1,y2:o+2,fill:"none",stroke:"#fff"}))}function gd(e){var{travellerProps:t,travellerType:r}=e;return p.isValidElement(r)?p.cloneElement(r,t):"function"==typeof r?r(t):p.createElement(gp,t)}function gh(e){var t,r,{otherProps:n,travellerX:i,id:a,onMouseEnter:o,onMouseLeave:l,onMouseDown:c,onTouchStart:u,onTravellerMoveKeyboard:s,onFocus:f,onBlur:d}=e,{y:h,x:y,travellerWidth:v,height:m,traveller:g,ariaLabel:b,data:x,startIndex:O,endIndex:w}=n,P=Math.max(i,y),j=gs(gs({},U(n,!1)),{},{x:P,y:h,width:v,height:m}),E=b||"Min value: ".concat(null==(t=x[O])?void 0:t.name,", Max value: ").concat(null==(r=x[w])?void 0:r.name);return p.createElement(q,{tabIndex:0,role:"slider","aria-label":E,"aria-valuenow":i,className:"recharts-brush-traveller",onMouseEnter:o,onMouseLeave:l,onMouseDown:c,onTouchStart:u,onKeyDown:e=>{["ArrowLeft","ArrowRight"].includes(e.key)&&(e.preventDefault(),e.stopPropagation(),s("ArrowRight"===e.key?1:-1,a))},onFocus:f,onBlur:d,style:{cursor:"col-resize"}},p.createElement(gd,{travellerType:g,travellerProps:j}))}function gy(e){var{index:t,data:r,tickFormatter:n,dataKey:i}=e,a=rG(r[t],i,t);return"function"==typeof n?n(a,t):a}function gv(e,t){for(var r=e.length,n=0,i=r-1;i-n>1;){var a=Math.floor((n+i)/2);e[a]>t?i=a:n=a}return t>=e[i]?i:n}function gm(e){var{startX:t,endX:r,scaleValues:n,gap:i,data:a}=e,o=a.length-1,l=Math.min(t,r),c=Math.max(t,r),u=gv(n,l),s=gv(n,c);return{startIndex:u-u%i,endIndex:s===o?o:s-s%i}}function gg(e){var{x:t,y:r,width:n,height:i,fill:a,stroke:o}=e;return p.createElement("rect",{stroke:o,fill:a,x:t,y:r,width:n,height:i})}function gb(e){var{startIndex:t,endIndex:r,y:n,height:i,travellerWidth:a,stroke:o,tickFormatter:l,dataKey:c,data:u,startX:s,endX:f}=e,d={pointerEvents:"none",fill:o};return p.createElement(q,{className:"recharts-brush-texts"},p.createElement(dS,gc({textAnchor:"end",verticalAnchor:"middle",x:Math.min(s,f)-5,y:n+i/2},d),gy({index:t,tickFormatter:l,dataKey:c,data:u})),p.createElement(dS,gc({textAnchor:"start",verticalAnchor:"middle",x:Math.max(s,f)+a+5,y:n+i/2},d),gy({index:r,tickFormatter:l,dataKey:c,data:u})))}function gx(e){var{y:t,height:r,stroke:n,travellerWidth:i,startX:a,endX:o,onMouseEnter:l,onMouseLeave:c,onMouseDown:u,onTouchStart:s}=e,f=Math.min(a,o)+i,d=Math.max(Math.abs(o-a)-i,0);return p.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:l,onMouseLeave:c,onMouseDown:u,onTouchStart:s,style:{cursor:"move"},stroke:"none",fill:n,fillOpacity:.2,x:f,y:t,width:d,height:r})}function gO(e){var{x:t,y:r,width:n,height:i,data:a,children:o,padding:l}=e;if(1!==p.Children.count(o))return null;var c=p.Children.only(o);return c?p.cloneElement(c,{x:t,y:r,width:n,height:i,margin:l,compact:!0,data:a}):null}var gw=e=>e.changedTouches&&!!e.changedTouches.length;class gP extends p.PureComponent{static getDerivedStateFromProps(e,t){var{data:r,width:n,x:i,travellerWidth:a,startIndex:o,endIndex:l,startIndexControlledFromProps:c,endIndexControlledFromProps:u}=e;if(r!==t.prevData)return gs({prevData:r,prevTravellerWidth:a,prevX:i,prevWidth:n},r&&r.length?(e=>{var{data:t,startIndex:r,endIndex:n,x:i,width:a,travellerWidth:o}=e;if(!t||!t.length)return{};var l=t.length,c=am().domain(ac()(0,l)).range([i,i+a-o]),u=c.domain().map(e=>c(e));return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:c(r),endX:c(n),scale:c,scaleValues:u}})({data:r,width:n,x:i,travellerWidth:a,startIndex:o,endIndex:l}):{scale:null,scaleValues:null});if(t.scale&&(n!==t.prevWidth||i!==t.prevX||a!==t.prevTravellerWidth)){t.scale.range([i,i+n-a]);var s=t.scale.domain().map(e=>t.scale(e));return{prevData:r,prevTravellerWidth:a,prevX:i,prevWidth:n,startX:t.scale(e.startIndex),endX:t.scale(e.endIndex),scaleValues:s}}if(t.scale&&!t.isSlideMoving&&!t.isTravellerMoving&&!t.isTravellerFocused&&!t.isTextActive){if(null!=c&&t.prevStartIndexControlledFromProps!==c)return{startX:t.scale(c),prevStartIndexControlledFromProps:c};if(null!=u&&t.prevEndIndexControlledFromProps!==u)return{endX:t.scale(u),prevEndIndexControlledFromProps:u}}return null}componentWillUnmount(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}attachDragEndListener(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}detachDragEndListener(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}handleSlideDrag(e){var{slideMoveStartX:t,startX:r,endX:n,scaleValues:i}=this.state,{x:a,width:o,travellerWidth:l,startIndex:c,endIndex:u,onChange:s,data:f,gap:p}=this.props,d=e.pageX-t;d>0?d=Math.min(d,a+o-l-n,a+o-l-r):d<0&&(d=Math.max(d,a-r,a-n));var h=gm({startX:r+d,endX:n+d,data:f,gap:p,scaleValues:i});(h.startIndex!==c||h.endIndex!==u)&&s&&s(h),this.setState({startX:r+d,endX:n+d,slideMoveStartX:e.pageX})}handleTravellerDragStart(e,t){var r=gw(t)?t.changedTouches[0]:t;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:e,brushMoveStartX:r.pageX}),this.attachDragEndListener()}handleTravellerMove(e){var{brushMoveStartX:t,movingTravellerId:r,endX:n,startX:i,scaleValues:a}=this.state,o=this.state[r],{x:l,width:c,travellerWidth:u,onChange:s,gap:f,data:p}=this.props,d={startX:this.state.startX,endX:this.state.endX,data:p,gap:f,scaleValues:a},h=e.pageX-t;h>0?h=Math.min(h,l+c-u-o):h<0&&(h=Math.max(h,l-o)),d[r]=o+h;var y=gm(d),{startIndex:v,endIndex:m}=y;this.setState({[r]:o+h,brushMoveStartX:e.pageX},()=>{var e;s&&(e=p.length-1,"startX"===r&&(n>i?v%f==0:m%f==0)||n<i&&m===e||"endX"===r&&(n>i?m%f==0:v%f==0)||n>i&&m===e||0)&&s(y)})}render(){var{data:e,className:t,children:r,x:n,y:i,dy:a,width:o,height:l,alwaysShowText:c,fill:u,stroke:s,startIndex:f,endIndex:h,travellerWidth:y,tickFormatter:v,dataKey:m,padding:g}=this.props,{startX:b,endX:O,isTextActive:w,isSlideMoving:P,isTravellerMoving:j,isTravellerFocused:E}=this.state;if(!e||!e.length||!x(n)||!x(i)||!x(o)||!x(l)||o<=0||l<=0)return null;var S=d("recharts-brush",t),A=((e,t)=>{if(!e)return null;var r=e.replace(/(\w)/,e=>e.toUpperCase()),n=m7.reduce((e,n)=>m8(m8({},e),{},{[n+r]:t}),{});return n[e]=t,n})("userSelect","none"),k=i+(null!=a?a:0);return p.createElement(q,{className:S,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:A},p.createElement(gg,{x:n,y:k,width:o,height:l,fill:u,stroke:s}),p.createElement(nm,null,p.createElement(gO,{x:n,y:k,width:o,height:l,data:e,padding:g},r)),p.createElement(gx,{y:k,height:l,stroke:s,travellerWidth:y,startX:b,endX:O,onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart}),p.createElement(gh,{travellerX:b,id:"startX",otherProps:gs(gs({},this.props),{},{y:k}),onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers.startX,onTouchStart:this.travellerDragStartHandlers.startX,onTravellerMoveKeyboard:this.handleTravellerMoveKeyboard,onFocus:()=>{this.setState({isTravellerFocused:!0})},onBlur:()=>{this.setState({isTravellerFocused:!1})}}),p.createElement(gh,{travellerX:O,id:"endX",otherProps:gs(gs({},this.props),{},{y:k}),onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers.endX,onTouchStart:this.travellerDragStartHandlers.endX,onTravellerMoveKeyboard:this.handleTravellerMoveKeyboard,onFocus:()=>{this.setState({isTravellerFocused:!0})},onBlur:()=>{this.setState({isTravellerFocused:!1})}}),(w||P||j||E||c)&&p.createElement(gb,{startIndex:f,endIndex:h,y:k,height:l,travellerWidth:y,stroke:s,tickFormatter:v,dataKey:m,data:e,startX:b,endX:O}))}constructor(e){super(e),gf(this,"handleDrag",e=>{this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.state.isTravellerMoving?this.handleTravellerMove(e):this.state.isSlideMoving&&this.handleSlideDrag(e)}),gf(this,"handleTouchMove",e=>{null!=e.changedTouches&&e.changedTouches.length>0&&this.handleDrag(e.changedTouches[0])}),gf(this,"handleDragEnd",()=>{this.setState({isTravellerMoving:!1,isSlideMoving:!1},()=>{var{endIndex:e,onDragEnd:t,startIndex:r}=this.props;null==t||t({endIndex:e,startIndex:r})}),this.detachDragEndListener()}),gf(this,"handleLeaveWrapper",()=>{(this.state.isTravellerMoving||this.state.isSlideMoving)&&(this.leaveTimer=window.setTimeout(this.handleDragEnd,this.props.leaveTimeOut))}),gf(this,"handleEnterSlideOrTraveller",()=>{this.setState({isTextActive:!0})}),gf(this,"handleLeaveSlideOrTraveller",()=>{this.setState({isTextActive:!1})}),gf(this,"handleSlideDragStart",e=>{var t=gw(e)?e.changedTouches[0]:e;this.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:t.pageX}),this.attachDragEndListener()}),gf(this,"handleTravellerMoveKeyboard",(e,t)=>{var{data:r,gap:n}=this.props,{scaleValues:i,startX:a,endX:o}=this.state,l=this.state[t],c=i.indexOf(l);if(-1!==c){var u=c+e;if(-1!==u&&!(u>=i.length)){var s=i[u];"startX"===t&&s>=o||"endX"===t&&s<=a||this.setState({[t]:s},()=>{this.props.onChange(gm({startX:this.state.startX,endX:this.state.endX,data:r,gap:n,scaleValues:i}))})}}}),this.travellerDragStartHandlers={startX:this.handleTravellerDragStart.bind(this,"startX"),endX:this.handleTravellerDragStart.bind(this,"endX")},this.state={}}}function gj(e){var t,r,n,i,a=eL(),o=eB(gt),{startIndex:l,endIndex:c}=eB(gr),u=(0,p.useContext)(gn),s=e.onChange,{startIndex:f,endIndex:d}=e;(0,p.useEffect)(()=>{a(pq({startIndex:f,endIndex:d}))},[a,d,f]),t=eB(uy),r=eB(um),n=eB(e=>e.chartData.dataStartIndex),i=eB(e=>e.chartData.dataEndIndex),(0,p.useEffect)(()=>{null!=t&&null!=n&&null!=i&&null!=r&&pK.emit(pB,t,{startIndex:n,endIndex:i},r)},[i,n,r,t]);var h=(0,p.useCallback)(e=>{(e.startIndex!==l||e.endIndex!==c)&&(null==u||u(e),null==s||s(e),a(pq(e)))},[s,u,a,l,c]),{x:y,y:v,width:m}=eB(nb);return p.createElement(gP,gc({},e,{data:o,x:y,y:v,width:m,startIndex:l,endIndex:c,onChange:h},{startIndexControlledFromProps:null!=f?f:void 0,endIndexControlledFromProps:null!=d?d:void 0}))}function gE(e){var t=eL();return(0,p.useEffect)(()=>(t(go(e)),()=>{t(go(null))}),[t,e]),null}class gS extends p.PureComponent{render(){return p.createElement(p.Fragment,null,p.createElement(gE,{height:this.props.height,x:this.props.x,y:this.props.y,width:this.props.width,padding:this.props.padding}),p.createElement(gj,this.props))}}function gA(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gk(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gA(Object(r),!0).forEach(function(t){gM(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gA(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function gM(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}gf(gS,"displayName","Brush"),gf(gS,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var gT=(e,t)=>{var{x:r,y:n}=e,{x:i,y:a}=t;return{x:Math.min(r,i),y:Math.min(n,a),width:Math.abs(i-r),height:Math.abs(a-n)}};class gC{static create(e){return new gC(e)}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(e){var{bandAware:t,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==e){if(r)switch(r){case"start":default:return this.scale(e);case"middle":var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+n;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(t){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}isInRange(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}constructor(e){this.scale=e}}gM(gC,"EPS",1e-4);var gD=e=>{var t=Object.keys(e).reduce((t,r)=>gk(gk({},t),{},{[r]:gC.create(e[r])}),{});return gk(gk({},t),{},{apply(e){var{bandAware:r,position:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Object.fromEntries(Object.entries(e).map(e=>{var[i,a]=e;return[i,t[i].apply(a,{bandAware:r,position:n})]}))},isInRange:e=>Object.keys(e).every(r=>t[r].isInRange(e[r]))})},gN=function(e){var{width:t,height:r}=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,a=Math.atan(r/t);return Math.abs(i>a&&i<Math.PI-a?r/Math.sin(i):t/Math.cos(i))},g_=t8({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=tY(e).dots.findIndex(e=>e===t.payload);-1!==r&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=tY(e).areas.findIndex(e=>e===t.payload);-1!==r&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=tY(e).lines.findIndex(e=>e===t.payload);-1!==r&&e.lines.splice(r,1)}}}),{addDot:gI,removeDot:gL,addArea:gR,removeArea:gK,addLine:gz,removeLine:gB}=g_.actions,gF=g_.reducer,gU=(0,p.createContext)(void 0),g$=e=>{var{children:t}=e,[r]=(0,p.useState)("".concat(P("recharts"),"-clip")),n=yQ();if(null==n)return null;var{x:i,y:a,width:o,height:l}=n;return p.createElement(gU.Provider,{value:r},p.createElement("defs",null,p.createElement("clipPath",{id:r},p.createElement("rect",{x:i,y:a,height:l,width:o}))),t)},gW=()=>(0,p.useContext)(gU);function gV(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gX(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gV(Object(r),!0).forEach(function(t){gH(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gV(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function gH(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function gq(){return(gq=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function gY(e){var t=eL();return(0,p.useEffect)(()=>(t(gz(e)),()=>{t(gB(e))})),null}function gG(e){var{x:t,y:r,segment:n,xAxisId:i,yAxisId:a,shape:o,className:l,ifOverflow:c}=e,u=nv(),s=gW(),f=eB(e=>uU(e,i)),h=eB(e=>uW(e,a)),y=eB(e=>sV(e,"xAxis",i,u)),v=eB(e=>sV(e,"yAxis",a,u)),m=nx(),b=O(t),x=O(r);if(!s||!m||null==f||null==h||null==y||null==v)return null;var w=((e,t,r,n,i,a,o,l,c)=>{var{x:u,y:s,width:f,height:p}=i;if(r){var{y:d}=c,h=e.y.apply(d,{position:a});if(g(h)||"discard"===c.ifOverflow&&!e.y.isInRange(h))return null;var y=[{x:u+f,y:h},{x:u,y:h}];return"left"===l?y.reverse():y}if(t){var{x:v}=c,m=e.x.apply(v,{position:a});if(g(m)||"discard"===c.ifOverflow&&!e.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===o?b.reverse():b}if(n){var{segment:x}=c,O=x.map(t=>e.apply(t,{position:a}));return"discard"===c.ifOverflow&&O.some(t=>!e.isInRange(t))?null:O}return null})(gD({x:y,y:v}),b,x,n&&2===n.length,m,e.position,f.orientation,h.orientation,e);if(!w)return null;var[{x:P,y:j},{x:E,y:S}]=w,A=gX(gX({clipPath:"hidden"===c?"url(#".concat(s,")"):void 0},U(e,!0)),{},{x1:P,y1:j,x2:E,y2:S});return p.createElement(q,{className:d("recharts-reference-line",l)},p.isValidElement(o)?p.cloneElement(o,A):"function"==typeof o?o(A):p.createElement("line",gq({},A,{className:"recharts-reference-line-line"})),d_.renderCallByParent(e,(e=>{var{x1:t,y1:r,x2:n,y2:i}=e;return gT({x:t,y:r},{x:n,y:i})})({x1:P,y1:j,x2:E,y2:S})))}function gZ(e){return p.createElement(p.Fragment,null,p.createElement(gY,{yAxisId:e.yAxisId,xAxisId:e.xAxisId,ifOverflow:e.ifOverflow,x:e.x,y:e.y}),p.createElement(gG,e))}class gJ extends p.Component{render(){return p.createElement(gZ,this.props)}}function gQ(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function g0(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gQ(Object(r),!0).forEach(function(t){g1(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gQ(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function g1(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function g2(){return(g2=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function g5(e){var t=eL();return(0,p.useEffect)(()=>(t(gI(e)),()=>{t(gL(e))})),null}function g3(e){var{x:t,y:r,r:n}=e,i=gW(),a=((e,t,r,n,i)=>{var a=O(e),o=O(t),l=nv(),c=eB(e=>sV(e,"xAxis",r,l)),u=eB(e=>sV(e,"yAxis",n,l));if(!a||!o||null==c||null==u)return null;var s=gD({x:c,y:u}),f=s.apply({x:e,y:t},{bandAware:!0});return"discard"!==i||s.isInRange(f)?f:null})(t,r,e.xAxisId,e.yAxisId,e.ifOverflow);if(!a)return null;var{x:o,y:l}=a,{shape:c,className:u,ifOverflow:s}=e,f=g0(g0({clipPath:"hidden"===s?"url(#".concat(i,")"):void 0},U(e,!0)),{},{cx:o,cy:l});return p.createElement(q,{className:d("recharts-reference-dot",u)},p.isValidElement(c)?p.cloneElement(c,f):"function"==typeof c?c(f):p.createElement(d1,g2({},f,{cx:f.cx,cy:f.cy,className:"recharts-reference-dot-dot"})),d_.renderCallByParent(e,{x:o-n,y:l-n,width:2*n,height:2*n}))}function g4(e){var{x:t,y:r,r:n,ifOverflow:i,yAxisId:a,xAxisId:o}=e;return p.createElement(p.Fragment,null,p.createElement(g5,{y:r,x:t,r:n,yAxisId:a,xAxisId:o,ifOverflow:i}),p.createElement(g3,e))}gH(gJ,"displayName","ReferenceLine"),gH(gJ,"defaultProps",{ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});class g6 extends p.Component{render(){return p.createElement(g4,this.props)}}function g8(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function g7(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g8(Object(r),!0).forEach(function(t){g9(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g8(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function g9(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function be(){return(be=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function bt(e){var t=eL();return(0,p.useEffect)(()=>(t(gR(e)),()=>{t(gK(e))})),null}function br(e){var t,{x1:r,x2:n,y1:i,y2:a,className:o,shape:l,xAxisId:c,yAxisId:u}=e,s=gW(),f=nv(),h=eB(e=>sV(e,"xAxis",c,f)),y=eB(e=>sV(e,"yAxis",u,f));if(null==h||null==!y)return null;var v=O(r),m=O(n),g=O(i),b=O(a);if(!v&&!m&&!g&&!b&&!l)return null;var x=((e,t,r,n,i,a,o)=>{var{x1:l,x2:c,y1:u,y2:s}=o;if(null==i||null==a)return null;var f=gD({x:i,y:a}),p={x:e?f.x.apply(l,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(u,{position:"start"}):f.y.rangeMin},d={x:t?f.x.apply(c,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(s,{position:"end"}):f.y.rangeMax};return"discard"!==o.ifOverflow||f.isInRange(p)&&f.isInRange(d)?gT(p,d):null})(v,m,g,b,h,y,e);if(!x&&!l)return null;var w="hidden"===e.ifOverflow;return p.createElement(q,{className:d("recharts-reference-area",o)},(t=g7(g7({clipPath:w?"url(#".concat(s,")"):void 0},U(e,!0)),x),p.isValidElement(l)?p.cloneElement(l,t):"function"==typeof l?l(t):p.createElement(ae,be({},t,{className:"recharts-reference-area-rect"}))),d_.renderCallByParent(e,x))}function bn(e){return p.createElement(p.Fragment,null,p.createElement(bt,{yAxisId:e.yAxisId,xAxisId:e.xAxisId,ifOverflow:e.ifOverflow,x1:e.x1,x2:e.x2,y1:e.y1,y2:e.y2}),p.createElement(br,e))}g1(g6,"displayName","ReferenceDot"),g1(g6,"defaultProps",{ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1});class bi extends p.Component{render(){return p.createElement(bn,this.props)}}function ba(e,t){for(var r in e)if(({}).hasOwnProperty.call(e,r)&&(!({}).hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if(({}).hasOwnProperty.call(t,n)&&!({}).hasOwnProperty.call(e,n))return!1;return!0}function bo(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],i=0;i<e.length;i+=t)if(void 0!==r&&!0!==r(e[i]))return;else n.push(e[i]);return n}function bl(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function bc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function bu(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?bc(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):bc(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function bs(e,t,r){var n,{tick:i,ticks:a,viewBox:o,minTickGap:l,orientation:c,interval:u,tickFormatter:s,unit:f,angle:p}=e;if(!a||!a.length||!i)return[];if(x(u)||n4.isSsr)return null!=(n=bo(a,(x(u)?u:0)+1))?n:[];var d="top"===c||"bottom"===c?"width":"height",h=f&&"width"===d?dl(f,{fontSize:t,letterSpacing:r}):{width:0,height:0},y=(e,n)=>{var i,a="function"==typeof s?s(e.value,n):e.value;return"width"===d?(i=dl(a,{fontSize:t,letterSpacing:r}),gN({width:i.width+h.width,height:i.height+h.height},p)):dl(a,{fontSize:t,letterSpacing:r})[d]},v=a.length>=2?m(a[1].coordinate-a[0].coordinate):1,g=function(e,t,r){var n="width"===r,{x:i,y:a,width:o,height:l}=e;return 1===t?{start:n?i:a,end:n?i+o:a+l}:{start:n?i+o:a+l,end:n?i:a}}(o,v,d);return"equidistantPreserveStart"===u?function(e,t,r,n,i){for(var a,o=(n||[]).slice(),{start:l,end:c}=t,u=0,s=1,f=l;s<=o.length;)if(a=function(){var t,a=null==n?void 0:n[u];if(void 0===a)return{v:bo(n,s)};var o=u,p=()=>(void 0===t&&(t=r(a,o)),t),d=a.coordinate,h=0===u||bl(e,d,p,f,c);h||(u=0,f=l,s+=1),h&&(f=d+e*(p()/2+i),u+=s)}())return a.v;return[]}(v,g,y,a,l):("preserveStart"===u||"preserveStartEnd"===u?function(e,t,r,n,i,a){var o=(n||[]).slice(),l=o.length,{start:c,end:u}=t;if(a){var s=n[l-1],f=r(s,l-1),p=e*(s.coordinate+e*f/2-u);o[l-1]=s=bu(bu({},s),{},{tickCoord:p>0?s.coordinate-p*e:s.coordinate}),bl(e,s.tickCoord,()=>f,c,u)&&(u=s.tickCoord-e*(f/2+i),o[l-1]=bu(bu({},s),{},{isShow:!0}))}for(var d=a?l-1:l,h=function(t){var n,a=o[t],l=()=>(void 0===n&&(n=r(a,t)),n);if(0===t){var s=e*(a.coordinate-e*l()/2-c);o[t]=a=bu(bu({},a),{},{tickCoord:s<0?a.coordinate-s*e:a.coordinate})}else o[t]=a=bu(bu({},a),{},{tickCoord:a.coordinate});bl(e,a.tickCoord,l,c,u)&&(c=a.tickCoord+e*(l()/2+i),o[t]=bu(bu({},a),{},{isShow:!0}))},y=0;y<d;y++)h(y);return o}(v,g,y,a,l,"preserveStartEnd"===u):function(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,{start:l}=t,{end:c}=t,u=function(t){var n,u=a[t],s=()=>(void 0===n&&(n=r(u,t)),n);if(t===o-1){var f=e*(u.coordinate+e*s()/2-c);a[t]=u=bu(bu({},u),{},{tickCoord:f>0?u.coordinate-f*e:u.coordinate})}else a[t]=u=bu(bu({},u),{},{tickCoord:u.coordinate});bl(e,u.tickCoord,s,l,c)&&(c=u.tickCoord-e*(s()/2+i),a[t]=bu(bu({},u),{},{isShow:!0}))},s=o-1;s>=0;s--)u(s);return a}(v,g,y,a,l)).filter(e=>e.isShow)}g9(bi,"displayName","ReferenceArea"),g9(bi,"defaultProps",{ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1});var bf=["viewBox"],bp=["viewBox"];function bd(){return(bd=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function bh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function by(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?bh(Object(r),!0).forEach(function(t){bm(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):bh(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function bv(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function bm(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class bg extends p.Component{shouldComponentUpdate(e,t){var{viewBox:r}=e,n=bv(e,bf),i=this.props,{viewBox:a}=i,o=bv(i,bp);return!ba(r,a)||!ba(n,o)||!ba(t,this.state)}getTickLineCoord(e){var t,r,n,i,a,o,{x:l,y:c,width:u,height:s,orientation:f,tickSize:p,mirror:d,tickMargin:h}=this.props,y=d?-1:1,v=e.tickSize||p,m=x(e.tickCoord)?e.tickCoord:e.coordinate;switch(f){case"top":t=r=e.coordinate,o=(n=(i=c+!d*s)-y*v)-y*h,a=m;break;case"left":n=i=e.coordinate,a=(t=(r=l+!d*u)-y*v)-y*h,o=m;break;case"right":n=i=e.coordinate,a=(t=(r=l+d*u)+y*v)+y*h,o=m;break;default:t=r=e.coordinate,o=(n=(i=c+d*s)+y*v)+y*h,a=m}return{line:{x1:t,y1:n,x2:r,y2:i},tick:{x:a,y:o}}}getTickTextAnchor(){var e,{orientation:t,mirror:r}=this.props;switch(t){case"left":e=r?"start":"end";break;case"right":e=r?"end":"start";break;default:e="middle"}return e}getTickVerticalAnchor(){var{orientation:e,mirror:t}=this.props;switch(e){case"left":case"right":return"middle";case"top":return t?"start":"end";default:return t?"end":"start"}}renderAxisLine(){var{x:e,y:t,width:r,height:n,orientation:i,mirror:a,axisLine:o}=this.props,l=by(by(by({},U(this.props,!1)),U(o,!1)),{},{fill:"none"});if("top"===i||"bottom"===i){var c=+("top"===i&&!a||"bottom"===i&&a);l=by(by({},l),{},{x1:e,y1:t+c*n,x2:e+r,y2:t+c*n})}else{var u=+("left"===i&&!a||"right"===i&&a);l=by(by({},l),{},{x1:e+u*r,y1:t,x2:e+u*r,y2:t+n})}return p.createElement("line",bd({},l,{className:d("recharts-cartesian-axis-line",y()(o,"className"))}))}static renderTickItem(e,t,r){var n,i=d(t.className,"recharts-cartesian-axis-tick-value");if(p.isValidElement(e))n=p.cloneElement(e,by(by({},t),{},{className:i}));else if("function"==typeof e)n=e(by(by({},t),{},{className:i}));else{var a="recharts-cartesian-axis-tick-value";"boolean"!=typeof e&&(a=d(a,e.className)),n=p.createElement(dS,bd({},t,{className:a}),r)}return n}renderTicks(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:n,stroke:i,tick:a,tickFormatter:o,unit:l}=this.props,c=bs(by(by({},this.props),{},{ticks:r}),e,t),u=this.getTickTextAnchor(),s=this.getTickVerticalAnchor(),f=U(this.props,!1),h=U(a,!1),v=by(by({},f),{},{fill:"none"},U(n,!1)),m=c.map((e,t)=>{var{line:r,tick:m}=this.getTickLineCoord(e),g=by(by(by(by({textAnchor:u,verticalAnchor:s},f),{},{stroke:"none",fill:i},h),m),{},{index:t,payload:e,visibleTicksCount:c.length,tickFormatter:o});return p.createElement(q,bd({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},I(this.props,e,t)),n&&p.createElement("line",bd({},v,r,{className:d("recharts-cartesian-axis-tick-line",y()(n,"className"))})),a&&bg.renderTickItem(a,g,"".concat("function"==typeof o?o(e.value,t):e.value).concat(l||"")))});return m.length>0?p.createElement("g",{className:"recharts-cartesian-axis-ticks"},m):null}render(){var{axisLine:e,width:t,height:r,className:n,hide:i}=this.props;if(i)return null;var{ticks:a}=this.props;return null!=t&&t<=0||null!=r&&r<=0?null:p.createElement(q,{className:d("recharts-cartesian-axis",n),ref:e=>{if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(t);var r=t[0];if(r){var n=window.getComputedStyle(r).fontSize,i=window.getComputedStyle(r).letterSpacing;(n!==this.state.fontSize||i!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},e&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,a),d_.renderCallByParent(this.props))}constructor(e){super(e),this.tickRefs=p.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}}bm(bg,"displayName","CartesianAxis"),bm(bg,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var bb=["x1","y1","x2","y2","key"],bx=["offset"],bO=["xAxisId","yAxisId"],bw=["xAxisId","yAxisId"];function bP(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function bj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?bP(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):bP(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function bE(){return(bE=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function bS(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var bA=e=>{var{fill:t}=e;if(!t||"none"===t)return null;var{fillOpacity:r,x:n,y:i,width:a,height:o,ry:l}=e;return p.createElement("rect",{x:n,y:i,ry:l,width:a,height:o,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function bk(e,t){var r;if(p.isValidElement(e))r=p.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var{x1:n,y1:i,x2:a,y2:o,key:l}=t,c=U(bS(t,bb),!1),{offset:u}=c,s=bS(c,bx);r=p.createElement("line",bE({},s,{x1:n,y1:i,x2:a,y2:o,fill:"none",key:l}))}return r}function bM(e){var{x:t,width:r,horizontal:n=!0,horizontalPoints:i}=e;if(!n||!i||!i.length)return null;var{xAxisId:a,yAxisId:o}=e,l=bS(e,bO),c=i.map((e,i)=>bk(n,bj(bj({},l),{},{x1:t,y1:e,x2:t+r,y2:e,key:"line-".concat(i),index:i})));return p.createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function bT(e){var{y:t,height:r,vertical:n=!0,verticalPoints:i}=e;if(!n||!i||!i.length)return null;var{xAxisId:a,yAxisId:o}=e,l=bS(e,bw),c=i.map((e,i)=>bk(n,bj(bj({},l),{},{x1:e,y1:t,x2:e,y2:t+r,key:"line-".concat(i),index:i})));return p.createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function bC(e){var{horizontalFill:t,fillOpacity:r,x:n,y:i,width:a,height:o,horizontalPoints:l,horizontal:c=!0}=e;if(!c||!t||!t.length)return null;var u=l.map(e=>Math.round(e+i-i)).sort((e,t)=>e-t);i!==u[0]&&u.unshift(0);var s=u.map((e,l)=>{var c=u[l+1]?u[l+1]-e:i+o-e;if(c<=0)return null;var s=l%t.length;return p.createElement("rect",{key:"react-".concat(l),y:e,x:n,height:c,width:a,stroke:"none",fill:t[s],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return p.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},s)}function bD(e){var{vertical:t=!0,verticalFill:r,fillOpacity:n,x:i,y:a,width:o,height:l,verticalPoints:c}=e;if(!t||!r||!r.length)return null;var u=c.map(e=>Math.round(e+i-i)).sort((e,t)=>e-t);i!==u[0]&&u.unshift(0);var s=u.map((e,t)=>{var c=u[t+1]?u[t+1]-e:i+o-e;if(c<=0)return null;var s=t%r.length;return p.createElement("rect",{key:"react-".concat(t),x:e,y:a,width:c,height:l,stroke:"none",fill:r[s],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return p.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},s)}var bN=(e,t)=>{var{xAxis:r,width:n,height:i,offset:a}=e;return rJ(bs(bj(bj(bj({},bg.defaultProps),r),{},{ticks:rQ(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.left,a.left+a.width,t)},b_=(e,t)=>{var{yAxis:r,width:n,height:i,offset:a}=e;return rJ(bs(bj(bj(bj({},bg.defaultProps),r),{},{ticks:rQ(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.top,a.top+a.height,t)},bI={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function bL(e){var t=nP(),r=nj(),n=nw(),i=bj(bj({},iL(e,bI)),{},{x:x(e.x)?e.x:n.left,y:x(e.y)?e.y:n.top,width:x(e.width)?e.width:n.width,height:x(e.height)?e.height:n.height}),{xAxisId:a,yAxisId:o,x:l,y:c,width:u,height:s,syncWithTicks:f,horizontalValues:d,verticalValues:h}=i,y=nv(),v=eB(e=>s8(e,"xAxis",a,y)),m=eB(e=>s8(e,"yAxis",o,y));if(!x(u)||u<=0||!x(s)||s<=0||!x(l)||l!==+l||!x(c)||c!==+c)return null;var g=i.verticalCoordinatesGenerator||bN,b=i.horizontalCoordinatesGenerator||b_,{horizontalPoints:O,verticalPoints:w}=i;if((!O||!O.length)&&"function"==typeof b){var P=d&&d.length,j=b({yAxis:m?bj(bj({},m),{},{ticks:P?d:m.ticks}):void 0,width:t,height:r,offset:n},!!P||f);p6(Array.isArray(j),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof j,"]")),Array.isArray(j)&&(O=j)}if((!w||!w.length)&&"function"==typeof g){var E=h&&h.length,S=g({xAxis:v?bj(bj({},v),{},{ticks:E?h:v.ticks}):void 0,width:t,height:r,offset:n},!!E||f);p6(Array.isArray(S),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof S,"]")),Array.isArray(S)&&(w=S)}return p.createElement("g",{className:"recharts-cartesian-grid"},p.createElement(bA,{fill:i.fill,fillOpacity:i.fillOpacity,x:i.x,y:i.y,width:i.width,height:i.height,ry:i.ry}),p.createElement(bC,bE({},i,{horizontalPoints:O})),p.createElement(bD,bE({},i,{verticalPoints:w})),p.createElement(bM,bE({},i,{offset:n,horizontalPoints:O,xAxis:v,yAxis:m})),p.createElement(bT,bE({},i,{offset:n,verticalPoints:w,xAxis:v,yAxis:m})))}bL.displayName="CartesianGrid";var bR=(e,t,r,n)=>fr(e,"xAxis",t,n),bK=(e,t,r,n)=>ft(e,"xAxis",t,n),bz=(e,t,r,n)=>fr(e,"yAxis",r,n),bB=(e,t,r,n)=>ft(e,"yAxis",r,n),bF=e3([nS,bR,bz,bK,bB],(e,t,r,n,i)=>rZ(e,"xAxis")?r7(t,n,!1):r7(r,i,!1)),bU=e3([uZ,(e,t,r,n,i)=>i],(e,t)=>{if(e.some(e=>"line"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),b$=e3([nS,bR,bz,bK,bB,bU,bF,cZ],(e,t,r,n,i,a,o,l)=>{var c,{chartData:u,dataStartIndex:s,dataEndIndex:f}=l;if(null!=a&&null!=t&&null!=r&&null!=n&&null!=i&&0!==n.length&&0!==i.length&&null!=o){var{dataKey:p,data:d}=a;if(null!=(c=null!=d&&d.length>0?d:null==u?void 0:u.slice(s,f+1)))return function(e){var{layout:t,xAxis:r,yAxis:n,xAxisTicks:i,yAxisTicks:a,dataKey:o,bandSize:l,displayedData:c}=e;return c.map((e,c)=>{var u=rG(e,o);if("horizontal"===t)return{x:r5({axis:r,ticks:i,bandSize:l,entry:e,index:c}),y:null==u?null:n.scale(u),value:u,payload:e};return{x:null==u?null:r.scale(u),y:r5({axis:n,ticks:a,bandSize:l,entry:e,index:c}),value:u,payload:e}})}({layout:e,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,dataKey:p,bandSize:o,displayedData:c})}}),bW=["type","layout","connectNulls","needClip"],bV=["activeDot","animateNewValues","animationBegin","animationDuration","animationEasing","connectNulls","dot","hide","isAnimationActive","label","legendType","xAxisId","yAxisId"];function bX(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function bH(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function bq(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?bH(Object(r),!0).forEach(function(t){bY(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):bH(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function bY(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bG(){return(bG=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function bZ(e){var{dataKey:t,data:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:l,unit:c}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,nameKey:void 0,name:ne(o,t),hide:l,type:e.tooltipType,color:e.stroke,unit:c}}}var bJ=(e,t)=>"".concat(t,"px ").concat(e-t,"px");function bQ(e){var{clipPathId:t,points:r,props:n}=e,{dot:i,dataKey:a,needClip:o}=n;if(null==r||!i&&1!==r.length)return null;var l=F(i),c=U(n,!1),u=U(i,!0),s=r.map((e,t)=>{var n,o=bq(bq(bq({key:"dot-".concat(t),r:3},c),u),{},{index:t,cx:e.x,cy:e.y,dataKey:a,value:e.value,payload:e.payload,points:r});if(p.isValidElement(i))n=p.cloneElement(i,o);else if("function"==typeof i)n=i(o);else{var l=d("recharts-line-dot","boolean"!=typeof i?i.className:"");n=p.createElement(d1,bG({},o,{className:l}))}return n}),f={clipPath:o?"url(#clipPath-".concat(l?"":"dots-").concat(t,")"):null};return p.createElement(q,bG({className:"recharts-line-dots",key:"dots"},f),s)}function b0(e){var{clipPathId:t,pathRef:r,points:n,strokeDasharray:i,props:a,showLabels:o}=e,{type:l,layout:c,connectNulls:u,needClip:s}=a,f=bq(bq({},U(bX(a,bW),!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:s?"url(#clipPath-".concat(t,")"):null,points:n,type:l,layout:c,connectNulls:u,strokeDasharray:null!=i?i:a.strokeDasharray});return p.createElement(p.Fragment,null,(null==n?void 0:n.length)>1&&p.createElement(iT,bG({},f,{pathRef:r})),p.createElement(bQ,{points:n,clipPathId:t,props:a}),o&&dV.renderCallByParent(a,n))}function b1(e){var{clipPathId:t,props:r,pathRef:n,previousPointsRef:i,longestAnimatedLengthRef:a}=e,{points:o,strokeDasharray:l,isAnimationActive:c,animationBegin:u,animationDuration:s,animationEasing:f,animateNewValues:d,width:h,height:y,onAnimationEnd:v,onAnimationStart:m}=r,g=i.current,b=yN(r,"recharts-line-"),[x,O]=(0,p.useState)(!1),w=(0,p.useCallback)(()=>{"function"==typeof v&&v(),O(!1)},[v]),P=(0,p.useCallback)(()=>{"function"==typeof m&&m(),O(!0)},[m]),j=function(e){try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch(e){return 0}}(n.current),E=a.current;return p.createElement(i6,{begin:u,duration:s,isActive:c,easing:f,from:{t:0},to:{t:1},onAnimationEnd:w,onAnimationStart:P,key:b},e=>{var c,{t:u}=e,s=Math.min(S(E,j+E)(u),j);if(c=l?((e,t,r)=>{var n=r.reduce((e,t)=>e+t);if(!n)return bJ(t,e);for(var i=Math.floor(e/n),a=e%n,o=t-e,l=[],c=0,u=0;c<r.length;u+=r[c],++c)if(u+r[c]>a){l=[...r.slice(0,c),a-u];break}var s=l.length%2==0?[0,o]:[o];return[...function(e,t){for(var r=e.length%2!=0?[...e,0]:e,n=[],i=0;i<t;++i)n=[...n,...r];return n}(r,i),...l,...s].map(e=>"".concat(e,"px")).join(", ")})(s,j,"".concat(l).split(/[,\s]+/gim).map(e=>parseFloat(e))):bJ(j,s),g){var f=g.length/o.length,v=1===u?o:o.map((e,t)=>{var r=Math.floor(t*f);if(g[r]){var n=g[r],i=S(n.x,e.x),a=S(n.y,e.y);return bq(bq({},e),{},{x:i(u),y:a(u)})}if(d){var o=S(2*h,e.x),l=S(y/2,e.y);return bq(bq({},e),{},{x:o(u),y:l(u)})}return bq(bq({},e),{},{x:e.x,y:e.y})});return i.current=v,p.createElement(b0,{props:r,points:v,clipPathId:t,pathRef:n,showLabels:!x,strokeDasharray:c})}return u>0&&j>0&&(i.current=o,a.current=s),p.createElement(b0,{props:r,points:o,clipPathId:t,pathRef:n,showLabels:!x,strokeDasharray:c})})}function b2(e){var{clipPathId:t,props:r}=e,{points:n,isAnimationActive:i}=r,a=(0,p.useRef)(null),o=(0,p.useRef)(0),l=(0,p.useRef)(null),c=a.current;return i&&n&&n.length&&c!==n?p.createElement(b1,{props:r,clipPathId:t,previousPointsRef:a,longestAnimatedLengthRef:o,pathRef:l}):p.createElement(b0,{props:r,points:n,clipPathId:t,pathRef:l,showLabels:!0})}var b5=(e,t)=>({x:e.x,y:e.y,value:e.value,errorVal:rG(e.payload,t)});class b3 extends p.Component{render(){var e,{hide:t,dot:r,points:n,className:i,xAxisId:a,yAxisId:o,top:l,left:c,width:u,height:s,id:f,needClip:h,layout:y}=this.props;if(t)return null;var v=d("recharts-line",i),m=null==f?this.id:f,{r:g=3,strokeWidth:b=2}=null!=(e=U(r,!1))?e:{r:3,strokeWidth:2},x=F(r),O=2*g+b;return p.createElement(p.Fragment,null,p.createElement(q,{className:v},h&&p.createElement("defs",null,p.createElement(vJ,{clipPathId:m,xAxisId:a,yAxisId:o}),!x&&p.createElement("clipPath",{id:"clipPath-dots-".concat(m)},p.createElement("rect",{x:c-O/2,y:l-O/2,width:u+O,height:s+O}))),p.createElement(b2,{props:this.props,clipPathId:m}),p.createElement(vB,{direction:"horizontal"===y?"y":"x"},p.createElement(vD,{xAxisId:a,yAxisId:o,data:n,dataPointFormatter:b5,errorBarOffset:0},this.props.children))),p.createElement(y5,{activeDot:this.props.activeDot,points:n,mainColor:this.props.stroke,itemDataKey:this.props.dataKey}))}constructor(){super(...arguments),bY(this,"id",P("recharts-line-"))}}var b4={activeDot:!0,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!0,fill:"#fff",hide:!1,isAnimationActive:!n4.isSsr,label:!1,legendType:"line",stroke:"#3182bd",strokeWidth:1,xAxisId:0,yAxisId:0};function b6(e){var t=iL(e,b4),{activeDot:r,animateNewValues:n,animationBegin:i,animationDuration:a,animationEasing:o,connectNulls:l,dot:c,hide:u,isAnimationActive:s,label:f,legendType:d,xAxisId:h,yAxisId:y}=t,v=bX(t,bV),{needClip:m}=vZ(h,y),{height:g,width:b,x:x,y:O}=yQ(),w=nA(),P=nv(),j=(0,p.useMemo)(()=>({dataKey:e.dataKey,data:e.data}),[e.dataKey,e.data]),E=eB(e=>b$(e,h,y,P,j));return"horizontal"!==w&&"vertical"!==w?null:p.createElement(b3,bG({},v,{connectNulls:l,dot:c,activeDot:r,animateNewValues:n,animationBegin:i,animationDuration:a,animationEasing:o,isAnimationActive:s,hide:u,label:f,legendType:d,xAxisId:h,yAxisId:y,points:E,layout:w,height:g,width:b,left:x,top:O,needClip:m}))}class b8 extends p.PureComponent{render(){return p.createElement(vN,{type:"line",data:this.props.data,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:void 0,hide:this.props.hide,barSize:void 0},p.createElement(yC,{legendPayload:(e=>{var{dataKey:t,name:r,stroke:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:ne(r,t),payload:e}]})(this.props)}),p.createElement(yM,{fn:bZ,args:this.props}),p.createElement(b6,this.props))}}bY(b8,"displayName","Line"),bY(b8,"defaultProps",b4);var b7=(e,t,r,n)=>fr(e,"xAxis",t,n),b9=(e,t,r,n)=>ft(e,"xAxis",t,n),xe=(e,t,r,n)=>fr(e,"yAxis",r,n),xt=(e,t,r,n)=>ft(e,"yAxis",r,n),xr=e3([nS,b7,xe,b9,xt],(e,t,r,n,i)=>rZ(e,"xAxis")?r7(t,n,!1):r7(r,i,!1)),xn=e3([uZ,(e,t,r,n,i)=>i],(e,t)=>{if(e.some(e=>"area"===e.type&&t.dataKey===e.dataKey&&r2(t.stackId)===e.stackId&&t.data===e.data))return t}),xi=e3([nS,b7,xe,b9,xt,(e,t,r,n,i)=>{if(null!=(o=rZ(nS(e),"xAxis")?sr(e,"yAxis",r,n):sr(e,"xAxis",t,n))){var a,o,{dataKey:l,stackId:c}=i;if(null!=c){var u=null==(a=o[c])?void 0:a.stackedData;return null==u?void 0:u.find(e=>e.key===l)}}},cZ,xr,xn],(e,t,r,n,i,a,o,l,c)=>{var u,{chartData:s,dataStartIndex:f,dataEndIndex:p}=o;if(null!=c&&("horizontal"===e||"vertical"===e)&&null!=t&&null!=r&&null!=n&&null!=i&&0!==n.length&&0!==i.length&&null!=l){var{data:d}=c;if(null!=(u=d&&d.length>0?d:null==s?void 0:s.slice(f,p+1)))return function(e){var t,{areaSettings:{connectNulls:r,baseValue:n,dataKey:i},stackedData:a,layout:o,chartBaseValue:l,xAxis:c,yAxis:u,displayedData:s,dataStartIndex:f,xAxisTicks:p,yAxisTicks:d,bandSize:h}=e,y=a&&a.length,v=((e,t,r,n,i)=>{var a=null!=r?r:t;if(x(a))return a;var o="horizontal"===e?i:n,l=o.scale.domain();if("number"===o.type){var c=Math.max(l[0],l[1]),u=Math.min(l[0],l[1]);return"dataMin"===a?u:"dataMax"===a||c<0?c:Math.max(Math.min(l[0],l[1]),0)}return"dataMin"===a?l[0]:"dataMax"===a?l[1]:l[0]})(o,l,n,c,u),m="horizontal"===o,g=!1,b=s.map((e,t)=>{y?n=a[f+t]:Array.isArray(n=rG(e,i))?g=!0:n=[v,n];var n,o=null==n[1]||y&&!r&&null==rG(e,i);return m?{x:r5({axis:c,ticks:p,bandSize:h,entry:e,index:t}),y:o?null:u.scale(n[1]),value:n,payload:e}:{x:o?null:c.scale(n[1]),y:r5({axis:u,ticks:d,bandSize:h,entry:e,index:t}),value:n,payload:e}});return t=y||g?b.map(e=>{var t=Array.isArray(e.value)?e.value[0]:null;return m?{x:e.x,y:null!=t&&null!=e.y?u.scale(t):null}:{x:null!=t?c.scale(t):null,y:e.y}}):m?u.scale(v):c.scale(v),{points:b,baseLine:t,isRange:g}}({layout:e,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,dataStartIndex:f,areaSettings:c,stackedData:a,displayedData:u,chartBaseValue:void 0,bandSize:l})}}),xa=["layout","type","stroke","connectNulls","isRange"],xo=["activeDot","animationBegin","animationDuration","animationEasing","connectNulls","dot","fill","fillOpacity","hide","isAnimationActive","legendType","stroke","xAxisId","yAxisId"];function xl(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function xc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function xu(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?xc(Object(r),!0).forEach(function(t){xs(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xc(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function xs(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function xf(){return(xf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function xp(e,t){return e&&"none"!==e?e:t}function xd(e){var{dataKey:t,data:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:l,unit:c}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,nameKey:void 0,name:ne(o,t),hide:l,type:e.tooltipType,color:xp(n,a),unit:c}}}function xh(e){var{clipPathId:t,points:r,props:n}=e,{needClip:i,dot:a,dataKey:o}=n;if(null==r||!a&&1!==r.length)return null;var l=F(a),c=U(n,!1),u=U(a,!0),s=r.map((e,t)=>{var n,i=xu(xu(xu({key:"dot-".concat(t),r:3},c),u),{},{index:t,cx:e.x,cy:e.y,dataKey:o,value:e.value,payload:e.payload,points:r});if(p.isValidElement(a))n=p.cloneElement(a,i);else if("function"==typeof a)n=a(i);else{var l=d("recharts-area-dot","boolean"!=typeof a?a.className:"");n=p.createElement(d1,xf({},i,{className:l}))}return n}),f={clipPath:i?"url(#clipPath-".concat(l?"":"dots-").concat(t,")"):void 0};return p.createElement(q,xf({className:"recharts-area-dots"},f),s)}function xy(e){var{points:t,baseLine:r,needClip:n,clipPathId:i,props:a,showLabels:o}=e,{layout:l,type:c,stroke:u,connectNulls:s,isRange:f}=a,d=xl(a,xa);return p.createElement(p.Fragment,null,(null==t?void 0:t.length)>1&&p.createElement(q,{clipPath:n?"url(#clipPath-".concat(i,")"):void 0},p.createElement(iT,xf({},U(d,!0),{points:t,connectNulls:s,type:c,baseLine:r,layout:l,stroke:"none",className:"recharts-area-area"})),"none"!==u&&p.createElement(iT,xf({},U(a,!1),{className:"recharts-area-curve",layout:l,type:c,connectNulls:s,fill:"none",points:t})),"none"!==u&&f&&p.createElement(iT,xf({},U(a,!1),{className:"recharts-area-curve",layout:l,type:c,connectNulls:s,fill:"none",points:r}))),p.createElement(xh,{points:t,props:a,clipPathId:i}),o&&dV.renderCallByParent(a,t))}function xv(e){var{alpha:t,baseLine:r,points:n,strokeWidth:i}=e,a=n[0].y,o=n[n.length-1].y;if(!iO(a)||!iO(o))return null;var l=t*Math.abs(a-o),c=Math.max(...n.map(e=>e.x||0));return(x(r)?c=Math.max(r,c):r&&Array.isArray(r)&&r.length&&(c=Math.max(...r.map(e=>e.x||0),c)),x(c))?p.createElement("rect",{x:0,y:a<o?a:a-l,width:c+(i?parseInt("".concat(i),10):1),height:Math.floor(l)}):null}function xm(e){var{alpha:t,baseLine:r,points:n,strokeWidth:i}=e,a=n[0].x,o=n[n.length-1].x;if(!iO(a)||!iO(o))return null;var l=t*Math.abs(a-o),c=Math.max(...n.map(e=>e.y||0));return(x(r)?c=Math.max(r,c):r&&Array.isArray(r)&&r.length&&(c=Math.max(...r.map(e=>e.y||0),c)),x(c))?p.createElement("rect",{x:a<o?a:a-l,y:0,width:l,height:Math.floor(c+(i?parseInt("".concat(i),10):1))}):null}function xg(e){var{alpha:t,layout:r,points:n,baseLine:i,strokeWidth:a}=e;return"vertical"===r?p.createElement(xv,{alpha:t,points:n,baseLine:i,strokeWidth:a}):p.createElement(xm,{alpha:t,points:n,baseLine:i,strokeWidth:a})}function xb(e){var{needClip:t,clipPathId:r,props:n,previousPointsRef:i,previousBaselineRef:a}=e,{points:o,baseLine:l,isAnimationActive:c,animationBegin:u,animationDuration:s,animationEasing:f,onAnimationStart:d,onAnimationEnd:h}=n,y=yN(n,"recharts-area-"),[v,m]=(0,p.useState)(!0),b=(0,p.useCallback)(()=>{"function"==typeof h&&h(),m(!1)},[h]),O=(0,p.useCallback)(()=>{"function"==typeof d&&d(),m(!0)},[d]),w=i.current,P=a.current;return p.createElement(i6,{begin:u,duration:s,isActive:c,easing:f,from:{t:0},to:{t:1},onAnimationEnd:b,onAnimationStart:O,key:y},e=>{var{t:c}=e;if(w){var u,s=w.length/o.length,f=1===c?o:o.map((e,t)=>{var r=Math.floor(t*s);if(w[r]){var n=w[r];return xu(xu({},e),{},{x:A(n.x,e.x,c),y:A(n.y,e.y,c)})}return e});if(x(l))u=A(P,l,c);else u=null==l||g(l)?A(P,0,c):l.map((e,t)=>{var r=Math.floor(t*s);if(Array.isArray(P)&&P[r]){var n=P[r];return xu(xu({},e),{},{x:A(n.x,e.x,c),y:A(n.y,e.y,c)})}return e});return c>0&&(i.current=f,a.current=u),p.createElement(xy,{points:f,baseLine:u,needClip:t,clipPathId:r,props:n,showLabels:!v})}return c>0&&(i.current=o,a.current=l),p.createElement(q,null,p.createElement("defs",null,p.createElement("clipPath",{id:"animationClipPath-".concat(r)},p.createElement(xg,{alpha:c,points:o,baseLine:l,layout:n.layout,strokeWidth:n.strokeWidth}))),p.createElement(q,{clipPath:"url(#animationClipPath-".concat(r,")")},p.createElement(xy,{points:o,baseLine:l,needClip:t,clipPathId:r,props:n,showLabels:!0})))})}function xx(e){var{needClip:t,clipPathId:r,props:n}=e,{points:i,baseLine:a,isAnimationActive:o}=n,l=(0,p.useRef)(null),c=(0,p.useRef)(),u=l.current,s=c.current;return o&&i&&i.length&&(u!==i||s!==a)?p.createElement(xb,{needClip:t,clipPathId:r,props:n,previousPointsRef:l,previousBaselineRef:c}):p.createElement(xy,{points:i,baseLine:a,needClip:t,clipPathId:r,props:n,showLabels:!0})}class xO extends p.PureComponent{render(){var e,{hide:t,dot:r,points:n,className:i,top:a,left:o,needClip:l,xAxisId:c,yAxisId:u,width:s,height:f,id:h,baseLine:y}=this.props;if(t)return null;var v=d("recharts-area",i),m=null==h?this.id:h,{r:g=3,strokeWidth:b=2}=null!=(e=U(r,!1))?e:{r:3,strokeWidth:2},x=F(r),O=2*g+b;return p.createElement(p.Fragment,null,p.createElement(q,{className:v},l&&p.createElement("defs",null,p.createElement(vJ,{clipPathId:m,xAxisId:c,yAxisId:u}),!x&&p.createElement("clipPath",{id:"clipPath-dots-".concat(m)},p.createElement("rect",{x:o-O/2,y:a-O/2,width:s+O,height:f+O}))),p.createElement(xx,{needClip:l,clipPathId:m,props:this.props})),p.createElement(y5,{points:n,mainColor:xp(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}),this.props.isRange&&Array.isArray(y)&&p.createElement(y5,{points:y,mainColor:xp(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}))}constructor(){super(...arguments),xs(this,"id",P("recharts-area-"))}}var xw={activeDot:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!1,fill:"#3182bd",fillOpacity:.6,hide:!1,isAnimationActive:!n4.isSsr,legendType:"line",stroke:"#3182bd",xAxisId:0,yAxisId:0};function xP(e){var t,r=iL(e,xw),{activeDot:n,animationBegin:i,animationDuration:a,animationEasing:o,connectNulls:l,dot:c,fill:u,fillOpacity:s,hide:f,isAnimationActive:d,legendType:h,stroke:y,xAxisId:v,yAxisId:m}=r,g=xl(r,xo),b=nA(),x=pb(),{needClip:O}=vZ(v,m),w=nv(),P=(0,p.useMemo)(()=>({baseValue:e.baseValue,stackId:e.stackId,connectNulls:l,data:e.data,dataKey:e.dataKey}),[e.baseValue,e.stackId,l,e.data,e.dataKey]),{points:j,isRange:E,baseLine:S}=null!=(t=eB(e=>xi(e,v,m,w,P)))?t:{},{height:A,width:k,x:M,y:T}=yQ();return"horizontal"!==b&&"vertical"!==b||"AreaChart"!==x&&"ComposedChart"!==x?null:p.createElement(xO,xf({},g,{activeDot:n,animationBegin:i,animationDuration:a,animationEasing:o,baseLine:S,connectNulls:l,dot:c,fill:u,fillOpacity:s,height:A,hide:f,layout:b,isAnimationActive:d,isRange:E,legendType:h,needClip:O,points:j,stroke:y,width:k,left:M,top:T,xAxisId:v,yAxisId:m}))}class xj extends p.PureComponent{render(){return p.createElement(vN,{type:"area",data:this.props.data,dataKey:this.props.dataKey,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,stackId:this.props.stackId,hide:this.props.hide,barSize:void 0},p.createElement(yC,{legendPayload:(e=>{var{dataKey:t,name:r,stroke:n,fill:i,legendType:a,hide:o}=e;return[{inactive:o,dataKey:t,type:a,color:xp(n,i),value:ne(r,t),payload:e}]})(this.props)}),p.createElement(yM,{fn:xd,args:this.props}),p.createElement(xP,this.props))}}function xE(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function xS(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?xE(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xE(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}xs(xj,"displayName","Area"),xs(xj,"defaultProps",xw);var xA=t8({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(e,t){e.xAxis[t.payload.id]=t.payload},removeXAxis(e,t){delete e.xAxis[t.payload.id]},addYAxis(e,t){e.yAxis[t.payload.id]=t.payload},removeYAxis(e,t){delete e.yAxis[t.payload.id]},addZAxis(e,t){e.zAxis[t.payload.id]=t.payload},removeZAxis(e,t){delete e.zAxis[t.payload.id]},updateYAxisWidth(e,t){var{id:r,width:n}=t.payload;e.yAxis[r]&&(e.yAxis[r]=xS(xS({},e.yAxis[r]),{},{width:n}))}}}),{addXAxis:xk,removeXAxis:xM,addYAxis:xT,removeYAxis:xC,addZAxis:xD,removeZAxis:xN,updateYAxisWidth:x_}=xA.actions,xI=xA.reducer;function xL(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function xR(e){var t=eL();return(0,p.useEffect)(()=>(t(xD(e)),()=>{t(xN(e))}),[e,t]),null}class xK extends p.Component{render(){return p.createElement(xR,{domain:this.props.domain,id:this.props.zAxisId,dataKey:this.props.dataKey,name:this.props.name,unit:this.props.unit,range:this.props.range,scale:this.props.scale,type:this.props.type,allowDuplicatedCategory:uV.allowDuplicatedCategory,allowDataOverflow:uV.allowDataOverflow,reversed:uV.reversed,includeHidden:uV.includeHidden})}}xL(xK,"displayName","ZAxis"),xL(xK,"defaultProps",{zAxisId:0,range:uV.range,scale:uV.scale,type:uV.type});var xz=["option","isActive"];function xB(){return(xB=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function xF(e){var{option:t,isActive:r}=e,n=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,xz);return"string"==typeof t?p.createElement(yE,xB({option:p.createElement(eE,xB({type:t},n)),isActive:r,shapeType:"symbols"},n)):p.createElement(yE,xB({option:t,isActive:r,shapeType:"symbols"},n))}var xU=e3([uZ,(e,t,r,n,i)=>i],(e,t)=>{if(e.some(e=>"scatter"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),x$=e3([(e,t,r,n,i,a,o)=>cZ(e,t,r,o),(e,t,r,n,i,a,o)=>fr(e,"xAxis",t,o),(e,t,r,n,i,a,o)=>ft(e,"xAxis",t,o),(e,t,r,n,i,a,o)=>fr(e,"yAxis",r,o),(e,t,r,n,i,a,o)=>ft(e,"yAxis",r,o),(e,t,r,n)=>fi(e,"zAxis",n,!1),xU,(e,t,r,n,i,a)=>a],(e,t,r,n,i,a,o,l)=>{var c,{chartData:u,dataStartIndex:s,dataEndIndex:f}=e;if(null!=o&&null!=(c=(null==o?void 0:o.data)!=null&&o.data.length>0?o.data:null==u?void 0:u.slice(s,f+1))&&null!=t&&null!=n&&null!=r&&null!=i&&(null==r?void 0:r.length)!==0&&(null==i?void 0:i.length)!==0)return function(e){var{displayedData:t,xAxis:r,yAxis:n,zAxis:i,scatterSettings:a,xAxisTicks:o,yAxisTicks:l,cells:c}=e,u=null==r.dataKey?a.dataKey:r.dataKey,s=null==n.dataKey?a.dataKey:n.dataKey,f=i&&i.dataKey,p=i?i.range:xK.defaultProps.range,d=p&&p[0],h=r.scale.bandwidth?r.scale.bandwidth():0,y=n.scale.bandwidth?n.scale.bandwidth():0;return t.map((e,t)=>{var p=rG(e,u),v=rG(e,s),m=null!=f&&rG(e,f)||"-",g=[{name:null==r.dataKey?a.name:r.name||r.dataKey,unit:r.unit||"",value:p,payload:e,dataKey:u,type:a.tooltipType},{name:null==n.dataKey?a.name:n.name||n.dataKey,unit:n.unit||"",value:v,payload:e,dataKey:s,type:a.tooltipType}];"-"!==m&&g.push({name:i.name||i.dataKey,unit:i.unit||"",value:m,payload:e,dataKey:f,type:a.tooltipType});var b=r5({axis:r,ticks:o,bandSize:h,entry:e,index:t,dataKey:u}),x=r5({axis:n,ticks:l,bandSize:y,entry:e,index:t,dataKey:s}),O="-"!==m?i.scale(m):d,w=Math.sqrt(Math.max(O,0)/Math.PI);return xY(xY({},e),{},{cx:b,cy:x,x:b-w,y:x-w,width:2*w,height:2*w,size:O,node:{x:p,y:v,z:m},tooltipPayload:g,tooltipPosition:{x:b,y:x},payload:e},c&&c[t]&&c[t].props)})}({displayedData:c,xAxis:t,yAxis:n,zAxis:a,scatterSettings:o,xAxisTicks:r,yAxisTicks:i,cells:l})}),xW=["onMouseEnter","onClick","onMouseLeave"],xV=["animationBegin","animationDuration","animationEasing","hide","isAnimationActive","legendType","lineJointType","lineType","shape","xAxisId","yAxisId","zAxisId"];function xX(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function xH(){return(xH=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function xq(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function xY(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?xq(Object(r),!0).forEach(function(t){xG(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xq(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function xG(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function xZ(e){var t,r,{points:n,props:i}=e,{line:a,lineType:o,lineJointType:l}=i;if(!a)return null;var c=U(i,!1),u=U(a,!1);if("joint"===o)t=n.map(e=>({x:e.cx,y:e.cy}));else if("fitting"===o){var{xmin:s,xmax:f,a:d,b:h}=(e=>{if(!e||!e.length)return null;for(var t=e.length,r=0,n=0,i=0,a=0,o=1/0,l=-1/0,c=0,u=0,s=0;s<t;s++)c=e[s].cx||0,u=e[s].cy||0,r+=c,n+=u,i+=c*u,a+=c*c,o=Math.min(o,c),l=Math.max(l,c);var f=t*a!=r*r?(t*i-r*n)/(t*a-r*r):0;return{xmin:o,xmax:l,a:f,b:(n-f*r)/t}})(n);t=[{x:s,y:d*s+h},{x:f,y:d*f+h}]}var y=xY(xY(xY({},c),{},{fill:"none",stroke:c&&c.fill},u),{},{points:t});return r=p.isValidElement(a)?p.cloneElement(a,y):"function"==typeof a?a(y):p.createElement(iT,xH({},y,{type:l})),p.createElement(q,{className:"recharts-scatter-line",key:"recharts-scatter-line"},r)}function xJ(e){var{points:t,showLabels:r,allOtherScatterProps:n}=e,{shape:i,activeShape:a,dataKey:o}=n,l=U(n,!1),c=eB(pc),{onMouseEnter:u,onClick:s,onMouseLeave:f}=n,d=xX(n,xW),h=yS(u,n.dataKey),y=yA(f),v=yk(s,n.dataKey);return null==t?null:p.createElement(p.Fragment,null,p.createElement(xZ,{points:t,props:n}),t.map((e,t)=>{var r=a&&c===String(t),n=r?a:i,u=xY(xY(xY({key:"symbol-".concat(t)},l),e),{},{[nc]:t,[nu]:String(o)});return p.createElement(q,xH({className:"recharts-scatter-symbol"},I(d,e,t),{onMouseEnter:h(e,t),onMouseLeave:y(e,t),onClick:v(e,t),key:"symbol-".concat(null==e?void 0:e.cx,"-").concat(null==e?void 0:e.cy,"-").concat(null==e?void 0:e.size,"-").concat(t)}),p.createElement(xF,xH({option:n,isActive:r},u)))}),r&&dV.renderCallByParent(n,t))}function xQ(e){var{previousPointsRef:t,props:r}=e,{points:n,isAnimationActive:i,animationBegin:a,animationDuration:o,animationEasing:l}=r,c=t.current,u=yN(r,"recharts-scatter-"),[s,f]=(0,p.useState)(!1),d=(0,p.useCallback)(()=>{f(!1)},[]),h=(0,p.useCallback)(()=>{f(!0)},[]);return p.createElement(i6,{begin:a,duration:o,isActive:i,easing:l,from:{t:0},to:{t:1},onAnimationEnd:d,onAnimationStart:h,key:u},e=>{var{t:i}=e,a=1===i?n:n.map((e,t)=>{var r=c&&c[t];if(r){var n=S(r.cx,e.cx),a=S(r.cy,e.cy),o=S(r.size,e.size);return xY(xY({},e),{},{cx:n(i),cy:a(i),size:o(i)})}var l=S(0,e.size);return xY(xY({},e),{},{size:l(i)})});return i>0&&(t.current=a),p.createElement(q,null,p.createElement(xJ,{points:a,allOtherScatterProps:r,showLabels:!s}))})}function x0(e){var{points:t,isAnimationActive:r}=e,n=(0,p.useRef)(null),i=n.current;return r&&t&&t.length&&(!i||i!==t)?p.createElement(xQ,{props:e,previousPointsRef:n}):p.createElement(xJ,{points:t,allOtherScatterProps:e,showLabels:!0})}function x1(e){var{dataKey:t,points:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:l,tooltipType:c}=e;return{dataDefinedOnItem:null==r?void 0:r.map(e=>e.tooltipPayload),positions:null==r?void 0:r.map(e=>e.tooltipPosition),settings:{stroke:n,strokeWidth:i,fill:a,nameKey:void 0,dataKey:t,name:ne(o,t),hide:l,type:c,color:a,unit:""}}}var x2=(e,t,r)=>({x:e.cx,y:e.cy,value:"x"===r?+e.node.x:+e.node.y,errorVal:rG(e,t)});function x5(e){var t=(0,p.useRef)(P("recharts-scatter-")),{hide:r,points:n,className:i,needClip:a,xAxisId:o,yAxisId:l,id:c,children:u}=e;if(r)return null;var s=d("recharts-scatter",i),f=null==c?t.current:c;return p.createElement(q,{className:s,clipPath:a?"url(#clipPath-".concat(f,")"):null},a&&p.createElement("defs",null,p.createElement(vJ,{clipPathId:f,xAxisId:o,yAxisId:l})),p.createElement(vD,{xAxisId:o,yAxisId:l,data:n,dataPointFormatter:x2,errorBarOffset:0},u),p.createElement(q,{key:"recharts-scatter-symbols"},p.createElement(x0,e)))}var x3={xAxisId:0,yAxisId:0,zAxisId:0,legendType:"circle",lineType:"joint",lineJointType:"linear",data:[],shape:"circle",hide:!1,isAnimationActive:!n4.isSsr,animationBegin:0,animationDuration:400,animationEasing:"linear"};function x4(e){var t=iL(e,x3),{animationBegin:r,animationDuration:n,animationEasing:i,hide:a,isAnimationActive:o,legendType:l,lineJointType:c,lineType:u,shape:s,xAxisId:f,yAxisId:d,zAxisId:h}=t,y=xX(t,xV),{needClip:v}=vZ(f,d),m=(0,p.useMemo)(()=>B(e.children,de),[e.children]),g=(0,p.useMemo)(()=>({name:e.name,tooltipType:e.tooltipType,data:e.data,dataKey:e.dataKey}),[e.data,e.dataKey,e.name,e.tooltipType]),b=nv(),x=eB(e=>x$(e,f,d,h,g,m,b));return null==v?null:p.createElement(p.Fragment,null,p.createElement(yM,{fn:x1,args:xY(xY({},e),{},{points:x})}),p.createElement(x5,xH({},y,{xAxisId:f,yAxisId:d,zAxisId:h,lineType:u,lineJointType:c,legendType:l,shape:s,hide:a,isAnimationActive:o,animationBegin:r,animationDuration:n,animationEasing:i,points:x,needClip:v})))}class x6 extends p.Component{render(){return p.createElement(vN,{type:"scatter",data:this.props.data,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:this.props.zAxisId,dataKey:this.props.dataKey,stackId:void 0,hide:this.props.hide,barSize:void 0},p.createElement(yC,{legendPayload:(e=>{var{dataKey:t,name:r,fill:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:ne(r,t),payload:e}]})(this.props)}),p.createElement(x4,this.props))}}xG(x6,"displayName","Scatter"),xG(x6,"defaultProps",x3);var x8=["children"],x7=["dangerouslySetInnerHTML","ticks"];function x9(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Oe(){return(Oe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function Ot(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function Or(e){var t=eL(),r=(0,p.useMemo)(()=>{var{children:t}=e;return Ot(e,x8)},[e]),n=eB(e=>uU(e,r.id)),i=r===n;return((0,p.useEffect)(()=>(t(xk(r)),()=>{t(xM(r))}),[r,t]),i)?e.children:null}var On=e=>{var{xAxisId:t,className:r}=e,n=eB(nh),i=nv(),a="xAxis",o=eB(e=>sV(e,a,t,i)),l=eB(e=>s9(e,a,t,i)),c=eB(e=>sJ(e,t)),u=eB(e=>((e,t)=>{var r=np(e),n=uU(e,t);if(null!=n){var i=sQ(e,n.orientation,n.mirror)[t];return null==i?{x:r.left,y:0}:{x:r.left,y:i}}})(e,t));if(null==c||null==u)return null;var{dangerouslySetInnerHTML:s,ticks:f}=e,h=Ot(e,x7);return p.createElement(bg,Oe({},h,{scale:o,x:u.x,y:u.y,width:c.width,height:c.height,className:d("recharts-".concat(a," ").concat(a),r),viewBox:n,ticks:l}))},Oi=e=>{var t,r,n,i,a;return p.createElement(Or,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.xAxisId,scale:e.scale,type:e.type,padding:e.padding,allowDataOverflow:e.allowDataOverflow,domain:e.domain,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,height:e.height,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(n=e.angle)?n:0,minTickGap:null!=(i=e.minTickGap)?i:5,tick:null==(a=e.tick)||a,tickFormatter:e.tickFormatter},p.createElement(On,e))};class Oa extends p.Component{render(){return p.createElement(Oi,this.props)}}x9(Oa,"displayName","XAxis"),x9(Oa,"defaultProps",{allowDataOverflow:uF.allowDataOverflow,allowDecimals:uF.allowDecimals,allowDuplicatedCategory:uF.allowDuplicatedCategory,height:uF.height,hide:!1,mirror:uF.mirror,orientation:uF.orientation,padding:uF.padding,reversed:uF.reversed,scale:uF.scale,tickCount:uF.tickCount,type:uF.type,xAxisId:0});var Oo=["dangerouslySetInnerHTML","ticks"];function Ol(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Oc(){return(Oc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function Ou(e){var t=eL();return(0,p.useEffect)(()=>(t(xT(e)),()=>{t(xC(e))}),[e,t]),null}var Os=e=>{var t,{yAxisId:r,className:n,width:i,label:a}=e,o=(0,p.useRef)(null),l=(0,p.useRef)(null),c=eB(nh),u=nv(),s=eL(),f="yAxis",h=eB(e=>sV(e,f,r,u)),y=eB(e=>s1(e,r)),v=eB(e=>((e,t)=>{var r=np(e),n=uW(e,t);if(null!=n){var i=s0(e,n.orientation,n.mirror)[t];return null==i?{x:0,y:r.top}:{x:i,y:r.top}}})(e,r)),m=eB(e=>s9(e,f,r,u));if((0,p.useLayoutEffect)(()=>{if(!("auto"!==i||!y||dN(a)||(0,p.isValidElement)(a))){var e,t=o.current,n=null==t||null==(e=t.tickRefs)?void 0:e.current,{tickSize:c,tickMargin:u}=t.props,f=(e=>{var{ticks:t,label:r,labelGapWithTick:n=5,tickSize:i=0,tickMargin:a=0}=e,o=0;if(t){t.forEach(e=>{if(e){var t=e.getBoundingClientRect();t.width>o&&(o=t.width)}});var l=r?r.getBoundingClientRect().width:0;return Math.round(o+(i+a)+l+(r?n:0))}return 0})({ticks:n,label:l.current,labelGapWithTick:5,tickSize:c,tickMargin:u});Math.round(y.width)!==Math.round(f)&&s(x_({id:r,width:f}))}},[o,null==o||null==(t=o.current)||null==(t=t.tickRefs)?void 0:t.current,null==y?void 0:y.width,y,s,a,r,i]),null==y||null==v)return null;var{dangerouslySetInnerHTML:g,ticks:b}=e,x=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Oo);return p.createElement(bg,Oc({},x,{ref:o,labelRef:l,scale:h,x:v.x,y:v.y,width:y.width,height:y.height,className:d("recharts-".concat(f," ").concat(f),n),viewBox:c,ticks:m}))},Of=e=>{var t,r,n,i,a;return p.createElement(p.Fragment,null,p.createElement(Ou,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.yAxisId,scale:e.scale,type:e.type,domain:e.domain,allowDataOverflow:e.allowDataOverflow,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,padding:e.padding,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,width:e.width,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(n=e.angle)?n:0,minTickGap:null!=(i=e.minTickGap)?i:5,tick:null==(a=e.tick)||a,tickFormatter:e.tickFormatter}),p.createElement(Os,e))},Op={allowDataOverflow:u$.allowDataOverflow,allowDecimals:u$.allowDecimals,allowDuplicatedCategory:u$.allowDuplicatedCategory,hide:!1,mirror:u$.mirror,orientation:u$.orientation,padding:u$.padding,reversed:u$.reversed,scale:u$.scale,tickCount:u$.tickCount,type:u$.type,width:u$.width,yAxisId:0};class Od extends p.Component{render(){return p.createElement(Of,this.props)}}Ol(Od,"displayName","YAxis"),Ol(Od,"defaultProps",Op),r(39611);var Oh={notify(){},get:()=>[]},Oy="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,Ov="undefined"!=typeof navigator&&"ReactNative"===navigator.product,Om=Oy||Ov?p.useLayoutEffect:p.useEffect;Object.getOwnPropertyNames,Object.getOwnPropertySymbols,Object.getOwnPropertyDescriptor,Object.getPrototypeOf,Object.prototype;var Og=Symbol.for("react-redux-context"),Ob="undefined"!=typeof globalThis?globalThis:{},Ox=function(){if(!p.createContext)return{};let e=Ob[Og]??=new Map,t=e.get(p.createContext);return t||(t=p.createContext(null),e.set(p.createContext,t)),t}(),OO=function(e){let{children:t,context:r,serverState:n,store:i}=e,a=p.useMemo(()=>{let e=function(e,t){let r,n=Oh,i=0,a=!1;function o(){u.onStateChange&&u.onStateChange()}function l(){if(i++,!r){let t,i;r=e.subscribe(o),t=null,i=null,n={clear(){t=null,i=null},notify(){let e=t;for(;e;)e.callback(),e=e.next},get(){let e=[],r=t;for(;r;)e.push(r),r=r.next;return e},subscribe(e){let r=!0,n=i={callback:e,next:null,prev:i};return n.prev?n.prev.next=n:t=n,function(){r&&null!==t&&(r=!1,n.next?n.next.prev=n.prev:i=n.prev,n.prev?n.prev.next=n.next:t=n.next)}}}}}function c(){i--,r&&0===i&&(r(),r=void 0,n.clear(),n=Oh)}let u={addNestedSub:function(e){l();let t=n.subscribe(e),r=!1;return()=>{r||(r=!0,t(),c())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:o,isSubscribed:function(){return a},trySubscribe:function(){a||(a=!0,l())},tryUnsubscribe:function(){a&&(a=!1,c())},getListeners:()=>n};return u}(i);return{store:i,subscription:e,getServerState:n?()=>n:void 0}},[i,n]),o=p.useMemo(()=>i.getState(),[i]);return Om(()=>{let{subscription:e}=a;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),o!==i.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[a,o]),p.createElement((r||Ox).Provider,{value:a},t)},Ow=e3([(e,t)=>t,nS,uI,fL,f9,pn,pP,np],(e,t,r,n,i,a,o,l)=>{if(e&&t&&n&&i&&a){var c=function(e,t,r,n,i){return"horizontal"===r||"vertical"===r?e>=i.left&&e<=i.left+i.width&&t>=i.top&&t<=i.top+i.height?{x:e,y:t}:null:n?((e,t)=>{var r,{x:n,y:i}=e,{radius:a,angle:o}=((e,t)=>{var{x:r,y:n}=e,{cx:i,cy:a}=t,o=((e,t)=>{var{x:r,y:n}=e,{x:i,y:a}=t;return Math.sqrt((r-i)**2+(n-a)**2)})({x:r,y:n},{x:i,y:a});if(o<=0)return{radius:o,angle:0};var l=Math.acos((r-i)/o);return n>a&&(l=2*Math.PI-l),{radius:o,angle:180*l/Math.PI,angleInRadian:l}})({x:n,y:i},t),{innerRadius:l,outerRadius:c}=t;if(a<l||a>c||0===a)return null;var{startAngle:u,endAngle:s}=(e=>{var{startAngle:t,endAngle:r}=e,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}})(t),f=o;if(u<=s){for(;f>s;)f-=360;for(;f<u;)f+=360;r=f>=u&&f<=s}else{for(;f>u;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=u}return r?r$(r$({},t),{},{radius:a,angle:((e,t)=>{var{startAngle:r,endAngle:n}=t;return e+360*Math.min(Math.floor(r/360),Math.floor(n/360))})(f,t)}):null})({x:e,y:t},n):null}(e.chartX,e.chartY,t,r,l);if(c){var u=((e,t,r,n,i)=>{var a,o=-1,l=null!=(a=null==t?void 0:t.length)?a:0;if(l<=1||null==e)return 0;if("angleAxis"===n&&null!=i&&1e-6>=Math.abs(Math.abs(i[1]-i[0])-360))for(var c=0;c<l;c++){var u=c>0?r[c-1].coordinate:r[l-1].coordinate,s=r[c].coordinate,f=c>=l-1?r[0].coordinate:r[c+1].coordinate,p=void 0;if(m(s-u)!==m(f-s)){var d=[];if(m(f-s)===m(i[1]-i[0])){p=f;var h=s+i[1]-i[0];d[0]=Math.min(h,(h+u)/2),d[1]=Math.max(h,(h+u)/2)}else{p=u;var y=f+i[1]-i[0];d[0]=Math.min(s,(y+s)/2),d[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(e>v[0]&&e<=v[1]||e>=d[0]&&e<=d[1]){({index:o}=r[c]);break}}else{var g=Math.min(u,f),b=Math.max(u,f);if(e>(g+s)/2&&e<=(b+s)/2){({index:o}=r[c]);break}}}else if(t){for(var x=0;x<l;x++)if(0===x&&e<=(t[x].coordinate+t[x+1].coordinate)/2||x>0&&x<l-1&&e>(t[x].coordinate+t[x-1].coordinate)/2&&e<=(t[x].coordinate+t[x+1].coordinate)/2||x===l-1&&e>(t[x].coordinate+t[x-1].coordinate)/2){({index:o}=t[x]);break}}return o})(((e,t)=>"horizontal"===t?e.x:"vertical"===t?e.y:"centric"===t?e.angle:e.radius)(c,t),o,a,n,i),s=((e,t,r,n)=>{var i=t.find(e=>e&&e.index===r);if(i){if("horizontal"===e)return{x:i.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:i.coordinate};if("centric"===e){var a=i.coordinate,{radius:o}=n;return rY(rY(rY({},n),rV(n.cx,n.cy,o,a)),{},{angle:a,radius:o})}var l=i.coordinate,{angle:c}=n;return rY(rY(rY({},n),rV(n.cx,n.cy,l,c)),{},{angle:c,radius:l})}return{x:0,y:0}})(t,a,u,c);return{activeIndex:String(u),activeCoordinate:s}}}}),OP=e=>{var t=e.currentTarget.getBoundingClientRect(),r=t.width/e.currentTarget.offsetWidth,n=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/r),chartY:Math.round((e.clientY-t.top)/n)}},Oj=tQ("mouseClick"),OE=rM();OE.startListening({actionCreator:Oj,effect:(e,t)=>{var r=e.payload,n=Ow(t.getState(),OP(r));(null==n?void 0:n.activeIndex)!=null&&t.dispatch(fO({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var OS=tQ("mouseMove"),OA=rM();function Ok(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}OA.startListening({actionCreator:OS,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=fu(n,n.tooltip.settings.shared),a=Ow(n,OP(r));"axis"===i&&((null==a?void 0:a.activeIndex)!=null?t.dispatch(fx({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate})):t.dispatch(fg()))}});var OM={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},OT=t8({name:"rootProps",initialState:OM,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=null!=(r=t.payload.barGap)?r:OM.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),OC=OT.reducer,{updateOptions:OD}=OT.actions,ON=t8({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:O_}=ON.actions,OI=ON.reducer,OL=tQ("keyDown"),OR=tQ("focus"),OK=rM();OK.startListening({actionCreator:OL,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,i=e.payload;if("ArrowRight"===i||"ArrowLeft"===i||"Enter"===i){var a=Number(fk(n,fW(r))),o=pn(r);if("Enter"===i){var l=pA(r,"axis","hover",String(n.index));t.dispatch(fP({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:l}));return}var c=a+("ArrowRight"===i?1:-1)*("left-to-right"===fa(r)?1:-1);if(null!=o&&!(c>=o.length)&&!(c<0)){var u=pA(r,"axis","hover",String(c));t.dispatch(fP({active:!0,activeIndex:c.toString(),activeDataKey:void 0,activeCoordinate:u}))}}}}}),OK.startListening({actionCreator:OR,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var i=pA(r,"axis","hover",String("0"));t.dispatch(fP({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:i}))}}}});var Oz=tQ("externalEvent"),OB=rM();OB.startListening({actionCreator:Oz,effect:(e,t)=>{if(null!=e.payload.handler){var r=t.getState(),n={activeCoordinate:pd(r),activeDataKey:ps(r),activeIndex:pc(r),activeLabel:pu(r),activeTooltipIndex:pc(r),isTooltipActive:ph(r)};e.payload.handler(n,e.payload.reactEvent)}}});var OF=e3([fD],e=>e.tooltipItemPayloads),OU=e3([OF,fC,(e,t,r)=>t,(e,t,r)=>r],(e,t,r,n)=>{var i=e.find(e=>e.settings.dataKey===n);if(null!=i){var{positions:a}=i;if(null!=a)return t(a,r)}}),O$=tQ("touchMove"),OW=rM();OW.startListening({actionCreator:O$,effect:(e,t)=>{var r=e.payload,n=t.getState(),i=fu(n,n.tooltip.settings.shared);if("axis"===i){var a=Ow(n,OP({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(null==a?void 0:a.activeIndex)!=null&&t.dispatch(fx({activeIndex:a.activeIndex,activeDataKey:void 0,activeCoordinate:a.activeCoordinate}))}else if("item"===i){var o,l=r.touches[0],c=document.elementFromPoint(l.clientX,l.clientY);if(!c||!c.getAttribute)return;var u=c.getAttribute(nc),s=null!=(o=c.getAttribute(nu))?o:void 0,f=OU(t.getState(),u,s);t.dispatch(fv({activeDataKey:s,activeIndex:u,activeCoordinate:f}))}}});var OV=to({brush:gl,cartesianAxis:xI,chartData:pG,graphicalItems:yc,layout:rL,legend:nI,options:p$,polarAxis:hT,polarOptions:OI,referenceElements:gF,rootProps:OC,tooltip:fj}),OX=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return function(e){let t,r,n,i=function(e){let{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:i=!0}=e??{},a=new t0;return t&&("boolean"==typeof t?a.push(ts):a.push(tu(t.extraArgument))),a},{reducer:a,middleware:o,devTools:l=!0,duplicateMiddlewareCheck:c=!0,preloadedState:u,enhancers:s}=e||{};if("function"==typeof a)t=a;else if(ta(a))t=to(a);else throw Error(rT(1));r="function"==typeof o?o(i):i();let f=tl;l&&(f=tJ({trace:!1,..."object"==typeof l&&l}));let p=(n=function(...e){return t=>(r,n)=>{let i=t(r,n),a=()=>{throw Error(tt(15))},o={getState:i.getState,dispatch:(e,...t)=>a(e,...t)};return a=tl(...e.map(e=>e(o)))(i.dispatch),{...i,dispatch:a}}}(...r),function(e){let{autoBatch:t=!0}=e??{},r=new t0(n);return t&&r.push(((e={type:"raf"})=>t=>(...r)=>{let n=t(...r),i=!0,a=!1,o=!1,l=new Set,c="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:t5(10):"callback"===e.type?e.queueNotification:t5(e.timeout),u=()=>{o=!1,a&&(a=!1,l.forEach(e=>e()))};return Object.assign({},n,{subscribe(e){let t=n.subscribe(()=>i&&e());return l.add(e),()=>{t(),l.delete(e)}},dispatch(e){try{return(a=!(i=!e?.meta?.RTK_autoBatch))&&!o&&(o=!0,c(u)),n.dispatch(e)}finally{i=!0}}})})("object"==typeof t?t:void 0)),r});return function e(t,r,n){if("function"!=typeof t)throw Error(tt(2));if("function"==typeof r&&"function"==typeof n||"function"==typeof n&&"function"==typeof arguments[3])throw Error(tt(0));if("function"==typeof r&&void 0===n&&(n=r,r=void 0),void 0!==n){if("function"!=typeof n)throw Error(tt(1));return n(e)(t,r)}let i=t,a=r,o=new Map,l=o,c=0,u=!1;function s(){l===o&&(l=new Map,o.forEach((e,t)=>{l.set(t,e)}))}function f(){if(u)throw Error(tt(3));return a}function p(e){if("function"!=typeof e)throw Error(tt(4));if(u)throw Error(tt(5));let t=!0;s();let r=c++;return l.set(r,e),function(){if(t){if(u)throw Error(tt(6));t=!1,s(),l.delete(r),o=null}}}function d(e){if(!ta(e))throw Error(tt(7));if(void 0===e.type)throw Error(tt(8));if("string"!=typeof e.type)throw Error(tt(17));if(u)throw Error(tt(9));try{u=!0,a=i(a,e)}finally{u=!1}return(o=l).forEach(e=>{e()}),e}return d({type:ti.INIT}),{dispatch:d,subscribe:p,getState:f,replaceReducer:function(e){if("function"!=typeof e)throw Error(tt(10));i=e,d({type:ti.REPLACE})},[tr]:function(){return{subscribe(e){if("object"!=typeof e||null===e)throw Error(tt(11));function t(){e.next&&e.next(f())}return t(),{unsubscribe:p(t)}},[tr](){return this}}}}}(t,u,f(..."function"==typeof s?s(p):p()))}({reducer:OV,preloadedState:e,middleware:e=>e({serializableCheck:!1}).concat([OE.middleware,OA.middleware,OK.middleware,OB.middleware,OW.middleware]),devTools:{serialize:{replacer:Ok},name:"recharts-".concat(t)}})};function OH(e){var{preloadedState:t,children:r,reduxStoreName:n}=e,i=nv(),a=(0,p.useRef)(null);return i?r:(null==a.current&&(a.current=OX(t,n)),p.createElement(OO,{context:e_,store:a.current},r))}function Oq(e){var{layout:t,width:r,height:n,margin:i}=e,a=eL(),o=nv();return(0,p.useEffect)(()=>{o||(a(rN(t)),a(r_({width:r,height:n})),a(rD(i)))},[a,o,t,r,n,i]),null}function OY(e){var t=eL();return(0,p.useEffect)(()=>{t(OD(e))},[t,e]),null}var OG=["children"];function OZ(){return(OZ=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var OJ={width:"100%",height:"100%"},OQ=(0,p.forwardRef)((e,t)=>{var r,n,i=nP(),a=nj(),o=n6();if(!iw(i)||!iw(a))return null;var{children:l,otherAttributes:c,title:u,desc:s}=e;return r="number"==typeof c.tabIndex?c.tabIndex:o?0:void 0,n="string"==typeof c.role?c.role:o?"application":void 0,p.createElement(V,OZ({},c,{title:u,desc:s,role:n,tabIndex:r,width:i,height:a,style:OJ,ref:t}),l)}),O0=e=>{var{children:t}=e,r=eB(nb);if(!r)return null;var{width:n,height:i,y:a,x:o}=r;return p.createElement(V,{width:n,height:i,x:o,y:a},t)},O1=(0,p.forwardRef)((e,t)=>{var{children:r}=e,n=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,OG);return nv()?p.createElement(O0,null,r):p.createElement(OQ,OZ({ref:t},n),r)});function O2(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var O5=(0,p.forwardRef)((e,t)=>{var{children:r,className:n,height:i,onClick:a,onContextMenu:o,onDoubleClick:l,onMouseDown:c,onMouseEnter:u,onMouseLeave:s,onMouseMove:f,onMouseUp:h,onTouchEnd:y,onTouchMove:v,onTouchStart:m,style:g,width:b}=e,x=eL(),[O,w]=(0,p.useState)(null),[P,j]=(0,p.useState)(null);!function(){var e,t,r,n,i,a,o,l,c,u,s,f=eL();(0,p.useEffect)(()=>{f(pW())},[f]),e=eB(uy),t=eB(um),r=eL(),n=eB(uv),i=eB(pn),a=nA(),o=nx(),l=eB(e=>e.rootProps.className),(0,p.useEffect)(()=>{if(null==e)return pZ;var l=(l,c,u)=>{if(t!==u&&e===l){if("index"===n)return void r(c);if(null!=i){if("function"==typeof n){var s,f=n(i,{activeTooltipIndex:null==c.payload.index?void 0:Number(c.payload.index),isTooltipActive:c.payload.active,activeIndex:null==c.payload.index?void 0:Number(c.payload.index),activeLabel:c.payload.label,activeDataKey:c.payload.dataKey,activeCoordinate:c.payload.coordinate});s=i[f]}else"value"===n&&(s=i.find(e=>String(e.value)===c.payload.label));var{coordinate:p}=c.payload;if(null==s||!1===c.payload.active||null==p||null==o)return void r(fw({active:!1,coordinate:void 0,dataKey:void 0,index:null,label:void 0}));var{x:d,y:h}=p,y=Math.min(d,o.x+o.width),v=Math.min(h,o.y+o.height),m={x:"horizontal"===a?s.coordinate:y,y:"horizontal"===a?v:s.coordinate};r(fw({active:c.payload.active,coordinate:m,dataKey:c.payload.dataKey,index:String(s.index),label:c.payload.label}))}}};return pK.on(pz,l),()=>{pK.off(pz,l)}},[l,r,t,e,n,i,a,o]),c=eB(uy),u=eB(um),s=eL(),(0,p.useEffect)(()=>{if(null==c)return pZ;var e=(e,t,r)=>{u!==r&&c===e&&s(pq(t))};return pK.on(pB,e),()=>{pK.off(pB,e)}},[s,u,c])}();var E=function(){var e=eL(),[t,r]=(0,p.useState)(null),n=eB(nn);return(0,p.useEffect)(()=>{if(null!=t){var r=t.getBoundingClientRect().width/t.offsetWidth;iO(r)&&r!==n&&e(rI(r))}},[t,e,n]),r}(),S=(0,p.useCallback)(e=>{E(e),"function"==typeof t&&t(e),w(e),j(e)},[E,t,w,j]),A=(0,p.useCallback)(e=>{x(Oj(e)),x(Oz({handler:a,reactEvent:e}))},[x,a]),k=(0,p.useCallback)(e=>{x(OS(e)),x(Oz({handler:u,reactEvent:e}))},[x,u]),M=(0,p.useCallback)(e=>{x(fg()),x(Oz({handler:s,reactEvent:e}))},[x,s]),T=(0,p.useCallback)(e=>{x(OS(e)),x(Oz({handler:f,reactEvent:e}))},[x,f]),C=(0,p.useCallback)(()=>{x(OR())},[x]),D=(0,p.useCallback)(e=>{x(OL(e.key))},[x]),N=(0,p.useCallback)(e=>{x(Oz({handler:o,reactEvent:e}))},[x,o]),_=(0,p.useCallback)(e=>{x(Oz({handler:l,reactEvent:e}))},[x,l]),I=(0,p.useCallback)(e=>{x(Oz({handler:c,reactEvent:e}))},[x,c]),L=(0,p.useCallback)(e=>{x(Oz({handler:h,reactEvent:e}))},[x,h]),R=(0,p.useCallback)(e=>{x(Oz({handler:m,reactEvent:e}))},[x,m]),K=(0,p.useCallback)(e=>{x(O$(e)),x(Oz({handler:v,reactEvent:e}))},[x,v]),z=(0,p.useCallback)(e=>{x(Oz({handler:y,reactEvent:e}))},[x,y]);return p.createElement(pR.Provider,{value:O},p.createElement(G.Provider,{value:P},p.createElement("div",{className:d("recharts-wrapper",n),style:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?O2(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):O2(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({position:"relative",cursor:"default",width:b,height:i},g),onClick:A,onContextMenu:N,onDoubleClick:_,onFocus:C,onKeyDown:D,onMouseDown:I,onMouseEnter:k,onMouseLeave:M,onMouseMove:T,onMouseUp:L,onTouchEnd:z,onTouchMove:K,onTouchStart:R,ref:S},r)))}),O3=["children","className","width","height","style","compact","title","desc"],O4=(0,p.forwardRef)((e,t)=>{var{children:r,className:n,width:i,height:a,style:o,compact:l,title:c,desc:u}=e,s=U(function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,O3),!1);return l?p.createElement(O1,{otherAttributes:s,title:c,desc:u},r):p.createElement(O5,{className:n,style:o,width:i,height:a,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},p.createElement(O1,{otherAttributes:s,title:c,desc:u,ref:t},p.createElement(g$,null,r)))}),O6=["width","height"];function O8(){return(O8=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var O7={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},O9=(0,p.forwardRef)(function(e,t){var r,n=iL(e.categoricalChartProps,O7),{width:i,height:a}=n,o=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(n,O6);if(!iw(i)||!iw(a))return null;var{chartName:l,defaultTooltipEventType:c,validateTooltipEventTypes:u,tooltipPayloadSearcher:s,categoricalChartProps:f}=e;return p.createElement(OH,{preloadedState:{options:{chartName:l,defaultTooltipEventType:c,validateTooltipEventTypes:u,tooltipPayloadSearcher:s,eventEmitter:void 0}},reduxStoreName:null!=(r=f.id)?r:l},p.createElement(m9,{chartData:f.data}),p.createElement(Oq,{width:i,height:a,layout:n.layout,margin:n.margin}),p.createElement(OY,{accessibilityLayer:n.accessibilityLayer,barCategoryGap:n.barCategoryGap,maxBarSize:n.maxBarSize,stackOffset:n.stackOffset,barGap:n.barGap,barSize:n.barSize,syncId:n.syncId,syncMethod:n.syncMethod,className:n.className}),p.createElement(O4,O8({},o,{width:i,height:a,ref:t})))}),we=["axis"],wt=(0,p.forwardRef)((e,t)=>p.createElement(O9,{chartName:"LineChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:we,tooltipPayloadSearcher:pF,categoricalChartProps:e,ref:t})),wr=["axis","item"],wn=(0,p.forwardRef)((e,t)=>p.createElement(O9,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:wr,tooltipPayloadSearcher:pF,categoricalChartProps:e,ref:t}));function wi(e){var t=eL();return(0,p.useEffect)(()=>{t(O_(e))},[t,e]),null}var wa=["width","height","layout"];function wo(){return(wo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var wl={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},wc=(0,p.forwardRef)(function(e,t){var r,n=iL(e.categoricalChartProps,wl),{width:i,height:a,layout:o}=n,l=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(n,wa);if(!iw(i)||!iw(a))return null;var{chartName:c,defaultTooltipEventType:u,validateTooltipEventTypes:s,tooltipPayloadSearcher:f}=e;return p.createElement(OH,{preloadedState:{options:{chartName:c,defaultTooltipEventType:u,validateTooltipEventTypes:s,tooltipPayloadSearcher:f,eventEmitter:void 0}},reduxStoreName:null!=(r=n.id)?r:c},p.createElement(m9,{chartData:n.data}),p.createElement(Oq,{width:i,height:a,layout:o,margin:n.margin}),p.createElement(OY,{accessibilityLayer:n.accessibilityLayer,barCategoryGap:n.barCategoryGap,maxBarSize:n.maxBarSize,stackOffset:n.stackOffset,barGap:n.barGap,barSize:n.barSize,syncId:n.syncId,syncMethod:n.syncMethod,className:n.className}),p.createElement(wi,{cx:n.cx,cy:n.cy,startAngle:n.startAngle,endAngle:n.endAngle,innerRadius:n.innerRadius,outerRadius:n.outerRadius}),p.createElement(O4,wo({width:i,height:a},l,{ref:t})))}),wu=["item"],ws={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},wf=(0,p.forwardRef)((e,t)=>{var r=iL(e,ws);return p.createElement(wc,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:wu,tooltipPayloadSearcher:pF,categoricalChartProps:r,ref:t})}),wp=r(20697),wd=r.n(wp),wh=["width","height","className","style","children","type"];function wy(){return(wy=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function wv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function wm(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?wv(Object(r),!0).forEach(function(t){wg(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):wv(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function wg(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var wb="value",wx=(e,t)=>y()(e,t),wO=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return"".concat(t,"children[").concat(e,"]")},ww={chartName:"Treemap",defaultTooltipEventType:"item",validateTooltipEventTypes:["item"],tooltipPayloadSearcher:wx,eventEmitter:void 0},wP=e=>{var t,{depth:r,node:n,index:i,dataKey:a,nameKey:o,nestedActiveTooltipIndex:l}=e,c=0===r?"":wO(i,l),{children:u}=n,s=r+1,f=u&&u.length?u.map((e,t)=>wP({depth:s,node:e,index:t,dataKey:a,nameKey:o,nestedActiveTooltipIndex:c})):null;return t=u&&u.length?f.reduce((e,t)=>e+t[wb],0):g(n[a])||n[a]<=0?0:n[a],wm(wm({},n),{},{children:f,name:rG(n,o,""),[wb]:t,depth:r,index:i,tooltipIndex:c})},wj=(e,t,r)=>{var n=t*t,i=e.area*e.area,{min:a,max:o}=e.reduce((e,t)=>({min:Math.min(e.min,t.area),max:Math.max(e.max,t.area)}),{min:1/0,max:0});return i?Math.max(n*o*r/i,i/(n*a*r)):1/0},wE=(e,t,r,n)=>t===r.width?((e,t,r,n)=>{var i,a=t?Math.round(e.area/t):0;(n||a>r.height)&&(a=r.height);for(var o=r.x,l=0,c=e.length;l<c;l++)(i=e[l]).x=o,i.y=r.y,i.height=a,i.width=Math.min(a?Math.round(i.area/a):0,r.x+r.width-o),o+=i.width;return i.width+=r.x+r.width-o,wm(wm({},r),{},{y:r.y+a,height:r.height-a})})(e,t,r,n):((e,t,r,n)=>{var i,a=t?Math.round(e.area/t):0;(n||a>r.width)&&(a=r.width);for(var o=r.y,l=0,c=e.length;l<c;l++)(i=e[l]).x=r.x,i.y=o,i.width=a,i.height=Math.min(a?Math.round(i.area/a):0,r.y+r.height-o),o+=i.height;return i&&(i.height+=r.y+r.height-o),wm(wm({},r),{},{x:r.x+a,width:r.width-a})})(e,t,r,n),wS=(e,t)=>{var{children:r}=e;if(r&&r.length){var n,i,a=(e=>({x:e.x,y:e.y,width:e.width,height:e.height}))(e),o=[],l=1/0,c=Math.min(a.width,a.height),u=((e,t)=>{var r=t<0?0:t;return e.map(e=>{var t=e[wb]*r;return wm(wm({},e),{},{area:g(t)||t<=0?0:t})})})(r,a.width*a.height/e[wb]),s=u.slice();for(o.area=0;s.length>0;)o.push(n=s[0]),o.area+=n.area,(i=wj(o,c,t))<=l?(s.shift(),l=i):(o.area-=o.pop().area,c=Math.min((a=wE(o,c,a,!1)).width,a.height),o.length=o.area=0,l=1/0);return o.length&&(a=wE(o,c,a,!0),o.length=o.area=0),wm(wm({},e),{},{children:u.map(e=>wS(e,t))})}return e},wA={isAnimationFinished:!1,formatRoot:null,currentRoot:null,nestIndex:[]};function wk(e){var{content:t,nodeProps:r,type:n,colorPanel:i,onMouseEnter:a,onMouseLeave:o,onClick:l}=e;if(p.isValidElement(t))return p.createElement(q,{onMouseEnter:a,onMouseLeave:o,onClick:l},p.cloneElement(t,r));if("function"==typeof t)return p.createElement(q,{onMouseEnter:a,onMouseLeave:o,onClick:l},t(r));var{x:c,y:u,width:s,height:f,index:d}=r,h=null;s>10&&f>10&&r.children&&"nest"===n&&(h=p.createElement(dQ,{points:[{x:c+2,y:u+f/2},{x:c+6,y:u+f/2+3},{x:c+2,y:u+f/2+6}]}));var y=null,v=dl(r.name);s>20&&f>20&&v.width<s&&v.height<f&&(y=p.createElement("text",{x:c+8,y:u+f/2+7,fontSize:14},r.name));var m=i||nl;return p.createElement("g",null,p.createElement(ae,wy({fill:r.depth<2?m[d%m.length]:"rgba(255,255,255,0)",stroke:"#fff"},wd()(r,["children"]),{onMouseEnter:a,onMouseLeave:o,onClick:l,"data-recharts-item-index":r.tooltipIndex})),h,y)}function wM(e){var t=eL(),r=e.nodeProps?{x:e.nodeProps.x+e.nodeProps.width/2,y:e.nodeProps.y+e.nodeProps.height/2}:null;return p.createElement(wk,wy({},e,{onMouseEnter:()=>{t(fv({activeIndex:e.nodeProps.tooltipIndex,activeDataKey:e.dataKey,activeCoordinate:r}))},onMouseLeave:()=>{},onClick:()=>{t(fb({activeIndex:e.nodeProps.tooltipIndex,activeDataKey:e.dataKey,activeCoordinate:r}))}}))}function wT(e){var{props:t,currentRoot:r}=e,{dataKey:n,nameKey:i,stroke:a,fill:o}=t;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:a,strokeWidth:void 0,fill:o,dataKey:n,nameKey:i,name:void 0,hide:!1,type:void 0,color:o,unit:""}}}var wC={top:0,right:0,bottom:0,left:0};class wD extends p.PureComponent{static getDerivedStateFromProps(e,t){if(e.data!==t.prevData||e.type!==t.prevType||e.width!==t.prevWidth||e.height!==t.prevHeight||e.dataKey!==t.prevDataKey||e.aspectRatio!==t.prevAspectRatio){var r=wP({depth:0,node:{children:e.data,x:0,y:0,width:e.width,height:e.height},index:0,dataKey:e.dataKey,nameKey:e.nameKey}),n=wS(r,e.aspectRatio);return wm(wm({},t),{},{formatRoot:n,currentRoot:r,nestIndex:[r],prevAspectRatio:e.aspectRatio,prevData:e.data,prevWidth:e.width,prevHeight:e.height,prevDataKey:e.dataKey,prevType:e.type})}return null}handleMouseEnter(e,t){t.persist();var{onMouseEnter:r}=this.props;r&&r(e,t)}handleMouseLeave(e,t){t.persist();var{onMouseLeave:r}=this.props;r&&r(e,t)}handleClick(e){var{onClick:t,type:r}=this.props;if("nest"===r&&e.children){var{width:n,height:i,dataKey:a,nameKey:o,aspectRatio:l}=this.props,c=wP({depth:0,node:wm(wm({},e),{},{x:0,y:0,width:n,height:i}),index:0,dataKey:a,nameKey:o,nestedActiveTooltipIndex:e.tooltipIndex}),u=wS(c,l),{nestIndex:s}=this.state;s.push(e),this.setState({formatRoot:u,currentRoot:c,nestIndex:s})}t&&t(e)}handleNestIndex(e,t){var{nestIndex:r}=this.state,{width:n,height:i,dataKey:a,nameKey:o,aspectRatio:l}=this.props,c=wS(wP({depth:0,node:wm(wm({},e),{},{x:0,y:0,width:n,height:i}),index:0,dataKey:a,nameKey:o,nestedActiveTooltipIndex:e.tooltipIndex}),l);r=r.slice(0,t+1),this.setState({formatRoot:c,currentRoot:e,nestIndex:r})}renderItem(e,t,r){var{isAnimationActive:n,animationBegin:i,animationDuration:a,animationEasing:o,isUpdateAnimationActive:l,type:c,animationId:u,colorPanel:s,dataKey:f}=this.props,{isAnimationFinished:d}=this.state,{width:h,height:y,x:v,y:m,depth:g}=t,b=parseInt("".concat((2*Math.random()-1)*h),10),x={};return((r||"nest"===c)&&(x={onMouseEnter:this.handleMouseEnter.bind(this,t),onMouseLeave:this.handleMouseLeave.bind(this,t),onClick:this.handleClick.bind(this,t)}),n)?p.createElement(i6,{begin:i,duration:a,isActive:n,easing:o,key:"treemap-".concat(u),from:{x:v,y:m,width:h,height:y},to:{x:v,y:m,width:h,height:y},onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},r=>{var{x:u,y:h,width:y,height:v}=r;return p.createElement(i6,{from:"translate(".concat(b,"px, ").concat(b,"px)"),to:"translate(0, 0)",attributeName:"transform",begin:i,easing:o,isActive:n,duration:a},p.createElement(q,x,g>2&&!d?null:p.createElement(wM,{content:e,dataKey:f,nodeProps:wm(wm({},t),{},{isAnimationActive:n,isUpdateAnimationActive:!l,width:y,height:v,x:u,y:h}),type:c,colorPanel:s})))}):p.createElement(q,x,p.createElement(wM,{content:e,dataKey:f,nodeProps:wm(wm({},t),{},{isAnimationActive:!1,isUpdateAnimationActive:!1,width:h,height:y,x:v,y:m}),type:c,colorPanel:s}))}renderNode(e,t){var{content:r,type:n}=this.props,i=wm(wm(wm({},U(this.props,!1)),t),{},{root:e}),a=!t.children||!t.children.length,{currentRoot:o}=this.state;return!(o.children||[]).filter(e=>e.depth===t.depth&&e.name===t.name).length&&e.depth&&"nest"===n?null:p.createElement(q,{key:"recharts-treemap-node-".concat(i.x,"-").concat(i.y,"-").concat(i.name),className:"recharts-treemap-depth-".concat(t.depth)},this.renderItem(r,i,a),t.children&&t.children.length?t.children.map(e=>this.renderNode(t,e)):null)}renderAllNodes(){var{formatRoot:e}=this.state;return e?this.renderNode(e,e):null}renderNestIndex(){var{nameKey:e,nestIndexContent:t}=this.props,{nestIndex:r}=this.state;return p.createElement("div",{className:"recharts-treemap-nest-index-wrapper",style:{marginTop:"8px",textAlign:"center"}},r.map((r,n)=>{var i=y()(r,e,"root"),a=null;return p.isValidElement(t)&&(a=p.cloneElement(t,r,n)),a="function"==typeof t?t(r,n):i,p.createElement("div",{onClick:this.handleNestIndex.bind(this,r,n),key:"nest-index-".concat(P()),className:"recharts-treemap-nest-index-box",style:{cursor:"pointer",display:"inline-block",padding:"0 7px",background:"#000",color:"#fff",marginRight:"3px"}},a)}))}render(){var e=this.props,{width:t,height:r,className:n,style:i,children:a,type:o}=e,l=U(function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,wh),!1);return p.createElement(pR.Provider,{value:this.state.tooltipPortal},p.createElement(yM,{fn:wT,args:{props:this.props,currentRoot:this.state.currentRoot}}),p.createElement(O5,{className:n,style:i,width:t,height:r,ref:e=>{null==this.state.tooltipPortal&&this.setState({tooltipPortal:e})},onMouseEnter:void 0,onMouseLeave:void 0,onClick:void 0,onMouseMove:void 0,onMouseDown:void 0,onMouseUp:void 0,onContextMenu:void 0,onDoubleClick:void 0,onTouchStart:void 0,onTouchMove:this.handleTouchMove,onTouchEnd:void 0},p.createElement(V,wy({},l,{width:t,height:"nest"===o?r-30:r}),this.renderAllNodes(),a),"nest"===o&&this.renderNestIndex()))}constructor(){super(...arguments),wg(this,"state",wm({},wA)),wg(this,"handleAnimationEnd",()=>{var{onAnimationEnd:e}=this.props;this.setState({isAnimationFinished:!0}),"function"==typeof e&&e()}),wg(this,"handleAnimationStart",()=>{var{onAnimationStart:e}=this.props;this.setState({isAnimationFinished:!1}),"function"==typeof e&&e()}),wg(this,"handleTouchMove",(e,t)=>{var r=t.touches[0],n=document.elementFromPoint(r.clientX,r.clientY);if(n&&n.getAttribute){var i=n.getAttribute("data-recharts-item-index"),a=wx(this.state.formatRoot,i);if(a){var{dataKey:o,dispatch:l}=this.props;l(fv({activeIndex:i,activeDataKey:o,activeCoordinate:{x:a.x+a.width/2,y:a.y+a.height/2}}))}}})}}function wN(e){var t=eL();return p.createElement(wD,wy({},e,{dispatch:t}))}function w_(e){var t,{width:r,height:n}=e;return iw(r)&&iw(n)?p.createElement(OH,{preloadedState:{options:ww},reduxStoreName:null!=(t=e.className)?t:"Treemap"},p.createElement(nk,{width:r,height:n}),p.createElement(nM,{margin:wC}),p.createElement(wN,e)):null}wg(wD,"displayName","Treemap"),wg(wD,"defaultProps",{aspectRatio:.5*(1+Math.sqrt(5)),dataKey:"value",nameKey:"name",type:"flat",isAnimationActive:!n4.isSsr,isUpdateAnimationActive:!n4.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"linear"});var wI=r(81682),wL=r.n(wI),wR=["sourceX","sourceY","sourceControlX","targetX","targetY","targetControlX","linkWidth"],wK=["width","height","className","style","children"];function wz(){return(wz=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function wB(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function wF(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function wU(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?wF(Object(r),!0).forEach(function(t){w$(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):wF(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function w$(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var wW=e=>e.y+e.dy/2,wV=e=>e&&e.value||0,wX=(e,t)=>t.reduce((t,r)=>t+wV(e[r]),0),wH=(e,t,r)=>r.reduce((r,n)=>r+wW(e[t[n].source])*wV(t[n]),0),wq=(e,t,r)=>r.reduce((r,n)=>r+wW(e[t[n].target])*wV(t[n]),0),wY=(e,t)=>e.y-t.y,wG=(e,t)=>{for(var{targetNodes:r}=t,n=0,i=r.length;n<i;n++){var a=e[r[n]];a&&(a.depth=Math.max(t.depth+1,a.depth),wG(e,a))}},wZ=function(e,t,r){for(var n=!(arguments.length>3)||void 0===arguments[3]||arguments[3],i=0,a=e.length;i<a;i++){var o=e[i],l=o.length;n&&o.sort(wY);for(var c=0,u=0;u<l;u++){var s=o[u],f=c-s.y;f>0&&(s.y+=f),c=s.y+s.dy+r}c=t+r;for(var p=l-1;p>=0;p--){var d=o[p],h=d.y+d.dy+r-c;if(h>0)d.y-=h,c=d.y;else break}}},wJ=(e,t,r,n)=>{for(var i=0,a=t.length;i<a;i++)for(var o=t[i],l=0,c=o.length;l<c;l++){var u=o[l];if(u.sourceLinks.length){var s=wX(r,u.sourceLinks),f=wH(e,r,u.sourceLinks)/s;u.y+=(f-wW(u))*n}}},wQ=(e,t,r,n)=>{for(var i=t.length-1;i>=0;i--)for(var a=t[i],o=0,l=a.length;o<l;o++){var c=a[o];if(c.targetLinks.length){var u=wX(r,c.targetLinks),s=wq(e,r,c.targetLinks)/u;c.y+=(s-wW(c))*n}}},w0=(e,t)=>"node"===t?{x:+e.x+e.width/2,y:+e.y+e.height/2}:"sourceX"in e&&{x:(e.sourceX+e.targetX)/2,y:(e.sourceY+e.targetY)/2},w1={chartName:"Sankey",defaultTooltipEventType:"item",validateTooltipEventTypes:["item"],tooltipPayloadSearcher:(e,t,r,n)=>{if(null!=t&&"string"==typeof t){var[i,a]=t.split("-"),o=y()(r,"".concat(i,"s[").concat(a,"]"));if(o)return((e,t,r)=>{var{payload:n}=e;if("node"===t)return{payload:n,name:rG(n,r,""),value:rG(n,"value")};if("source"in n&&n.source&&n.target){var i=rG(n.source,r,""),a=rG(n.target,r,"");return{payload:n,name:"".concat(i," - ").concat(a),value:rG(n,"value")}}return null})(o,i,n)}},eventEmitter:void 0};function w2(e){var{dataKey:t,nameKey:r,stroke:n,strokeWidth:i,fill:a,name:o,data:l}=e;return{dataDefinedOnItem:l,positions:void 0,settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,name:o,nameKey:r,color:a,unit:""}}}var w5={top:0,right:0,bottom:0,left:0};function w3(e){var{props:t,i:r,linkContent:n,onMouseEnter:i,onMouseLeave:a,onClick:o,dataKey:l}=e,c=w0(t,"link"),u="link-".concat(r),s=eL();return p.createElement(q,{onMouseEnter:e=>{s(fv({activeIndex:u,activeDataKey:l,activeCoordinate:c})),i(t,e)},onMouseLeave:e=>{s(fm()),a(t,e)},onClick:e=>{s(fb({activeIndex:u,activeDataKey:l,activeCoordinate:c})),o(t,e)}},function(e,t){if(p.isValidElement(e))return p.cloneElement(e,t);if("function"==typeof e)return e(t);var{sourceX:r,sourceY:n,sourceControlX:i,targetX:a,targetY:o,targetControlX:l,linkWidth:c}=t,u=wB(t,wR);return p.createElement("path",wz({className:"recharts-sankey-link",d:"\n          M".concat(r,",").concat(n,"\n          C").concat(i,",").concat(n," ").concat(l,",").concat(o," ").concat(a,",").concat(o,"\n        "),fill:"none",stroke:"#333",strokeWidth:c,strokeOpacity:"0.2"},U(u,!1)))}(n,t))}function w4(e){var{modifiedLinks:t,links:r,linkContent:n,onMouseEnter:i,onMouseLeave:a,onClick:o,dataKey:l}=e;return p.createElement(q,{className:"recharts-sankey-links",key:"recharts-sankey-links"},r.map((e,r)=>{var c=t[r];return p.createElement(w3,{key:"link-".concat(e.source,"-").concat(e.target,"-").concat(e.value),props:c,linkContent:n,i:r,onMouseEnter:i,onMouseLeave:a,onClick:o,dataKey:l})}))}function w6(e){var{props:t,nodeContent:r,i:n,onMouseEnter:i,onMouseLeave:a,onClick:o,dataKey:l}=e,c=eL(),u=w0(t,"node"),s="node-".concat(n);return p.createElement(q,{onMouseEnter:e=>{c(fv({activeIndex:s,activeDataKey:l,activeCoordinate:u})),i(t,e)},onMouseLeave:e=>{c(fm()),a(t,e)},onClick:e=>{c(fb({activeIndex:s,activeDataKey:l,activeCoordinate:u})),o(t,e)}},p.isValidElement(r)?p.cloneElement(r,t):"function"==typeof r?r(t):p.createElement(ae,wz({className:"recharts-sankey-node",fill:"#0088fe",fillOpacity:"0.8"},U(t,!1))))}function w8(e){var{modifiedNodes:t,nodeContent:r,onMouseEnter:n,onMouseLeave:i,onClick:a,dataKey:o}=e;return p.createElement(q,{className:"recharts-sankey-nodes",key:"recharts-sankey-nodes"},t.map((e,t)=>p.createElement(w6,{props:e,nodeContent:r,i:t,onMouseEnter:n,onMouseLeave:i,onClick:a,dataKey:o})))}class w7 extends p.PureComponent{static getDerivedStateFromProps(e,t){var{data:r,width:n,height:i,margin:a,iterations:o,nodeWidth:l,nodePadding:c,sort:u,linkCurvature:s}=e;if(r!==t.prevData||n!==t.prevWidth||i!==t.prevHeight||!ba(a,t.prevMargin)||o!==t.prevIterations||l!==t.prevNodeWidth||c!==t.prevNodePadding||u!==t.sort){var{links:f,nodes:p}=(e=>{var{data:t,width:r,height:n,iterations:i,nodeWidth:a,nodePadding:o,sort:l}=e,{links:c}=t,{tree:u}=((e,t,r)=>{for(var{nodes:n,links:i}=e,a=n.map((e,t)=>{var r=((e,t)=>{for(var r=[],n=[],i=[],a=[],o=0,l=e.length;o<l;o++){var c=e[o];c.source===t&&(i.push(c.target),a.push(o)),c.target===t&&(r.push(c.source),n.push(o))}return{sourceNodes:r,sourceLinks:n,targetLinks:a,targetNodes:i}})(i,t);return wU(wU(wU({},e),r),{},{value:Math.max(wX(i,r.sourceLinks),wX(i,r.targetLinks)),depth:0})}),o=0,l=a.length;o<l;o++){var c=a[o];c.sourceNodes.length||wG(a,c)}var u=hw()(a,e=>e.depth).depth;if(u>=1)for(var s=(t-r)/u,f=0,p=a.length;f<p;f++){var d=a[f];d.targetNodes.length||(d.depth=u),d.x=d.depth*s,d.dx=r}return{tree:a,maxDepth:u}})(t,r,a),s=(e=>{for(var t=[],r=0,n=e.length;r<n;r++){var i=e[r];t[i.depth]||(t[i.depth]=[]),t[i.depth].push(i)}return t})(u),f=((e,t,r,n)=>{for(var i=Math.min(...e.map(e=>(t-(e.length-1)*r)/wL()(e,wV))),a=0,o=e.length;a<o;a++)for(var l=0,c=e[a].length;l<c;l++){var u=e[a][l];u.y=l,u.dy=u.value*i}return n.map(e=>wU(wU({},e),{},{dy:wV(e)*i}))})(s,n,o,c);wZ(s,n,o,l);for(var p=1,d=1;d<=i;d++)wQ(u,s,f,p*=.99),wZ(s,n,o,l),wJ(u,s,f,p),wZ(s,n,o,l);return((e,t)=>{for(var r=0,n=e.length;r<n;r++){var i=e[r],a=0,o=0;i.targetLinks.sort((r,n)=>e[t[r].target].y-e[t[n].target].y),i.sourceLinks.sort((r,n)=>e[t[r].source].y-e[t[n].source].y);for(var l=0,c=i.targetLinks.length;l<c;l++){var u=t[i.targetLinks[l]];u&&(u.sy=a,a+=u.dy)}for(var s=0,f=i.sourceLinks.length;s<f;s++){var p=t[i.sourceLinks[s]];p&&(p.ty=o,o+=p.dy)}}})(u,f),{nodes:u,links:f}})({data:r,width:n-(a&&a.left||0)-(a&&a.right||0),height:i-(a&&a.top||0)-(a&&a.bottom||0),iterations:o,nodeWidth:l,nodePadding:c,sort:u}),d=y()(a,"top")||0,h=y()(a,"left")||0,v=f.map((t,r)=>(e=>{var t,r,{link:n,nodes:i,left:a,top:o,i:l,linkContent:c,linkCurvature:u}=e,{sy:s,ty:f,dy:p}=n,d=i[n.source],h=i[n.target],y=d.x+d.dx+a,v=h.x+a,m=(r=v-(t=+y),e=>t+r*e),g=m(u),b=m(1-u);return wU({sourceX:y,targetX:v,sourceY:d.y+s+p/2+o,targetY:h.y+f+p/2+o,sourceControlX:g,targetControlX:b,sourceRelativeY:s,targetRelativeY:f,linkWidth:p,index:l,payload:wU(wU({},n),{},{source:d,target:h})},U(c,!1))})({link:t,nodes:p,i:r,top:d,left:h,linkContent:e.link,linkCurvature:s})),m=p.map((t,r)=>(e=>{var{node:t,nodeContent:r,top:n,left:i,i:a}=e,{x:o,y:l,dx:c,dy:u}=t;return wU(wU({},U(r,!1)),{},{x:o+i,y:l+n,width:c,height:u,index:a,payload:t})})({node:t,nodeContent:e.node,i:r,top:d,left:h}));return wU(wU({},t),{},{nodes:p,links:f,modifiedLinks:v,modifiedNodes:m,prevData:r,prevWidth:o,prevHeight:i,prevMargin:a,prevNodePadding:c,prevNodeWidth:l,prevIterations:o,prevSort:u})}return null}handleMouseEnter(e,t,r){var{onMouseEnter:n}=this.props;n&&n(e,t,r)}handleMouseLeave(e,t,r){var{onMouseLeave:n}=this.props;n&&n(e,t,r)}handleClick(e,t,r){var{onClick:n}=this.props;n&&n(e,t,r)}render(){var e=this.props,{width:t,height:r,className:n,style:i,children:a}=e,o=wB(e,wK);if(!iw(t)||!iw(r))return null;var{links:l,modifiedNodes:c,modifiedLinks:u}=this.state,s=U(o,!1);return p.createElement(OH,{preloadedState:{options:w1},reduxStoreName:null!=n?n:"Sankey"},p.createElement(yM,{fn:w2,args:this.props}),p.createElement(ge,{computedData:{links:u,nodes:c}}),p.createElement(nk,{width:t,height:r}),p.createElement(nM,{margin:w5}),p.createElement(pR.Provider,{value:this.state.tooltipPortal},p.createElement(O5,{className:n,style:i,width:t,height:r,ref:e=>{null==this.state.tooltipPortal&&this.setState({tooltipPortal:e})},onMouseEnter:void 0,onMouseLeave:void 0,onClick:void 0,onMouseMove:void 0,onMouseDown:void 0,onMouseUp:void 0,onContextMenu:void 0,onDoubleClick:void 0,onTouchStart:void 0,onTouchMove:void 0,onTouchEnd:void 0},p.createElement(V,wz({},s,{width:t,height:r}),a,p.createElement(w4,{links:l,modifiedLinks:u,linkContent:this.props.link,dataKey:this.props.dataKey,onMouseEnter:(e,t)=>this.handleMouseEnter(e,"link",t),onMouseLeave:(e,t)=>this.handleMouseLeave(e,"link",t),onClick:(e,t)=>this.handleClick(e,"link",t)}),p.createElement(w8,{modifiedNodes:c,nodeContent:this.props.node,dataKey:this.props.dataKey,onMouseEnter:(e,t)=>this.handleMouseEnter(e,"node",t),onMouseLeave:(e,t)=>this.handleMouseLeave(e,"node",t),onClick:(e,t)=>this.handleClick(e,"node",t)})))))}constructor(){super(...arguments),w$(this,"state",{nodes:[],links:[],modifiedLinks:[],modifiedNodes:[]})}}w$(w7,"displayName","Sankey"),w$(w7,"defaultProps",{nameKey:"name",dataKey:"value",nodePadding:10,nodeWidth:10,linkCurvature:.5,iterations:32,margin:{top:5,right:5,bottom:5,left:5},sort:!0});var w9=["axis"],Pe={layout:"centric",startAngle:90,endAngle:-270,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},Pt=(0,p.forwardRef)((e,t)=>{var r=iL(e,Pe);return p.createElement(wc,{chartName:"RadarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:w9,tooltipPayloadSearcher:pF,categoricalChartProps:r,ref:t})}),Pr=["item"],Pn=(0,p.forwardRef)((e,t)=>p.createElement(O9,{chartName:"ScatterChart",defaultTooltipEventType:"item",validateTooltipEventTypes:Pr,tooltipPayloadSearcher:pF,categoricalChartProps:e,ref:t})),Pi=["axis"],Pa=(0,p.forwardRef)((e,t)=>p.createElement(O9,{chartName:"AreaChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:Pi,tooltipPayloadSearcher:pF,categoricalChartProps:e,ref:t})),Po=["axis","item"],Pl={layout:"radial",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},Pc=(0,p.forwardRef)((e,t)=>{var r=iL(e,Pl);return p.createElement(wc,{chartName:"RadialBarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:Po,tooltipPayloadSearcher:pF,categoricalChartProps:r,ref:t})}),Pu=["axis"],Ps=(0,p.forwardRef)((e,t)=>p.createElement(O9,{chartName:"ComposedChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:Pu,tooltipPayloadSearcher:pF,categoricalChartProps:e,ref:t}));function Pf(){return(Pf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function Pp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Pd(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Pp(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Pp(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var Ph={fontWeight:"bold",paintOrder:"stroke fill",fontSize:".75rem",stroke:"#FFF",fill:"black",pointerEvents:"none"};function Py(e){var t,{dataKey:r,nameKey:n,data:i,stroke:a,fill:o,positions:l}=e;return{dataDefinedOnItem:i.children,positions:(t={},l.forEach((e,r)=>{t[r]=e}),t),settings:{stroke:a,strokeWidth:void 0,fill:o,nameKey:n,dataKey:r,name:n?void 0:r,hide:!1,type:void 0,color:o,unit:""}}}var Pv={top:0,right:0,bottom:0,left:0},Pm=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return"".concat(t,"children[").concat(e,"]")},Pg={options:{validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",chartName:"Sunburst",tooltipPayloadSearcher:(e,t)=>y()(e,t),eventEmitter:void 0}},Pb=e=>{var{className:t,data:r,children:n,width:i,height:a,padding:o=2,dataKey:l="value",nameKey:c="name",ringPadding:u=2,innerRadius:s=50,fill:f="#333",stroke:h="#FFF",textOptions:y=Ph,outerRadius:v=Math.min(i,a)/2,cx:m=i/2,cy:g=a/2,startAngle:b=0,endAngle:x=360,onClick:O,onMouseEnter:w,onMouseLeave:P}=e,j=eL(),E=oC([0,r[l]],[0,x]),S=function e(t){return t.children&&0!==t.children.length?1+Math.max(...t.children.map(t=>e(t))):1}(r),A=[],k=new Map([]),[M,T]=(0,p.useState)(null);!function e(t,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,{radius:i,innerR:a,initialAngle:c,childColor:s,nestedActiveTooltipIndex:d}=r,v=c;t&&t.forEach((t,r)=>{var c,b,x=1===n?"[".concat(r,"]"):Pm(r,d),S=Pd(Pd({},t),{},{tooltipIndex:x}),M=E(t[l]),T=v,C=null!=(c=null!=(b=null==t?void 0:t.fill)?b:s)?c:f,{x:D,y:N}=rV(0,0,a+i/2,-(T+M-M/2));v+=M,A.push(p.createElement("g",{key:"sunburst-sector-".concat(t.name,"-").concat(r)},p.createElement(ao,{onClick:()=>{O&&O(S),j(fb({activeIndex:S.tooltipIndex,activeDataKey:l,activeCoordinate:k.get(S.name)}))},onMouseEnter:e=>{w&&w(S,e),j(fv({activeIndex:S.tooltipIndex,activeDataKey:l,activeCoordinate:k.get(S.name)}))},onMouseLeave:e=>{P&&P(S,e),j(fm())},fill:C,stroke:h,strokeWidth:o,startAngle:T,endAngle:T+M,innerRadius:a,outerRadius:a+i,cx:m,cy:g}),p.createElement(dS,Pf({},y,{alignmentBaseline:"middle",textAnchor:"middle",x:D+m,y:g-N}),t[l])));var{x:_,y:I}=rV(m,g,a+i/2,T);return k.set(t.name,{x:_,y:I}),e(t.children,{radius:i,innerR:a+i+u,initialAngle:T,childColor:C,nestedActiveTooltipIndex:x},n+1)})}(r.children,{radius:(v-s)/S,innerR:s,initialAngle:b});var C=d("recharts-sunburst",t);return p.createElement(pR.Provider,{value:M},p.createElement(O5,{className:t,width:i,height:a,ref:e=>{null==M&&null!=e&&T(e)},onMouseEnter:void 0,onMouseLeave:void 0,onClick:void 0,onMouseMove:void 0,onMouseDown:void 0,onMouseUp:void 0,onContextMenu:void 0,onDoubleClick:void 0,onTouchStart:void 0,onTouchMove:void 0,onTouchEnd:void 0},p.createElement(V,{width:i,height:a},p.createElement(q,{className:C},A),p.createElement(yM,{fn:Py,args:{dataKey:l,data:r,stroke:h,fill:f,nameKey:c,positions:k}}),n)))},Px=e=>{var t;return p.createElement(OH,{preloadedState:Pg,reduxStoreName:null!=(t=e.className)?t:"SunburstChart"},p.createElement(nk,{width:e.width,height:e.height}),p.createElement(nM,{margin:Pv}),p.createElement(Pb,e))};function PO(){return(PO=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function Pw(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function PP(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Pw(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Pw(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Pj(e,t){var r=parseInt("".concat(t.x||e.x),10),n=parseInt("".concat(t.y||e.y),10),i=parseInt("".concat((null==t?void 0:t.height)||(null==e?void 0:e.height)),10);return PP(PP(PP({},t),yj(e)),{},{height:i,x:r,y:n})}function PE(e){return p.createElement(yE,PO({shapeType:"trapezoid",propTransformer:Pj},e))}function PS(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function PA(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?PS(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):PS(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var Pk=e3([np,(e,t)=>t,cG],(e,t,r)=>{var n,{data:i,dataKey:a,nameKey:o,tooltipType:l,lastShapeType:c,reversed:u,customWidth:s,cells:f,presentationProps:p}=t,{chartData:d}=r;if(null!=i&&i.length>0?n=i:null!=d&&d.length>0&&(n=d),n&&n.length)n=n.map((e,t)=>PA(PA(PA({payload:e},p),e),f&&f[t]&&f[t].props));else{if(!f||!f.length)return{trapezoids:[],data:n};n=f.map(e=>PA(PA({},p),e.props))}return function(e){var{dataKey:t,nameKey:r,displayedData:n,tooltipType:i,lastShapeType:a,reversed:o,offset:l,customWidth:c}=e,{left:u,top:s}=l,{realHeight:f,realWidth:p,offsetX:d,offsetY:h}=((e,t)=>{var{width:r,height:n,left:i,right:a,top:o,bottom:l}=t,c=r;return x(e)?c=e:"string"==typeof e&&(c=c*parseFloat(e)/100),{realWidth:c-i-a-50,realHeight:n-l-o,offsetX:(r-c)/2,offsetY:(n-n)/2}})(c,l),y=Math.max.apply(null,n.map(e=>rG(e,t,0))),v=n.length,m=f/v,g={x:l.left,y:l.top,width:l.width,height:l.height},b=n.map((e,o)=>{var l,c=rG(e,t,0),f=rG(e,r,o),b=c;o!==v-1?(l=rG(n[o+1],t,0))instanceof Array&&([l]=l):c instanceof Array&&2===c.length?[b,l]=c:l="rectangle"===a?b:0;var x=(y-b)*p/(2*y)+s+25+d,O=m*o+u+h,w=b/y*p,P=l/y*p,j=[{name:f,value:b,payload:e,dataKey:t,type:i}];return PN(PN({x,y:O,width:Math.max(w,P),upperWidth:w,lowerWidth:P,height:m,name:f,val:b,tooltipPayload:j,tooltipPosition:{x:x+w/2,y:O+m/2}},wd()(e,["width"])),{},{payload:e,parentViewBox:g,labelViewBox:{x:x+(w-P)/4,y:O,width:Math.abs(w-P)/2+Math.min(w,P),height:m}})});return o&&(b=b.map((e,t)=>{var r=e.y-t*m+(v-1-t)*m;return PN(PN({},e),{},{upperWidth:e.lowerWidth,lowerWidth:e.upperWidth,x:e.x-(e.lowerWidth-e.upperWidth)/2,y:e.y-t*m+(v-1-t)*m,tooltipPosition:PN(PN({},e.tooltipPosition),{},{y:r+m/2}),labelViewBox:PN(PN({},e.labelViewBox),{},{y:r})})})),{trapezoids:b,data:n}}({dataKey:a,nameKey:o,displayedData:n,tooltipType:l,lastShapeType:c,reversed:u,offset:e,customWidth:s})}),PM=["onMouseEnter","onClick","onMouseLeave","shape","activeShape"],PT=["stroke","fill","legendType","hide","isAnimationActive","animationBegin","animationDuration","animationEasing","nameKey","lastShapeType"];function PC(){return(PC=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function PD(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function PN(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?PD(Object(r),!0).forEach(function(t){P_(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):PD(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function P_(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function PI(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function PL(e){var{dataKey:t,nameKey:r,stroke:n,strokeWidth:i,fill:a,name:o,hide:l,tooltipType:c,data:u}=e;return{dataDefinedOnItem:u,positions:e.trapezoids.map(e=>{var{tooltipPosition:t}=e;return t}),settings:{stroke:n,strokeWidth:i,fill:a,dataKey:t,name:o,nameKey:r,hide:l,type:c,color:a,unit:""}}}function PR(e){var{trapezoids:t,allOtherFunnelProps:r,showLabels:n}=e,i=eB(e=>pE(e,"item",e.tooltip.settings.trigger,void 0)),{onMouseEnter:a,onClick:o,onMouseLeave:l,shape:c,activeShape:u}=r,s=PI(r,PM),f=yS(a,r.dataKey),d=yA(l),h=yk(o,r.dataKey);return p.createElement(p.Fragment,null,t.map((e,t)=>{var r=u&&i===String(t),n=PN(PN({},e),{},{option:r?u:c,isActive:r,stroke:e.stroke});return p.createElement(q,PC({className:"recharts-funnel-trapezoid"},I(s,e,t),{onMouseEnter:f(e,t),onMouseLeave:d(e,t),onClick:h(e,t),key:"trapezoid-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.name,"-").concat(null==e?void 0:e.value)}),p.createElement(PE,n))}),n&&dV.renderCallByParent(r,t))}var PK=0;function Pz(e){var t,r,{previousTrapezoidsRef:n,props:i}=e,{trapezoids:a,isAnimationActive:o,animationBegin:l,animationDuration:c,animationEasing:u,onAnimationEnd:s,onAnimationStart:f}=i,d=n.current,[h,y]=(0,p.useState)(!0),v=(t=(0,p.useRef)(PK),(r=(0,p.useRef)(a)).current!==a&&(t.current+=1,PK=t.current,r.current=a),t.current),m=(0,p.useCallback)(()=>{"function"==typeof s&&s(),y(!1)},[s]),g=(0,p.useCallback)(()=>{"function"==typeof f&&f(),y(!0)},[f]);return p.createElement(i6,{begin:l,duration:c,isActive:o,easing:u,from:{t:0},to:{t:1},key:v,onAnimationStart:g,onAnimationEnd:m},e=>{var{t}=e,r=1===t?a:a.map((e,r)=>{var n=d&&d[r];if(n){var i=S(n.x,e.x),a=S(n.y,e.y),o=S(n.upperWidth,e.upperWidth),l=S(n.lowerWidth,e.lowerWidth),c=S(n.height,e.height);return PN(PN({},e),{},{x:i(t),y:a(t),upperWidth:o(t),lowerWidth:l(t),height:c(t)})}var u=S(e.x+e.upperWidth/2,e.x),s=S(e.y+e.height/2,e.y),f=S(0,e.upperWidth),p=S(0,e.lowerWidth),h=S(0,e.height);return PN(PN({},e),{},{x:u(t),y:s(t),upperWidth:f(t),lowerWidth:p(t),height:h(t)})});return t>0&&(n.current=r),p.createElement(q,null,p.createElement(PR,{trapezoids:r,allOtherFunnelProps:i,showLabels:!h}))})}function PB(e){var{trapezoids:t,isAnimationActive:r}=e,n=(0,p.useRef)(null),i=n.current;return r&&t&&t.length&&(!i||i!==t)?p.createElement(Pz,{props:e,previousTrapezoidsRef:n}):p.createElement(PR,{trapezoids:t,allOtherFunnelProps:e,showLabels:!0})}class PF extends p.PureComponent{render(){var{className:e}=this.props,t=d("recharts-trapezoids",e);return p.createElement(q,{className:t},p.createElement(PB,this.props))}}var PU={stroke:"#fff",fill:"#808080",legendType:"rect",hide:!1,isAnimationActive:!n4.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",lastShapeType:"triangle"};function P$(e){var{height:t,width:r}=yQ(),n=iL(e,PU),{stroke:i,fill:a,legendType:o,hide:l,isAnimationActive:c,animationBegin:u,animationDuration:s,animationEasing:f,nameKey:d,lastShapeType:h}=n,y=PI(n,PT),v=U(e,!1),m=B(e.children,de),g=(0,p.useMemo)(()=>({dataKey:e.dataKey,nameKey:d,data:e.data,tooltipType:e.tooltipType,lastShapeType:h,reversed:e.reversed,customWidth:e.width,cells:m,presentationProps:v}),[e.dataKey,d,e.data,e.tooltipType,h,e.reversed,e.width,m,v]),{trapezoids:b}=eB(e=>Pk(e,g));return p.createElement(p.Fragment,null,p.createElement(yM,{fn:PL,args:PN(PN({},e),{},{trapezoids:b})}),l?null:p.createElement(PF,PC({},y,{stroke:i,fill:a,nameKey:d,lastShapeType:h,animationBegin:u,animationDuration:s,animationEasing:f,isAnimationActive:c,hide:l,legendType:o,height:t,width:r,trapezoids:b})))}class PW extends p.PureComponent{render(){return p.createElement(P$,this.props)}}P_(PW,"displayName","Funnel"),P_(PW,"defaultProps",PU);var PV=["item"],PX=(0,p.forwardRef)((e,t)=>p.createElement(O9,{chartName:"FunnelChart",defaultTooltipEventType:"item",validateTooltipEventTypes:PV,tooltipPayloadSearcher:pF,categoricalChartProps:e,ref:t}))},35695:(e,t,r)=>{"use strict";var n=r(18999);r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}})},36633:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPrimitive=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e}},37298:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(29738);t.cloneDeep=function(e){return n.cloneDeepWith(e)}},39611:(e,t,r)=>{"use strict";r(4993)},40220:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.last=function(e){return e[e.length-1]}},42694:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8287);t.toNumber=function(e){return n.isSymbol(e)?NaN:Number(e)}},42721:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.argumentsTag="[object Arguments]",t.arrayBufferTag="[object ArrayBuffer]",t.arrayTag="[object Array]",t.bigInt64ArrayTag="[object BigInt64Array]",t.bigUint64ArrayTag="[object BigUint64Array]",t.booleanTag="[object Boolean]",t.dataViewTag="[object DataView]",t.dateTag="[object Date]",t.errorTag="[object Error]",t.float32ArrayTag="[object Float32Array]",t.float64ArrayTag="[object Float64Array]",t.functionTag="[object Function]",t.int16ArrayTag="[object Int16Array]",t.int32ArrayTag="[object Int32Array]",t.int8ArrayTag="[object Int8Array]",t.mapTag="[object Map]",t.numberTag="[object Number]",t.objectTag="[object Object]",t.regexpTag="[object RegExp]",t.setTag="[object Set]",t.stringTag="[object String]",t.symbolTag="[object Symbol]",t.uint16ArrayTag="[object Uint16Array]",t.uint32ArrayTag="[object Uint32Array]",t.uint8ArrayTag="[object Uint8Array]",t.uint8ClampedArrayTag="[object Uint8ClampedArray]"},44117:(e,t,r)=>{"use strict";var n=r(44134).Buffer;Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let i=r(98221),a=r(15160),o=r(42721),l=r(36633),c=r(80885);function u(e,t,r,i=new Map,f){let p=f?.(e,t,r,i);if(null!=p)return p;if(l.isPrimitive(e))return e;if(i.has(e))return i.get(e);if(Array.isArray(e)){let t=Array(e.length);i.set(e,t);for(let n=0;n<e.length;n++)t[n]=u(e[n],n,r,i,f);return Object.hasOwn(e,"index")&&(t.index=e.index),Object.hasOwn(e,"input")&&(t.input=e.input),t}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){let t=new RegExp(e.source,e.flags);return t.lastIndex=e.lastIndex,t}if(e instanceof Map){let t=new Map;for(let[n,a]of(i.set(e,t),e))t.set(n,u(a,n,r,i,f));return t}if(e instanceof Set){let t=new Set;for(let n of(i.set(e,t),e))t.add(u(n,void 0,r,i,f));return t}if(void 0!==n&&n.isBuffer(e))return e.subarray();if(c.isTypedArray(e)){let t=new(Object.getPrototypeOf(e)).constructor(e.length);i.set(e,t);for(let n=0;n<e.length;n++)t[n]=u(e[n],n,r,i,f);return t}if(e instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){let t=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return i.set(e,t),s(t,e,r,i,f),t}if("undefined"!=typeof File&&e instanceof File){let t=new File([e],e.name,{type:e.type});return i.set(e,t),s(t,e,r,i,f),t}if(e instanceof Blob){let t=new Blob([e],{type:e.type});return i.set(e,t),s(t,e,r,i,f),t}if(e instanceof Error){let t=new e.constructor;return i.set(e,t),t.message=e.message,t.name=e.name,t.stack=e.stack,t.cause=e.cause,s(t,e,r,i,f),t}if("object"==typeof e&&function(e){switch(a.getTag(e)){case o.argumentsTag:case o.arrayTag:case o.arrayBufferTag:case o.dataViewTag:case o.booleanTag:case o.dateTag:case o.float32ArrayTag:case o.float64ArrayTag:case o.int8ArrayTag:case o.int16ArrayTag:case o.int32ArrayTag:case o.mapTag:case o.numberTag:case o.objectTag:case o.regexpTag:case o.setTag:case o.stringTag:case o.symbolTag:case o.uint8ArrayTag:case o.uint8ClampedArrayTag:case o.uint16ArrayTag:case o.uint32ArrayTag:return!0;default:return!1}}(e)){let t=Object.create(Object.getPrototypeOf(e));return i.set(e,t),s(t,e,r,i,f),t}return e}function s(e,t,r=e,n,a){let o=[...Object.keys(t),...i.getSymbols(t)];for(let i=0;i<o.length;i++){let l=o[i],c=Object.getOwnPropertyDescriptor(e,l);(null==c||c.writable)&&(e[l]=u(t[l],l,r,n,a))}}t.cloneDeepWith=function(e,t){return u(e,void 0,e,new Map,t)},t.cloneDeepWithImpl=u,t.copyProperties=s},45643:(e,t,r)=>{"use strict";e.exports=r(6115)},46200:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toKey=function(e){return"string"==typeof e||"symbol"==typeof e?e:Object.is(e?.valueOf?.(),-0)?"-0":String(e)}},47064:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(55181),i=r(51551),a=r(64072);t.orderBy=function(e,t,r,o){if(null==e)return[];r=o?void 0:r,Array.isArray(e)||(e=Object.values(e)),Array.isArray(t)||(t=null==t?[null]:[t]),0===t.length&&(t=[null]),Array.isArray(r)||(r=null==r?[]:[r]),r=r.map(e=>String(e));let l=(e,t)=>{let r=e;for(let e=0;e<t.length&&null!=r;++e)r=r[t[e]];return r},c=t.map(e=>(Array.isArray(e)&&1===e.length&&(e=e[0]),null==e||"function"==typeof e||Array.isArray(e)||i.isKey(e))?e:{key:e,path:a.toPath(e)});return e.map(e=>({original:e,criteria:c.map(t=>{var r,n;return r=t,null==(n=e)||null==r?n:"object"==typeof r&&"key"in r?Object.hasOwn(n,r.key)?n[r.key]:l(n,r.path):"function"==typeof r?r(n):Array.isArray(r)?l(n,r):"object"==typeof n?n[r]:n})})).slice().sort((e,t)=>{for(let i=0;i<c.length;i++){let a=n.compareValues(e.criteria[i],t.criteria[i],r[i]);if(0!==a)return a}return 0}).map(e=>e.original)}},49033:(e,t,r)=>{"use strict";e.exports=r(22436)},49901:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(64373),i=r(64664);t.range=function(e,t,r){r&&"number"!=typeof r&&n.isIterateeCall(e,t,r)&&(t=r=void 0),e=i.toFinite(e),void 0===t?(t=e,e=0):t=i.toFinite(t),r=void 0===r?e<t?1:-1:i.toFinite(r);let a=Math.max(Math.ceil((t-e)/(r||1)),0),o=Array(a);for(let t=0;t<a;t++)o[t]=e,e+=r;return o}},50177:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(15160);t.isArguments=function(e){return null!==e&&"object"==typeof e&&"[object Arguments]"===n.getTag(e)}},51551:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8287),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.isKey=function(e,t){return!Array.isArray(e)&&(!!("number"==typeof e||"boolean"==typeof e||null==e||n.isSymbol(e))||"string"==typeof e&&(a.test(e)||!i.test(e))||null!=t&&Object.hasOwn(t,e))}},53588:(e,t)=>{"use strict";var r=Symbol.for("react.transitional.element"),n=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler");Symbol.for("react.provider");var l=Symbol.for("react.consumer"),c=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),s=Symbol.for("react.suspense"),f=Symbol.for("react.suspense_list"),p=Symbol.for("react.memo"),d=Symbol.for("react.lazy"),h=Symbol.for("react.view_transition");Symbol.for("react.client.reference");t.zv=function(e){return function(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case i:case o:case a:case s:case f:case h:return e;default:switch(e=e&&e.$$typeof){case c:case u:case d:case p:case l:return e;default:return t}}case n:return t}}}(e)===i}},55181:(e,t)=>{"use strict";function r(e){return"symbol"==typeof e?1:null===e?2:void 0===e?3:4*(e!=e)}Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.compareValues=(e,t,n)=>{if(e!==t){let i=r(e),a=r(t);if(i===a&&0===i){if(e<t)return"desc"===n?1:-1;if(e>t)return"desc"===n?-1:1}return"desc"===n?a-i:i-a}return 0}},55998:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.flatten=function(e,t=1){let r=[],n=Math.floor(t),i=(e,t)=>{for(let a=0;a<e.length;a++){let o=e[a];Array.isArray(o)&&t<n?i(o,t+1):r.push(o)}};return i(e,0),r}},58080:(e,t,r)=>{e.exports=r(78359).last},60512:(e,t,r)=>{e.exports=r(7547).uniqBy},60760:(e,t,r)=>{"use strict";r.d(t,{N:()=>g});var n=r(95155),i=r(12115),a=r(90869),o=r(82885),l=r(97494),c=r(80845),u=r(27351),s=r(51508);class f extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=(0,u.s)(e)&&e.offsetWidth||0,n=this.props.sizeRef.current;n.height=t.offsetHeight||0,n.width=t.offsetWidth||0,n.top=t.offsetTop,n.left=t.offsetLeft,n.right=r-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function p(e){let{children:t,isPresent:r,anchorX:a,root:o}=e,l=(0,i.useId)(),c=(0,i.useRef)(null),u=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:p}=(0,i.useContext)(s.Q);return(0,i.useInsertionEffect)(()=>{let{width:e,height:t,top:n,left:i,right:s}=u.current;if(r||!c.current||!e||!t)return;c.current.dataset.motionPopId=l;let f=document.createElement("style");p&&(f.nonce=p);let d=null!=o?o:document.head;return d.appendChild(f),f.sheet&&f.sheet.insertRule('\n          [data-motion-pop-id="'.concat(l,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===a?"left: ".concat(i):"right: ".concat(s),"px !important;\n            top: ").concat(n,"px !important;\n          }\n        ")),()=>{d.removeChild(f),d.contains(f)&&d.removeChild(f)}},[r]),(0,n.jsx)(f,{isPresent:r,childRef:c,sizeRef:u,children:i.cloneElement(t,{ref:c})})}let d=e=>{let{children:t,initial:r,isPresent:a,onExitComplete:l,custom:u,presenceAffectsLayout:s,mode:f,anchorX:d,root:y}=e,v=(0,o.M)(h),m=(0,i.useId)(),g=!0,b=(0,i.useMemo)(()=>(g=!1,{id:m,initial:r,isPresent:a,custom:u,onExitComplete:e=>{for(let t of(v.set(e,!0),v.values()))if(!t)return;l&&l()},register:e=>(v.set(e,!1),()=>v.delete(e))}),[a,v,l]);return s&&g&&(b={...b}),(0,i.useMemo)(()=>{v.forEach((e,t)=>v.set(t,!1))},[a]),i.useEffect(()=>{a||v.size||!l||l()},[a]),"popLayout"===f&&(t=(0,n.jsx)(p,{isPresent:a,anchorX:d,root:y,children:t})),(0,n.jsx)(c.t.Provider,{value:b,children:t})};function h(){return new Map}var y=r(32082);let v=e=>e.key||"";function m(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}let g=e=>{let{children:t,custom:r,initial:c=!0,onExitComplete:u,presenceAffectsLayout:s=!0,mode:f="sync",propagate:p=!1,anchorX:h="left",root:g}=e,[b,x]=(0,y.xQ)(p),O=(0,i.useMemo)(()=>m(t),[t]),w=p&&!b?[]:O.map(v),P=(0,i.useRef)(!0),j=(0,i.useRef)(O),E=(0,o.M)(()=>new Map),[S,A]=(0,i.useState)(O),[k,M]=(0,i.useState)(O);(0,l.E)(()=>{P.current=!1,j.current=O;for(let e=0;e<k.length;e++){let t=v(k[e]);w.includes(t)?E.delete(t):!0!==E.get(t)&&E.set(t,!1)}},[k,w.length,w.join("-")]);let T=[];if(O!==S){let e=[...O];for(let t=0;t<k.length;t++){let r=k[t],n=v(r);w.includes(n)||(e.splice(t,0,r),T.push(r))}return"wait"===f&&T.length&&(e=T),M(m(e)),A(O),null}let{forceRender:C}=(0,i.useContext)(a.L);return(0,n.jsx)(n.Fragment,{children:k.map(e=>{let t=v(e),i=(!p||!!b)&&(O===k||w.includes(t));return(0,n.jsx)(d,{isPresent:i,initial:(!P.current||!!c)&&void 0,custom:r,presenceAffectsLayout:s,mode:f,root:g,onExitComplete:i?void 0:()=>{if(!E.has(t))return;E.set(t,!0);let e=!0;E.forEach(t=>{t||(e=!1)}),e&&(null==C||C(),M(j.current),p&&(null==x||x()),u&&u())},anchorX:h,children:e},t)})})}},62194:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(10921);t.property=function(e){return function(t){return n.get(t,e)}}},62513:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.minBy=function(e,t){if(0===e.length)return;let r=e[0],n=t(r);for(let i=1;i<e.length;i++){let a=e[i],o=t(a);o<n&&(n=o,r=a)}return r}},64072:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toPath=function(e){let t=[],r=e.length;if(0===r)return t;let n=0,i="",a="",o=!1;for(46===e.charCodeAt(0)&&(t.push(""),n++);n<r;){let l=e[n];a?"\\"===l&&n+1<r?i+=e[++n]:l===a?a="":i+=l:o?'"'===l||"'"===l?a=l:"]"===l?(o=!1,t.push(i),i=""):i+=l:"["===l?(o=!0,i&&(t.push(i),i="")):"."===l?i&&(t.push(i),i=""):i+=l,n++}return i&&t.push(i),t}},64373:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98412),i=r(68179),a=r(82384),o=r(83616);t.isIterateeCall=function(e,t,r){return!!a.isObject(r)&&(!!("number"==typeof t&&i.isArrayLike(r)&&n.isIndex(t))&&t<r.length||"string"==typeof t&&t in r)&&o.eq(r[t],e)}},64664:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(42694);t.toFinite=function(e){return e?(e=n.toNumber(e))===1/0||e===-1/0?(e<0?-1:1)*Number.MAX_VALUE:e==e?e:0:0===e?e:0}},68179:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(19452);t.isArrayLike=function(e){return null!=e&&"function"!=typeof e&&n.isLength(e.length)}},70027:(e,t,r)=>{e.exports=r(84043).maxBy},72465:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.identity=function(e){return e}},72605:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(62513),i=r(72465),a=r(81571);t.minBy=function(e,t){if(null!=e)return n.minBy(Array.from(e),a.iteratee(t??i.identity))}},72744:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98132),i=r(82384),a=r(36633),o=r(83616);function l(e,t,r,n){if(t===e)return!0;switch(typeof t){case"object":return function(e,t,r,n){if(null==t)return!0;if(Array.isArray(t))return c(e,t,r,n);if(t instanceof Map){var i=e,o=t,l=r,s=n;if(0===o.size)return!0;if(!(i instanceof Map))return!1;for(let[e,t]of o.entries())if(!1===l(i.get(e),t,e,i,o,s))return!1;return!0}if(t instanceof Set)return u(e,t,r,n);let f=Object.keys(t);if(null==e)return 0===f.length;if(0===f.length)return!0;if(n&&n.has(t))return n.get(t)===e;n&&n.set(t,e);try{for(let i=0;i<f.length;i++){let o=f[i];if(!a.isPrimitive(e)&&!(o in e)||void 0===t[o]&&void 0!==e[o]||null===t[o]&&null!==e[o]||!r(e[o],t[o],o,e,t,n))return!1}return!0}finally{n&&n.delete(t)}}(e,t,r,n);case"function":if(Object.keys(t).length>0)return l(e,{...t},r,n);return o.eq(e,t);default:if(!i.isObject(e))return o.eq(e,t);if("string"==typeof t)return""===t;return!0}}function c(e,t,r,n){if(0===t.length)return!0;if(!Array.isArray(e))return!1;let i=new Set;for(let a=0;a<t.length;a++){let o=t[a],l=!1;for(let c=0;c<e.length;c++){if(i.has(c))continue;let u=e[c],s=!1;if(r(u,o,a,e,t,n)&&(s=!0),s){i.add(c),l=!0;break}}if(!l)return!1}return!0}function u(e,t,r,n){return 0===t.size||e instanceof Set&&c([...e],[...t],r,n)}t.isMatchWith=function(e,t,r){return"function"!=typeof r?n.isMatch(e,t):l(e,t,function e(t,n,i,a,o,c){let u=r(t,n,i,a,o,c);return void 0!==u?!!u:l(t,n,e,c)},new Map)},t.isSetMatch=u},73054:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(94456),i=r(12429);t.omit=function(e,...t){if(null==e)return{};let r=i.cloneDeep(e);for(let e=0;e<t.length;e++){let i=t[e];switch(typeof i){case"object":Array.isArray(i)||(i=Array.from(i));for(let e=0;e<i.length;e++){let t=i[e];n.unset(r,t)}break;case"string":case"symbol":case"number":n.unset(r,i)}}return r}},78359:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(40220),i=r(14986),a=r(68179);t.last=function(e){if(a.isArrayLike(e))return n.last(i.toArray(e))}},78673:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.debounce=function(e,t=0,r={}){let n;"object"!=typeof r&&(r={});let i=null,a=null,o=null,l=0,c=null,{leading:u=!1,trailing:s=!0,maxWait:f}=r,p="maxWait"in r,d=p?Math.max(Number(f)||0,t):0,h=t=>(null!==i&&(n=e.apply(a,i)),i=a=null,l=t,n),y=e=>(c=null,s&&null!==i)?h(e):n,v=e=>{if(null===o)return!0;let r=e-o,n=p&&e-l>=d;return r>=t||r<0||n},m=()=>{let e=Date.now();if(v(e))return y(e);c=setTimeout(m,(e=>{let r=t-(null===o?0:e-o),n=d-(e-l);return p?Math.min(r,n):r})(e))},g=function(...e){let r=Date.now(),s=v(r);if(i=e,a=this,o=r,s){if(null===c)return(l=r,c=setTimeout(m,t),u&&null!==i)?h(r):n;if(p)return clearTimeout(c),c=setTimeout(m,t),h(r)}return null===c&&(c=setTimeout(m,t)),n};return g.cancel=()=>{null!==c&&clearTimeout(c),l=0,o=i=a=c=null},g.flush=()=>null===c?n:y(Date.now()),g}},80885:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isTypedArray=function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}},80931:(e,t,r)=>{e.exports=r(86006).isPlainObject},81571:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(72465),i=r(62194),a=r(14804),o=r(24517);t.iteratee=function(e){if(null==e)return n.identity;switch(typeof e){case"function":return e;case"object":if(Array.isArray(e)&&2===e.length)return o.matchesProperty(e[0],e[1]);return a.matches(e);case"string":case"symbol":case"number":return i.property(e)}}},81682:(e,t,r)=>{e.exports=r(6706).sumBy},82384:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObject=function(e){return null!==e&&("object"==typeof e||"function"==typeof e)}},82661:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,n,a,o){if("function"!=typeof n)throw TypeError("The listener must be a function");var l=new i(n,a||e,o),c=r?r+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],l]:e._events[c].push(l):(e._events[c]=l,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function l(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),l.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},l.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,a=n.length,o=Array(a);i<a;i++)o[i]=n[i].fn;return o},l.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},l.prototype.emit=function(e,t,n,i,a,o){var l=r?r+e:e;if(!this._events[l])return!1;var c,u,s=this._events[l],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(e,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,t),!0;case 3:return s.fn.call(s.context,t,n),!0;case 4:return s.fn.call(s.context,t,n,i),!0;case 5:return s.fn.call(s.context,t,n,i,a),!0;case 6:return s.fn.call(s.context,t,n,i,a,o),!0}for(u=1,c=Array(f-1);u<f;u++)c[u-1]=arguments[u];s.fn.apply(s.context,c)}else{var p,d=s.length;for(u=0;u<d;u++)switch(s[u].once&&this.removeListener(e,s[u].fn,void 0,!0),f){case 1:s[u].fn.call(s[u].context);break;case 2:s[u].fn.call(s[u].context,t);break;case 3:s[u].fn.call(s[u].context,t,n);break;case 4:s[u].fn.call(s[u].context,t,n,i);break;default:if(!c)for(p=1,c=Array(f-1);p<f;p++)c[p-1]=arguments[p];s[u].fn.apply(s[u].context,c)}}return!0},l.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},l.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},l.prototype.removeListener=function(e,t,n,i){var a=r?r+e:e;if(!this._events[a])return this;if(!t)return o(this,a),this;var l=this._events[a];if(l.fn)l.fn!==t||i&&!l.once||n&&l.context!==n||o(this,a);else{for(var c=0,u=[],s=l.length;c<s;c++)(l[c].fn!==t||i&&!l[c].once||n&&l[c].context!==n)&&u.push(l[c]);u.length?this._events[a]=1===u.length?u[0]:u:o(this,a)}return this},l.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&o(this,t)):(this._events=new n,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=r,l.EventEmitter=l,e.exports=l},82962:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(78673);t.throttle=function(e,t=0,r={}){let{leading:i=!0,trailing:a=!0}=r;return n.debounce(e,t,{leading:i,maxWait:t,trailing:a})}},83616:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.eq=function(e,t){return e===t||Number.isNaN(e)&&Number.isNaN(t)}},83949:(e,t,r)=>{e.exports=r(49901).range},84043:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(93639),i=r(72465),a=r(81571);t.maxBy=function(e,t){if(null!=e)return n.maxBy(Array.from(e),a.iteratee(t??i.identity))}},85252:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(22520),i=r(2767);t.isEqual=function(e,t){return n.isEqualWith(e,t,i.noop)}},86006:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if("object"!=typeof e||null==e)return!1;if(null===Object.getPrototypeOf(e))return!0;if("[object Object]"!==Object.prototype.toString.call(e)){let t=e[Symbol.toStringTag];return null!=t&&!!Object.getOwnPropertyDescriptor(e,Symbol.toStringTag)?.writable&&e.toString()===`[object ${t}]`}let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}},93205:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(14545),i=r(98412),a=r(50177),o=r(64072);t.has=function(e,t){let r;if(0===(r=Array.isArray(t)?t:"string"==typeof t&&n.isDeepKey(t)&&e?.[t]==null?o.toPath(t):[t]).length)return!1;let l=e;for(let e=0;e<r.length;e++){let t=r[e];if((null==l||!Object.hasOwn(l,t))&&!((Array.isArray(l)||a.isArguments(l))&&i.isIndex(t)&&t<l.length))return!1;l=l[t]}return!0}},93639:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.maxBy=function(e,t){if(0===e.length)return;let r=e[0],n=t(r);for(let i=1;i<e.length;i++){let a=e[i],o=t(a);o>n&&(n=o,r=a)}return r}},94456:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(10921),i=r(27040),a=r(14545),o=r(46200),l=r(64072);function c(e,t){let r=n.get(e,t.slice(0,-1),e),a=t[t.length-1];if(r?.[a]===void 0)return!0;if(i.isUnsafeProperty(a))return!1;try{return delete r[a],!0}catch{return!1}}t.unset=function(e,t){if(null==e)return!0;switch(typeof t){case"symbol":case"number":case"object":if(Array.isArray(t))return c(e,t);if("number"==typeof t?t=o.toKey(t):"object"==typeof t&&(t=Object.is(t?.valueOf(),-0)?"-0":String(t)),i.isUnsafeProperty(t))return!1;if(e?.[t]===void 0)return!0;try{return delete e[t],!0}catch{return!1}case"string":if(e?.[t]===void 0&&a.isDeepKey(t))return c(e,l.toPath(t));if(i.isUnsafeProperty(t))return!1;try{return delete e[t],!0}catch{return!1}}}},95672:(e,t,r)=>{e.exports=r(10921).get},98132:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(72744);t.isMatch=function(e,t){return n.isMatchWith(e,t,()=>void 0)}},98221:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getSymbols=function(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))}},98412:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let r=/^(?:0|[1-9]\d*)$/;t.isIndex=function(e,t=Number.MAX_SAFE_INTEGER){switch(typeof e){case"number":return Number.isInteger(e)&&e>=0&&e<t;case"symbol":return!1;case"string":return r.test(e)}}},99279:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObjectLike=function(e){return"object"==typeof e&&null!==e}}}]);