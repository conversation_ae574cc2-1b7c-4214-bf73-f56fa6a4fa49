(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8733],{3910:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>j});var a=t(95155),r=t(12115),i=t(10351),l=t(26071),c=t(52814),n=t(13741),d=t(17703),o=t(93915),m=t(66440),x=t(29925),h=t(30353),u=t(75399),v=t(64198),g=t(24630);function j(){var e;let[s,t]=(0,r.useState)([]),[j,N]=(0,r.useState)(!0),[p,y]=(0,r.useState)(null),[f,b]=(0,r.useState)(!1),[A,w]=(0,r.useState)(!1),[C,k]=(0,r.useState)({search:"",isActive:void 0,kycStatus:void 0,dateFrom:"",dateTo:"",sortBy:"createdAt",sortOrder:"desc",page:1,limit:20}),[P,S]=(0,r.useState)({total:0,page:1,limit:20,totalPages:0});(0,r.useEffect)(()=>{E()},[C]);let E=async()=>{try{N(!0);let e=await g.ZJ.getAllUsers(C),s=(e.users||[]).filter(e=>e&&"object"==typeof e&&"string"==typeof e._id).map(e=>({...e,id:e._id})).filter(e=>"string"==typeof e.id&&"string"==typeof e.firstName&&""!==e.firstName.trim()&&"string"==typeof e.lastName&&""!==e.lastName.trim()&&"string"==typeof e.email&&""!==e.email.trim());t(s),S({total:e.total,page:e.page,limit:e.limit,totalPages:e.totalPages})}catch(e){v.P0.error("Failed to load users")}finally{N(!1)}},D=async(e,a)=>{try{await g.ZJ.updateUserStatus(e,a),t(s.map(s=>s.id===e?{...s,isActive:a}:s)),v.P0.success("User ".concat(a?"activated":"deactivated"," successfully")),w(!1)}catch(e){v.P0.error("Failed to update user status")}},U=async e=>{try{let s=await g.ZJ.resetUserPassword(e);v.P0.success("Password reset. Temporary password: ".concat(s.temporaryPassword))}catch(e){v.P0.error("Failed to reset password")}},L=async()=>{try{v.P0.success("User export started. You will receive an email when ready.")}catch(e){v.P0.error("Failed to export users")}},O=e=>new Intl.NumberFormat("en-NG",{style:"currency",currency:"NGN"}).format(e),F=e=>new Date(e).toLocaleDateString("en-NG"),T=e=>{switch(e){case"APPROVED":return"success";case"PENDING":return"warning";case"REJECTED":return"error";default:return"default"}},I=e=>e?"success":"error",J=[{key:"firstName",title:"User",render:(e,s)=>{let t=s.firstName,r=s.lastName,i=s.email,l="".concat(t.charAt(0)||"?").concat(r.charAt(0)||"");return(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center text-white font-semibold",children:l}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"font-medium text-white",children:[t," ",r]}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:i})]})]})}},{key:"email",title:"Contact",render:(e,s)=>{let t=s.phoneNumber||"";return(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-400",children:[(0,a.jsx)(i.pHD,{className:"mr-1"}),e]}),t&&(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-400",children:[(0,a.jsx)(i.QFc,{className:"mr-1"}),t]})]})}},{key:"isActive",title:"Status",render:(e,s)=>{let t=s.kycStatus;return(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(c.E,{variant:I(e),children:e?"Active":"Inactive"}),(0,a.jsxs)(c.E,{variant:T(t),children:["KYC ",t]})]})}},{key:"balance",title:"Balance",render:(e,s)=>{let t=s.totalSavings;return(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"font-medium text-white",children:O(e)}),(0,a.jsxs)("p",{className:"text-sm text-gray-400",children:["Savings: ",O(t)]})]})}},{key:"createdAt",title:"Joined",render:(e,s)=>{let t=s.lastLoginAt;return(0,a.jsxs)("div",{className:"text-sm text-gray-400",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(i.wIk,{className:"mr-1"}),e?F(e):"N/A"]}),t&&(0,a.jsxs)("p",{className:"mt-1",children:["Last: ",F(t)]})]})}},{key:"id",title:"Actions",render:(e,s)=>{let t=s.isActive;return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(n.$n,{size:"sm",variant:"outline",onClick:()=>{y(s),b(!0)},children:(0,a.jsx)(i.Vap,{})}),(0,a.jsx)(n.$n,{size:"sm",variant:"outline",onClick:()=>{y(s),w(!0)},children:t?(0,a.jsx)(i.JOY,{}):(0,a.jsx)(i.p45,{})})]})}}];return(0,a.jsxs)(l.A,{title:"User Management",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white",children:"User Management"}),(0,a.jsx)("p",{className:"text-gray-400 mt-2",children:"Manage platform users and their accounts"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)(n.$n,{variant:"outline",onClick:L,children:[(0,a.jsx)(i.a4x,{className:"mr-2"}),"Export"]}),(0,a.jsxs)(n.$n,{variant:"outline",onClick:E,children:[(0,a.jsx)(i.jTZ,{className:"mr-2"}),"Refresh"]})]})]}),(0,a.jsx)(d.Z,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,a.jsx)(o.p,{placeholder:"Search users...",value:C.search,onChange:e=>k({...C,search:e.target.value})}),(0,a.jsx)(h.l,{value:"boolean"==typeof C.isActive?C.isActive?"active":"inactive":"",onChange:e=>{let s;"active"===e.target.value?s=!0:"inactive"===e.target.value&&(s=!1),k({...C,isActive:s})},options:[{value:"",label:"All Status"},{value:"active",label:"Active"},{value:"inactive",label:"Inactive"}]}),(0,a.jsx)(h.l,{value:C.kycStatus||"",onChange:e=>k({...C,kycStatus:e.target.value}),options:[{value:"",label:"All KYC Status"},{value:"PENDING",label:"KYC Pending"},{value:"APPROVED",label:"KYC Approved"},{value:"REJECTED",label:"KYC Rejected"}]}),(0,a.jsx)(o.p,{type:"date",value:C.dateFrom,onChange:e=>k({...C,dateFrom:e.target.value}),placeholder:"From Date"}),(0,a.jsx)(o.p,{type:"date",value:C.dateTo,onChange:e=>k({...C,dateTo:e.target.value}),placeholder:"To Date"})]})}),(0,a.jsxs)(d.Z,{className:"bg-gray-800 border-gray-700",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-700",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-white",children:["Users (",(null!=(e=P.total)?e:0).toLocaleString(),")"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(h.l,{value:C.sortBy,onChange:e=>k({...C,sortBy:e}),options:[{value:"createdAt",label:"Date Joined"},{value:"firstName",label:"Name"},{value:"balance",label:"Balance"},{value:"lastLoginAt",label:"Last Login"}]}),(0,a.jsx)(n.$n,{size:"sm",variant:"outline",onClick:()=>k({...C,sortOrder:"asc"===C.sortOrder?"desc":"asc"}),children:"asc"===C.sortOrder?"↑":"↓"})]})]})}),(0,a.jsx)(u.Ay,{columns:J,data:Array.isArray(s)?s:[],loading:j,emptyMessage:"No users found"}),(0,a.jsx)("div",{className:"p-6 border-t border-gray-700",children:(0,a.jsx)(x.dK,{currentPage:P.page,totalPages:P.totalPages,onPageChange:e=>k({...C,page:e}),showInfo:!0,totalItems:P.total,itemsPerPage:P.limit})})]})]}),(0,a.jsx)(m.Ay,{isOpen:f,onClose:()=>b(!1),title:"User Details",size:"lg",children:p&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"w-16 h-16 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center text-white text-xl font-bold",children:["string"==typeof p.firstName&&p.firstName?p.firstName.charAt(0):"?","string"==typeof p.lastName&&p.lastName?p.lastName.charAt(0):""]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold text-white",children:["string"==typeof p.firstName?p.firstName:""," ","string"==typeof p.lastName?p.lastName:""]}),(0,a.jsx)("p",{className:"text-gray-400",children:"string"==typeof p.email?p.email:"N/A"}),(0,a.jsxs)("div",{className:"flex space-x-2 mt-2",children:[(0,a.jsx)(c.E,{variant:I(p.isActive),children:p.isActive?"Active":"Inactive"}),(0,a.jsxs)(c.E,{variant:T(p.kycStatus),children:["KYC ",p.kycStatus]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-700 rounded-lg",children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Balance"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-white",children:O(p.balance)})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-700 rounded-lg",children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Savings"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-white",children:O(p.totalSavings)})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-700 rounded-lg",children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Earnings"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-white",children:O(p.balance)})]})]}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Phone Number"}),(0,a.jsx)("p",{className:"text-white",children:p.phoneNumber||"Not provided"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Date Joined"}),(0,a.jsx)("p",{className:"text-white",children:F(p.createdAt)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Last Login"}),(0,a.jsx)("p",{className:"text-white",children:p.lastLoginAt?F(p.lastLoginAt):"Never"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Email Verified"}),(0,a.jsx)(c.E,{variant:p.isVerified?"success":"warning",children:p.isVerified?"Verified":"Unverified"})]})]})}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4 border-t border-gray-700",children:[(0,a.jsx)(n.$n,{variant:"outline",onClick:()=>U(p.id),children:"Reset Password"}),(0,a.jsx)(n.$n,{variant:p.isActive?"danger":"secondary",onClick:()=>{b(!1),w(!0)},children:p.isActive?"Deactivate User":"Activate User"})]})]})}),(0,a.jsx)(m.Ay,{isOpen:A,onClose:()=>w(!1),title:"Confirm Status Change",children:p&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("p",{className:"text-gray-300",children:["Are you sure you want to ",p.isActive?"deactivate":"activate"," this user?"]}),(0,a.jsxs)("p",{className:"text-sm text-gray-400",children:["User: ",p.firstName," ",p.lastName," (",p.email,")"]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,a.jsx)(n.$n,{variant:"outline",onClick:()=>w(!1),children:"Cancel"}),(0,a.jsx)(n.$n,{variant:p.isActive?"danger":"secondary",onClick:()=>D(p.id,!p.isActive),children:p.isActive?"Deactivate":"Activate"})]})]})})]})}},8451:(e,s,t)=>{Promise.resolve().then(t.bind(t,3910))}},e=>{e.O(0,[844,9268,5236,6874,6766,3568,5221,3289,4630,411,6071,8940,8441,5964,7358],()=>e(e.s=8451)),_N_E=e.O()}]);