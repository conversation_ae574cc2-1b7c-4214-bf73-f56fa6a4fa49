/**
 * Mobile App Detection Utilities
 * Detects if the app is running in a Capacitor mobile environment
 */

declare global {
  interface Window {
    Capacitor?: {
      isNativePlatform: () => boolean;
      getPlatform: () => string;
    };
  }
}

/**
 * Check if the app is running in a Capacitor mobile environment
 */
export const isMobileApp = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  // Check for Capacitor
  if (window.Capacitor?.isNativePlatform?.()) {
    return true;
  }
  
  // Fallback: Check user agent for mobile patterns
  const userAgent = navigator.userAgent.toLowerCase();
  const mobilePatterns = [
    /android/i,
    /iphone/i,
    /ipad/i,
    /ipod/i,
    /blackberry/i,
    /windows phone/i,
    /mobile/i
  ];
  
  return mobilePatterns.some(pattern => pattern.test(userAgent));
};

/**
 * Check if running on Android
 */
export const isAndroid = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  if (window.Capacitor?.getPlatform?.() === 'android') {
    return true;
  }
  
  return /android/i.test(navigator.userAgent);
};

/**
 * Check if running on iOS
 */
export const isIOS = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  if (window.Capacitor?.getPlatform?.() === 'ios') {
    return true;
  }
  
  return /iphone|ipad|ipod/i.test(navigator.userAgent);
};

/**
 * Get the current platform
 */
export const getPlatform = (): 'web' | 'android' | 'ios' => {
  if (typeof window === 'undefined') return 'web';
  
  if (window.Capacitor?.getPlatform) {
    const platform = window.Capacitor.getPlatform();
    if (platform === 'android' || platform === 'ios') {
      return platform;
    }
  }
  
  if (isAndroid()) return 'android';
  if (isIOS()) return 'ios';
  
  return 'web';
};

/**
 * Check if the app should show mobile-specific UI
 */
export const shouldShowMobileUI = (): boolean => {
  return isMobileApp() || (typeof window !== 'undefined' && window.innerWidth < 768);
};

/**
 * Check if the app should skip the landing page
 */
export const shouldSkipLandingPage = (): boolean => {
  return isMobileApp();
};
