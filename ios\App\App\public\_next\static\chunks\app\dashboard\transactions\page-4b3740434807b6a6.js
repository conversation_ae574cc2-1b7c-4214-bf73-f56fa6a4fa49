(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2563],{33885:(e,t,a)=>{Promise.resolve().then(a.bind(a,77458))},52814:(e,t,a)=>{"use strict";a.d(t,{A:()=>n,E:()=>l});var s=a(95155);function l(e){let{children:t,variant:a="default",size:l="md",className:n=""}=e,r="\n    ".concat("\n    inline-flex items-center justify-center font-medium rounded-full\n    transition-all duration-200\n  "," \n    ").concat({default:"bg-gray-700 text-gray-300 border border-gray-600",success:"bg-green-500/20 text-green-400 border border-green-500/30",warning:"bg-yellow-500/20 text-yellow-400 border border-yellow-500/30",error:"bg-red-500/20 text-red-400 border border-red-500/30",info:"bg-blue-500/20 text-blue-400 border border-blue-500/30"}[a]," \n    ").concat({sm:"px-2 py-1 text-xs",md:"px-3 py-1 text-sm",lg:"px-4 py-2 text-base"}[l]," \n    ").concat(n,"\n  ");return(0,s.jsx)("span",{className:r,children:t})}a(12115);let n=l},77458:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>N});var s=a(95155),l=a(12115),n=a(10351),r=a(11846),i=a(24630),c=a(13741),o=a(17703),d=a(52814),x=a(93915),m=a(30353),h=a(66440),g=a(75399),u=a(29925),p=a(64198);let j={DEPOSIT:{icon:n.OgA,color:"text-green-500",bg:"bg-green-500/20"},WITHDRAWAL:{icon:n.qdV,color:"text-red-500",bg:"bg-red-500/20"},TRANSFER:{icon:n.jEl,color:"text-blue-500",bg:"bg-blue-500/20"},CONTRIBUTION:{icon:n.ARf,color:"text-purple-500",bg:"bg-purple-500/20"},INTEREST:{icon:n.z8N,color:"text-yellow-500",bg:"bg-yellow-500/20"},FEE:{icon:n.JW4,color:"text-orange-500",bg:"bg-orange-500/20"},REFUND:{icon:n.OgA,color:"text-green-500",bg:"bg-green-500/20"},PENALTY:{icon:n.JW4,color:"text-red-500",bg:"bg-red-500/20"}};function N(){var e;let[t,a]=(0,l.useState)([]),[N,b]=(0,l.useState)(!0),[y,f]=(0,l.useState)(null),[v,w]=(0,l.useState)(!1),[T,A]=(0,l.useState)({search:"",type:void 0,status:void 0,dateFrom:"",dateTo:"",minAmount:void 0,maxAmount:void 0,page:1,limit:20}),[I,E]=(0,l.useState)({total:0,page:1,limit:20,totalPages:0}),[D,S]=(0,l.useState)({totalTransactions:0,totalInflow:0,totalOutflow:0,netFlow:0,thisMonthTransactions:0,avgTransactionAmount:0});(0,l.useEffect)(()=>{C(),F()},[T]);let C=async()=>{try{b(!0);let e=await i.lD.getUserTransactions("current",T);a(e.transactions),E({total:e.total,page:e.page,limit:e.limit,totalPages:e.totalPages})}catch(e){p.oR.error("Failed to load transactions")}finally{b(!1)}},F=async()=>{try{let e=await i.lD.getTransactionStats();S({totalTransactions:e.totalTransactions,totalInflow:e.totalDeposits,totalOutflow:e.totalWithdrawals,netFlow:e.totalDeposits-e.totalWithdrawals,thisMonthTransactions:e.transactionsThisMonth,avgTransactionAmount:e.averageTransactionAmount})}catch(e){console.error("Failed to load transaction stats:",e)}},R=async()=>{try{let e={format:"CSV",filters:T,includeMetadata:!0,dateRange:{from:T.dateFrom||new Date(Date.now()-2592e6).toISOString().split("T")[0],to:T.dateTo||new Date().toISOString().split("T")[0]}},t=await i.lD.exportTransactions(e),a=document.createElement("a");a.href=t.downloadUrl,a.download=t.fileName,document.body.appendChild(a),a.click(),document.body.removeChild(a),p.oR.success("Transactions exported successfully")}catch(e){p.oR.error("Failed to export transactions")}},O=e=>new Intl.NumberFormat("en-NG",{style:"currency",currency:"NGN"}).format(e),P=e=>new Date(e).toLocaleDateString("en-NG",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),k=e=>{switch(e){case"COMPLETED":return"success";case"PENDING":return"warning";case"FAILED":return"error";default:return"default"}},L=e=>j[e]||j.TRANSFER,M=e=>{let t=["DEPOSIT","REFUND","INTEREST"].includes(e.type);return(0,s.jsxs)("span",{className:"font-semibold ".concat(t?"text-green-400":"text-red-400"),children:[t?"+":"-",O(Math.abs(e.amount))]})},W=[{key:"type",title:"Type",render:e=>{let t=L(e.type),a=t.icon;return(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-10 h-10 ".concat(t.bg," rounded-full flex items-center justify-center"),children:(0,s.jsx)(a,{className:"".concat(t.color)})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-white",children:e.type.replace("_"," ")}),(0,s.jsx)("p",{className:"text-sm text-gray-400",children:e.description})]})]})}},{key:"reference",title:"Reference",render:e=>(0,s.jsx)("span",{className:"font-mono text-sm text-gray-300",children:e.reference})},{key:"amount",title:"Amount",render:e=>M(e)},{key:"status",title:"Status",render:e=>(0,s.jsx)(d.E,{variant:k(e.status),children:e.status})},{key:"balanceAfter",title:"Balance After",render:e=>(0,s.jsx)("span",{className:"text-gray-300",children:O(e.balanceAfter)})},{key:"createdAt",title:"Date",render:e=>(0,s.jsx)("span",{className:"text-gray-400",children:P(e.createdAt)})},{key:"actions",title:"Actions",render:e=>(0,s.jsx)(c.$n,{size:"sm",variant:"outline",onClick:()=>{f(e),w(!0)},children:(0,s.jsx)(n.Vap,{})})}];return(0,s.jsxs)(r.A,{title:"Transactions",children:[(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-white",children:"Transaction History"}),(0,s.jsx)("p",{className:"text-gray-400 mt-2",children:"View all your account transactions"})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)(c.$n,{variant:"outline",onClick:C,children:[(0,s.jsx)(n.jTZ,{className:"mr-2"}),"Refresh"]}),(0,s.jsxs)(c.$n,{variant:"outline",onClick:R,children:[(0,s.jsx)(n.a4x,{className:"mr-2"}),"Export"]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,s.jsx)(o.Z,{className:"bg-gray-800 border-gray-700 p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Transactions"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-white",children:D.totalTransactions.toLocaleString()}),(0,s.jsxs)("p",{className:"text-blue-400 text-sm",children:["+",D.thisMonthTransactions," this month"]})]}),(0,s.jsx)(n.z1n,{className:"text-blue-500 text-2xl"})]})}),(0,s.jsx)(o.Z,{className:"bg-gray-800 border-gray-700 p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Inflow"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-green-400",children:O(D.totalInflow)}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Money received"})]}),(0,s.jsx)(n.OgA,{className:"text-green-500 text-2xl"})]})}),(0,s.jsx)(o.Z,{className:"bg-gray-800 border-gray-700 p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Outflow"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-red-400",children:O(D.totalOutflow)}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Money sent"})]}),(0,s.jsx)(n.qdV,{className:"text-red-500 text-2xl"})]})}),(0,s.jsx)(o.Z,{className:"bg-gray-800 border-gray-700 p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Net Flow"}),(0,s.jsx)("p",{className:"text-2xl font-bold ".concat(D.netFlow>=0?"text-green-400":"text-red-400"),children:O(D.netFlow)}),(0,s.jsxs)("p",{className:"text-gray-400 text-sm",children:["Avg: ",O(D.avgTransactionAmount)]})]}),(0,s.jsx)(n.z8N,{className:"text-purple-500 text-2xl"})]})})]}),(0,s.jsxs)(o.Z,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4",children:[(0,s.jsx)(x.p,{placeholder:"Search transactions...",value:T.search,onChange:e=>A({...T,search:e.target.value})}),(0,s.jsx)(m.l,{value:T.type||"",onChange:e=>A({...T,type:e.target.value||void 0}),options:[{value:"",label:"All Types"},{value:"DEPOSIT",label:"Deposits"},{value:"WITHDRAWAL",label:"Withdrawals"},{value:"TRANSFER",label:"Transfers"},{value:"CONTRIBUTION",label:"Contributions"},{value:"INTEREST",label:"Interest"},{value:"FEE",label:"Fees"},{value:"REFUND",label:"Refunds"},{value:"PENALTY",label:"Penalties"}]}),(0,s.jsx)(m.l,{value:T.status||"",onChange:e=>A({...T,status:e.target.value||void 0}),options:[{value:"",label:"All Status"},{value:"COMPLETED",label:"Completed"},{value:"PENDING",label:"Pending"},{value:"FAILED",label:"Failed"},{value:"CANCELLED",label:"Cancelled"}]}),(0,s.jsx)(x.p,{type:"date",value:T.dateFrom,onChange:e=>A({...T,dateFrom:e.target.value}),placeholder:"From Date"}),(0,s.jsx)(x.p,{type:"date",value:T.dateTo,onChange:e=>A({...T,dateTo:e.target.value}),placeholder:"To Date"}),(0,s.jsx)(c.$n,{variant:"outline",onClick:()=>A({search:"",type:void 0,status:void 0,dateFrom:"",dateTo:"",minAmount:void 0,maxAmount:void 0,page:1,limit:20}),children:"Clear"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-4",children:[(0,s.jsx)(x.p,{type:"number",placeholder:"Min amount",value:T.minAmount||"",onChange:e=>A({...T,minAmount:Number(e.target.value)||void 0})}),(0,s.jsx)(x.p,{type:"number",placeholder:"Max amount",value:T.maxAmount||"",onChange:e=>A({...T,maxAmount:Number(e.target.value)||void 0})})]})]}),(0,s.jsxs)(o.Z,{className:"bg-gray-800 border-gray-700",children:[(0,s.jsx)("div",{className:"p-6 border-b border-gray-700",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-white",children:["Transactions (",I.total.toLocaleString(),")"]}),(0,s.jsx)("div",{className:"flex items-center space-x-2",children:(0,s.jsx)(m.l,{value:(null==(e=T.limit)?void 0:e.toString())||"20",onChange:e=>A({...T,limit:Number(e),page:1}),options:[{value:"10",label:"10 per page"},{value:"20",label:"20 per page"},{value:"50",label:"50 per page"},{value:"100",label:"100 per page"}]})})]})}),(0,s.jsx)(g.Ay,{data:t,columns:W,loading:N,emptyMessage:"No transactions found"}),(0,s.jsx)("div",{className:"p-6 border-t border-gray-700",children:(0,s.jsx)(u.dK,{currentPage:I.page,totalPages:I.totalPages,onPageChange:e=>A({...T,page:e}),showInfo:!0,totalItems:I.total,itemsPerPage:I.limit})})]})]}),(0,s.jsx)(h.aF,{isOpen:v,onClose:()=>w(!1),title:"Transaction Details",size:"lg",children:y&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-gray-700 rounded-lg",children:[(0,s.jsx)("div",{className:"w-16 h-16 ".concat(L(y.type).bg," rounded-full flex items-center justify-center"),children:l.createElement(L(y.type).icon,{className:"".concat(L(y.type).color," text-2xl")})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-white",children:y.type.replace("_"," ")}),(0,s.jsx)("p",{className:"text-gray-400",children:y.description}),(0,s.jsx)(d.E,{variant:k(y.status),className:"mt-2",children:y.status})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-white mb-3",children:"Transaction Information"}),(0,s.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Reference:"}),(0,s.jsx)("span",{className:"text-white font-mono",children:y.reference})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Amount:"}),(0,s.jsx)("span",{className:"text-white",children:M(y)})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Balance Before:"}),(0,s.jsx)("span",{className:"text-white",children:O(y.balanceBefore)})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Balance After:"}),(0,s.jsx)("span",{className:"text-white",children:O(y.balanceAfter)})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-white mb-3",children:"Additional Details"}),(0,s.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Date:"}),(0,s.jsx)("span",{className:"text-white",children:P(y.createdAt)})]}),y.processedAt&&(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Processed:"}),(0,s.jsx)("span",{className:"text-white",children:P(y.processedAt)})]}),y.fees&&y.fees>0&&(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Fees:"}),(0,s.jsx)("span",{className:"text-white",children:O(y.fees)})]})]})]})]}),(y.planId||y.goalId||y.targetId||y.groupId)&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-white mb-3",children:"Related Information"}),(0,s.jsxs)("div",{className:"p-4 bg-gray-700 rounded-lg",children:[y.planId&&(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Plan ID:"}),(0,s.jsx)("span",{className:"text-white font-mono",children:y.planId})]}),y.goalId&&(0,s.jsxs)("div",{className:"flex justify-between items-center mt-2",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Goal ID:"}),(0,s.jsx)("span",{className:"text-white font-mono",children:y.goalId})]}),y.targetId&&(0,s.jsxs)("div",{className:"flex justify-between items-center mt-2",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Target ID:"}),(0,s.jsx)("span",{className:"text-white font-mono",children:y.targetId})]}),y.groupId&&(0,s.jsxs)("div",{className:"flex justify-between items-center mt-2",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Group ID:"}),(0,s.jsx)("span",{className:"text-white font-mono",children:y.groupId})]})]})]}),y.description&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-white mb-3",children:"Description"}),(0,s.jsx)("p",{className:"text-gray-300 bg-gray-700 p-3 rounded-lg",children:y.description})]}),y.metadata&&Object.keys(y.metadata).length>0&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-white mb-3",children:"Additional Data"}),(0,s.jsx)("div",{className:"bg-gray-700 p-3 rounded-lg",children:(0,s.jsx)("pre",{className:"text-sm text-gray-300 overflow-x-auto",children:JSON.stringify(y.metadata,null,2)})})]})]})})]})}}},e=>{e.O(0,[844,9268,5236,6874,6766,3568,5221,3289,4630,1846,411,8940,8441,5964,7358],()=>e(e.s=33885)),_N_E=e.O()}]);