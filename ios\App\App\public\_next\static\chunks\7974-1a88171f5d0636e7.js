(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7974],{6654:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return l}});let r=n(12115);function l(e,t){let n=(0,r.useRef)(null),l=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=l.current;t&&(l.current=null,t())}else e&&(n.current=u(e,r)),t&&(l.current=u(t,r))},[e,t])}function u(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},15863:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'"},className:"__className_245e56",variable:"__variable_245e56"}},35695:(e,t,n)=>{"use strict";var r=n(18999);n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}})},42714:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return u}});let n={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},r=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function l(e){return["async","defer","noModule"].includes(e)}function u(e,t){for(let[u,a]of Object.entries(t)){if(!t.hasOwnProperty(u)||r.includes(u)||void 0===a)continue;let i=n[u]||u.toLowerCase();"SCRIPT"===e.tagName&&l(i)?e[i]=!!a:e.setAttribute(i,String(a)),(!1===a||"SCRIPT"===e.tagName&&l(i)&&(!a||"false"===a))&&(e.setAttribute(i,""),e.removeAttribute(i))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},69243:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return g},handleClientScriptLoad:function(){return y},initScriptLoader:function(){return _}});let r=n(88229),l=n(6966),u=n(95155),a=r._(n(47650)),i=l._(n(12115)),o=n(82830),s=n(42714),c=n(92374),d=new Map,f=new Set,p=e=>{let{src:t,id:n,onLoad:r=()=>{},onReady:l=null,dangerouslySetInnerHTML:u,children:i="",strategy:o="afterInteractive",onError:c,stylesheets:p}=e,y=n||t;if(y&&f.has(y))return;if(d.has(t)){f.add(y),d.get(t).then(r,c);return}let _=()=>{l&&l(),f.add(y)},b=document.createElement("script"),g=new Promise((e,t)=>{b.addEventListener("load",function(t){e(),r&&r.call(this,t),_()}),b.addEventListener("error",function(e){t(e)})}).catch(function(e){c&&c(e)});u?(b.innerHTML=u.__html||"",_()):i?(b.textContent="string"==typeof i?i:Array.isArray(i)?i.join(""):"",_()):t&&(b.src=t,d.set(t,g)),(0,s.setAttributesFromProps)(b,e),"worker"===o&&b.setAttribute("type","text/partytown"),b.setAttribute("data-nscript",o),p&&(e=>{if(a.default.preinit)return e.forEach(e=>{a.default.preinit(e,{as:"style"})});{let t=document.head;e.forEach(e=>{let n=document.createElement("link");n.type="text/css",n.rel="stylesheet",n.href=e,t.appendChild(n)})}})(p),document.body.appendChild(b)};function y(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>p(e))}):p(e)}function _(e){e.forEach(y),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");f.add(t)})}function b(e){let{id:t,src:n="",onLoad:r=()=>{},onReady:l=null,strategy:s="afterInteractive",onError:d,stylesheets:y,..._}=e,{updateScripts:b,scripts:g,getIsSsr:h,appDir:m,nonce:v}=(0,i.useContext)(o.HeadManagerContext);v=_.nonce||v;let O=(0,i.useRef)(!1);(0,i.useEffect)(()=>{let e=t||n;O.current||(l&&e&&f.has(e)&&l(),O.current=!0)},[l,t,n]);let I=(0,i.useRef)(!1);if((0,i.useEffect)(()=>{if(!I.current){if("afterInteractive"===s)p(e);else"lazyOnload"===s&&("complete"===document.readyState?(0,c.requestIdleCallback)(()=>p(e)):window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>p(e))}));I.current=!0}},[e,s]),("beforeInteractive"===s||"worker"===s)&&(b?(g[s]=(g[s]||[]).concat([{id:t,src:n,onLoad:r,onReady:l,onError:d,..._,nonce:v}]),b(g)):h&&h()?f.add(t||n):h&&!h()&&p({...e,nonce:v})),m){if(y&&y.forEach(e=>{a.default.preinit(e,{as:"style"})}),"beforeInteractive"===s)if(!n)return _.dangerouslySetInnerHTML&&(_.children=_.dangerouslySetInnerHTML.__html,delete _.dangerouslySetInnerHTML),(0,u.jsx)("script",{nonce:v,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{..._,id:t}])+")"}});else return a.default.preload(n,_.integrity?{as:"script",integrity:_.integrity,nonce:v,crossOrigin:_.crossOrigin}:{as:"script",nonce:v,crossOrigin:_.crossOrigin}),(0,u.jsx)("script",{nonce:v,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([n,{..._,id:t}])+")"}});"afterInteractive"===s&&n&&a.default.preload(n,_.integrity?{as:"script",integrity:_.integrity,nonce:v,crossOrigin:_.crossOrigin}:{as:"script",nonce:v,crossOrigin:_.crossOrigin})}return null}Object.defineProperty(b,"__nextScript",{value:!0});let g=b;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},92374:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{cancelIdleCallback:function(){return r},requestIdleCallback:function(){return n}});let n="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},r="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}}]);