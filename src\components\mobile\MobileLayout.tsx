"use client";

import { ReactNode } from 'react';
import { isMobileApp } from '../../utils/mobileDetection';
import MobileNavigation from './MobileNavigation';

interface MobileLayoutProps {
  children: ReactNode;
  title?: string;
  showBackButton?: boolean;
  onBackClick?: () => void;
}

export default function MobileLayout({ 
  children, 
  title, 
  showBackButton = false, 
  onBackClick 
}: MobileLayoutProps) {
  const isMobile = isMobileApp();

  if (!isMobile) {
    // Return children without mobile layout for web
    return <>{children}</>;
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Mobile Navigation */}
      <MobileNavigation />
      
      {/* Main Content */}
      <main className="flex-1 overflow-y-auto">
        {/* Page Header (if title provided) */}
        {title && (
          <div className="bg-white border-b border-gray-200 px-4 py-3">
            <div className="flex items-center">
              {showBackButton && (
                <button
                  onClick={onBackClick}
                  className="mr-3 p-2 -ml-2 text-gray-600 hover:text-gray-900"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
              )}
              <h1 className="text-lg font-semibold text-gray-900">{title}</h1>
            </div>
          </div>
        )}
        
        {/* Page Content */}
        <div className="p-4 pb-20">
          {children}
        </div>
      </main>
    </div>
  );
}
