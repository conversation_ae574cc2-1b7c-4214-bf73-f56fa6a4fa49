"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4630],{24630:(t,e,a)=>{a.d(e,{ZJ:()=>O,y1:()=>r.authService,Pd:()=>f,Iu:()=>c.I,$p:()=>T,wy:()=>j,KT:()=>m.K,TA:()=>i.T,Uy:()=>d,lD:()=>E,Dv:()=>n,uX:()=>w.u});var r=a(96365);let s="http://localhost:8080";class o{getAuthHeaders(){let t=localStorage.getItem("auth_token");return{"Content-Type":"application/json",...t&&{Authorization:"Bearer ".concat(t)}}}async getCurrentUserProfile(){let t=await fetch("".concat(s,"/api/auth/user-info"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch user profile");return(await t.json()).user}async updateCurrentUserProfile(t){let e=await fetch("".concat(s,"/user/me"),{method:"PUT",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!e.ok)throw Error((await e.json()).message||"Failed to update user profile");return e.json()}async uploadProfileImage(t){let e=new FormData;e.append("profileImage",t);let a=localStorage.getItem("auth_token"),r=await fetch("".concat(s,"/user/me/profile-image"),{method:"POST",headers:{...a&&{Authorization:"Bearer ".concat(a)}},body:e});if(!r.ok)throw Error((await r.json()).message||"Failed to upload profile image");return r.json()}async getUserBalance(t){if(!t)throw Error("User ID is required to fetch balance");let e=await fetch("".concat(s,"/api/userinfo/balance/").concat(t),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch user balance");return e.json()}async refreshUserBalance(){let t=await fetch("".concat(s,"/user/balance/refresh"),{method:"POST",headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to refresh user balance");return t.json()}async addBankAccount(t){let e=await fetch("".concat(s,"/user/bank-accounts"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!e.ok)throw Error((await e.json()).message||"Failed to add bank account");return e.json()}async getBankAccounts(){let t=await fetch("".concat(s,"/user/bank-accounts"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch bank accounts");return t.json()}async deleteBankAccount(t){let e=await fetch("".concat(s,"/user/bank-accounts/").concat(t),{method:"DELETE",headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to delete bank account")}async setDefaultBankAccount(t){let e=await fetch("".concat(s,"/user/bank-accounts/").concat(t,"/set-default"),{method:"POST",headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to set default bank account")}async getUserById(t){let e=await fetch("".concat(s,"/user/").concat(t),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch user");return e.json()}async getAllUsers(t){let e=new URLSearchParams;t&&Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&""!==r&&e.append(a,r.toString())});let a=await fetch("".concat(s,"/user/all?").concat(e),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch users");return a.json()}async getUserStats(){let t=await fetch("".concat(s,"/user/stats"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch user statistics");return t.json()}async updateUserStatus(t,e){let a=await fetch("".concat(s,"/user/").concat(t,"/status"),{method:"PATCH",headers:this.getAuthHeaders(),body:JSON.stringify({isActive:e})});if(!a.ok)throw Error((await a.json()).message||"Failed to update user status")}async resetUserPassword(t){let e=await fetch("".concat(s,"/user/").concat(t,"/reset-password"),{method:"POST",headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to reset user password");return e.json()}async getUserActivity(t,e){let a=new URLSearchParams;e&&Object.entries(e).forEach(t=>{let[e,r]=t;null!=r&&""!==r&&a.append(e,r.toString())});let r=await fetch("".concat(s,"/user/").concat(t,"/activity?").concat(a),{headers:this.getAuthHeaders()});if(!r.ok)throw Error((await r.json()).message||"Failed to fetch user activity");return r.json()}formatCurrency(t){return new Intl.NumberFormat("en-NG",{style:"currency",currency:"NGN"}).format(t)}getInitials(t,e){return"".concat(t.charAt(0)).concat(e.charAt(0)).toUpperCase()}validatePhoneNumber(t){return/^(\+234|234|0)?[789][01]\d{8}$/.test(t)}validateEmail(t){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)}}let n=new o;var i=a(41277),c=a(59972);let h="http://localhost:8080";class l{getAuthHeaders(){let t=localStorage.getItem("auth_token");return{"Content-Type":"application/json",...t&&{Authorization:"Bearer ".concat(t)}}}async getTargetSavings(){let t=await fetch("".concat(h,"/api/target-savings"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).error||"Failed to fetch target savings");let e=await t.json();return{targets:Array.isArray(e)?e:[]}}async getTargetSavingsById(t){let e=await fetch("".concat(h,"/target-savings/").concat(t),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch target savings");return e.json()}async createTargetSavings(t){let e={goalName:t.title||"",targetAmount:t.targetAmount,timelineMonths:t.targetDate&&t.targetAmount&&t.contributionAmount?Math.max(1,Math.round(Number(t.targetAmount)/Number(t.contributionAmount))):1,frequency:(t.contributionFrequency||"MONTHLY").toLowerCase()},a=await fetch("".concat(h,"/api/target-savings"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(e)});if(!a.ok)throw Error((await a.json()).error||"Failed to create target savings");return a.json()}async updateTargetSavings(t,e){let a=await fetch("".concat(h,"/target-savings/").concat(t),{method:"PUT",headers:this.getAuthHeaders(),body:JSON.stringify(e)});if(!a.ok)throw Error((await a.json()).message||"Failed to update target savings");return a.json()}async deleteTargetSavings(t){let e=await fetch("".concat(h,"/target-savings/").concat(t),{method:"DELETE",headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to delete target savings")}async pauseTargetSavings(t){let e=await fetch("".concat(h,"/target-savings/").concat(t,"/pause"),{method:"POST",headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to pause target savings");return e.json()}async resumeTargetSavings(t){let e=await fetch("".concat(h,"/target-savings/").concat(t,"/resume"),{method:"POST",headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to resume target savings");return e.json()}async makeContribution(t){let e=await fetch("".concat(h,"/target-savings/").concat(t.targetId,"/contribute"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify({amount:t.amount,paymentMethod:t.paymentMethod,description:t.description})});if(!e.ok)throw Error((await e.json()).message||"Failed to make contribution");return e.json()}async getContributionHistory(t,e){let a=new URLSearchParams;e&&Object.entries(e).forEach(t=>{let[e,r]=t;null!=r&&""!==r&&a.append(e,r.toString())});let r=await fetch("".concat(h,"/target-savings/").concat(t,"/contributions?").concat(a),{headers:this.getAuthHeaders()});if(!r.ok)throw Error((await r.json()).message||"Failed to fetch contribution history");return r.json()}async addMilestone(t,e){let a=await fetch("".concat(h,"/target-savings/").concat(t,"/milestones"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(e)});if(!a.ok)throw Error((await a.json()).message||"Failed to add milestone");return a.json()}async updateMilestone(t,e,a){let r=await fetch("".concat(h,"/target-savings/").concat(t,"/milestones/").concat(e),{method:"PUT",headers:this.getAuthHeaders(),body:JSON.stringify(a)});if(!r.ok)throw Error((await r.json()).message||"Failed to update milestone");return r.json()}async deleteMilestone(t,e){let a=await fetch("".concat(h,"/target-savings/").concat(t,"/milestones/").concat(e),{method:"DELETE",headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to delete milestone")}async completeMilestone(t,e){let a=await fetch("".concat(h,"/target-savings/").concat(t,"/milestones/").concat(e,"/complete"),{method:"POST",headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to complete milestone");return a.json()}async addReminder(t,e){let a=await fetch("".concat(h,"/target-savings/").concat(t,"/reminders"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(e)});if(!a.ok)throw Error((await a.json()).message||"Failed to add reminder");return a.json()}async updateReminder(t,e,a){let r=await fetch("".concat(h,"/target-savings/").concat(t,"/reminders/").concat(e),{method:"PUT",headers:this.getAuthHeaders(),body:JSON.stringify(a)});if(!r.ok)throw Error((await r.json()).message||"Failed to update reminder");return r.json()}async deleteReminder(t,e){let a=await fetch("".concat(h,"/target-savings/").concat(t,"/reminders/").concat(e),{method:"DELETE",headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to delete reminder")}async getTargetStats(){let t=await fetch("".concat(h,"/target-savings/stats"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch target statistics");return t.json()}async getTargetProgress(t){let e=await fetch("".concat(h,"/target-savings/").concat(t,"/progress"),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch target progress");return e.json()}calculateProgress(t,e){return Math.min(t/e*100,100)}calculateDaysRemaining(t){let e=new Date(t),a=new Date;return Math.ceil((e.getTime()-a.getTime())/864e5)}calculateRecommendedMonthlyContribution(t,e,a){return(t-e)/Math.max(this.calculateDaysRemaining(a)/30,1)}isOnTrack(t,e,a,r){let s=new Date(a).getTime()-new Date(r).getTime();return e>=(Date.now()-new Date(r).getTime())/s*t*.9}formatCurrency(t){return new Intl.NumberFormat("en-NG",{style:"currency",currency:"NGN"}).format(t)}getCategoryIcon(t){return({EMERGENCY:"\uD83D\uDEA8",VACATION:"✈️",EDUCATION:"\uD83C\uDF93",HOUSE:"\uD83C\uDFE0",CAR:"\uD83D\uDE97",BUSINESS:"\uD83D\uDCBC",WEDDING:"\uD83D\uDC92",HEALTH:"\uD83C\uDFE5",OTHER:"\uD83C\uDFAF"})[t]||"\uD83C\uDFAF"}getPriorityColor(t){return({LOW:"#10B981",MEDIUM:"#F59E0B",HIGH:"#EF4444"})[t]||"#6B7280"}}let d=new l,u="http://localhost:8080";class g{getAuthHeaders(){let t=localStorage.getItem("auth_token");return{"Content-Type":"application/json",...t&&{Authorization:"Bearer ".concat(t)}}}async initiateDeposit(t){let e=await fetch("".concat(u,"/api/deposits/initialize"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!e.ok)throw Error((await e.json()).message||"Failed to initiate deposit");return e.json()}async verifyDeposit(t){let e=await fetch("".concat(u,"/api/deposit/verify"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!e.ok)throw Error((await e.json()).message||"Failed to verify deposit");return e.json()}async getUserDeposits(t){let e=new URLSearchParams;t&&Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&""!==r&&e.append(a,r.toString())});let a=await fetch("".concat(u,"/api/deposit/user?").concat(e),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch user deposits");return a.json()}async getDepositById(t){let e=await fetch("".concat(u,"/api/deposit/").concat(t),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch deposit");return e.json()}async cancelDeposit(t){let e=await fetch("".concat(u,"/api/deposit/").concat(t,"/cancel"),{method:"POST",headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to cancel deposit");return e.json()}async getAllDeposits(t){let e=new URLSearchParams;t&&Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&""!==r&&e.append(a,r.toString())});let a=await fetch("".concat(u,"/api/deposit/all?").concat(e),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch all deposits");return a.json()}async updateDepositStatus(t,e,a){let r=await fetch("".concat(u,"/api/deposit/").concat(t,"/status"),{method:"PATCH",headers:this.getAuthHeaders(),body:JSON.stringify({status:e,adminNotes:a})});if(!r.ok)throw Error((await r.json()).message||"Failed to update deposit status");return r.json()}async getDepositStats(t){let e=new URLSearchParams;t&&Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&""!==r&&e.append(a,r.toString())});let a=await fetch("".concat(u,"/api/deposit/stats?").concat(e),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch deposit statistics");return a.json()}async getDepositAnalytics(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"monthly",e=await fetch("".concat(u,"/api/deposit/analytics?period=").concat(t),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch deposit analytics");return e.json()}async getPaymentMethods(){let t=await fetch("".concat(u,"/api/deposit/payment-methods"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch payment methods");return t.json()}async getBanks(){let t=await fetch("".concat(u,"/api/deposit/banks"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch banks");return t.json()}async handlePaystackWebhook(t){let e=await fetch("".concat(u,"/api/deposit/webhook/paystack"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!e.ok)throw Error((await e.json()).message||"Failed to handle webhook")}calculateFees(t,e){let a={CARD:{percentage:1.5,fixed:100,cap:2e3},BANK_TRANSFER:{percentage:.5,fixed:50,cap:1e3},USSD:{percentage:1,fixed:50},QR_CODE:{percentage:1,fixed:50},MOBILE_MONEY:{percentage:1.5,fixed:100}},r=a[e]||a.CARD,s=t*r.percentage/100+r.fixed;return r.cap&&s>r.cap&&(s=r.cap),{fees:Math.round(s),netAmount:t-Math.round(s)}}formatCurrency(t){return new Intl.NumberFormat("en-NG",{style:"currency",currency:"NGN"}).format(t)}getPaymentMethodIcon(t){return({CARD:"\uD83D\uDCB3",BANK_TRANSFER:"\uD83C\uDFE6",USSD:"\uD83D\uDCF1",QR_CODE:"\uD83D\uDCF1",MOBILE_MONEY:"\uD83D\uDCF1"})[t]||"\uD83D\uDCB3"}getStatusColor(t){return({PENDING:"#F59E0B",PROCESSING:"#3B82F6",COMPLETED:"#10B981",FAILED:"#EF4444",CANCELLED:"#6B7280"})[t]||"#6B7280"}generateReference(){let t=Date.now().toString(),e=Math.random().toString(36).substring(2,8);return"DEP_".concat(t,"_").concat(e).toUpperCase()}validateAmount(t,e){let a={CARD:{min:100,max:1e6},BANK_TRANSFER:{min:100,max:5e6},USSD:{min:100,max:1e5},QR_CODE:{min:100,max:1e5},MOBILE_MONEY:{min:100,max:5e5}},r=a[e]||a.CARD;return t<r.min?{isValid:!1,error:"Minimum amount for ".concat(e," is ").concat(this.formatCurrency(r.min))}:t>r.max?{isValid:!1,error:"Maximum amount for ".concat(e," is ").concat(this.formatCurrency(r.max))}:{isValid:!0}}async getUserTotalDeposits(t){let e,a="".concat(u,"/api/deposit/user/").concat(t,"/total");console.log("[depositsService] Fetching total deposits:",{url:a,userId:t});let r=await fetch(a,{headers:this.getAuthHeaders()});404===r.status&&(console.log("[depositsService] Retrying with plural route:",{url:a="".concat(u,"/api/deposits/user/").concat(t,"/total"),userId:t}),r=await fetch(a,{headers:this.getAuthHeaders()})),console.log("[depositsService] Backend response:",{url:a,status:r.status,statusText:r.statusText,headers:Object.fromEntries(r.headers.entries())});try{e=await r.clone().json(),console.log("[depositsService] Backend response body:",e)}catch(t){console.error("[depositsService] Failed to parse backend response as JSON:",t),e=null}if(!r.ok)throw console.error("[depositsService] Failed to fetch total deposits:",{url:a,userId:t,error:e}),Error(e&&e.message||"Failed to fetch total deposits");return e&&e.total?e.total:0}}let f=new g;var w=a(46142),m=a(43851);let p="http://localhost:8080";class y{getAuthHeaders(){let t=localStorage.getItem("auth_token");return{"Content-Type":"application/json",...t&&{Authorization:"Bearer ".concat(t)}}}async getUserTransactions(t,e){let a=new URLSearchParams;e&&Object.entries(e).forEach(t=>{let[e,r]=t;null!=r&&""!==r&&a.append(e,r.toString())});let r="".concat(p,"/api/transactions/user/").concat(t,"?").concat(a),s=await fetch(r,{headers:this.getAuthHeaders()});if(!s.ok)throw Error((await s.json()).message||"Failed to fetch user transactions");return s.json()}async getAllTransactions(t){let e=new URLSearchParams;t&&Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&""!==r&&e.append(a,r.toString())});let a=await fetch("".concat(p,"/api/transactions/all?").concat(e),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch all transactions");return a.json()}async getTransactionById(t){let e=await fetch("".concat(p,"/api/transactions/").concat(t),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch transaction");return e.json()}async createTransaction(t){let e=await fetch("".concat(p,"/api/transactions"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!e.ok)throw Error((await e.json()).message||"Failed to create transaction");return e.json()}async updateTransactionStatus(t,e,a){let r=await fetch("".concat(p,"/api/transactions/").concat(t,"/status"),{method:"PATCH",headers:this.getAuthHeaders(),body:JSON.stringify({status:e,notes:a})});if(!r.ok)throw Error((await r.json()).message||"Failed to update transaction status");return r.json()}async getTransactionStats(t){let e=new URLSearchParams;t&&Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&""!==r&&e.append(a,r.toString())});let a=await fetch("".concat(p,"/api/transactions/stats?").concat(e),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch transaction statistics");return a.json()}async getTransactionSummary(t,e){let a=new URLSearchParams({period:t});e&&a.append("userId",e);let r=await fetch("".concat(p,"/api/transactions/summary?").concat(a),{headers:this.getAuthHeaders()});if(!r.ok)throw Error((await r.json()).message||"Failed to fetch transaction summary");return r.json()}async getTransactionAnalytics(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"monthly",e=arguments.length>1?arguments[1]:void 0,a=new URLSearchParams({period:t});e&&Object.entries(e).forEach(t=>{let[e,r]=t;null!=r&&""!==r&&a.append(e,r.toString())});let r=await fetch("".concat(p,"/api/transactions/analytics?").concat(a),{headers:this.getAuthHeaders()});if(!r.ok)throw Error((await r.json()).message||"Failed to fetch transaction analytics");return r.json()}async exportTransactions(t){let e=await fetch("".concat(p,"/api/transactions/export"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!e.ok)throw Error((await e.json()).message||"Failed to export transactions");return e.json()}async generateTransactionReport(t,e){let a=await fetch("".concat(p,"/api/transactions/report"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify({reportType:t,filters:e})});if(!a.ok)throw Error((await a.json()).message||"Failed to generate transaction report");return a.json()}async searchTransactions(t,e){let a=new URLSearchParams({search:t});e&&Object.entries(e).forEach(t=>{let[e,r]=t;null!=r&&""!==r&&a.append(e,r.toString())});let r=await fetch("".concat(p,"/api/transactions/search?").concat(a),{headers:this.getAuthHeaders()});if(!r.ok)throw Error((await r.json()).message||"Failed to search transactions");return r.json()}async getTransactionsByReference(t){let e=await fetch("".concat(p,"/api/transactions/reference/").concat(t),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch transactions by reference");return e.json()}formatCurrency(t){return new Intl.NumberFormat("en-NG",{style:"currency",currency:"NGN"}).format(t)}getTransactionTypeIcon(t){return({DEPOSIT:"\uD83D\uDCB0",WITHDRAWAL:"\uD83C\uDFE6",CONTRIBUTION:"\uD83D\uDCC8",INTEREST:"\uD83D\uDC8E",PENALTY:"⚠️",REFUND:"↩️",TRANSFER:"\uD83D\uDD04",FEE:"\uD83D\uDCB3",BONUS:"\uD83C\uDF81"})[t]||"\uD83D\uDCB0"}getStatusColor(t){return({PENDING:"#F59E0B",PROCESSING:"#3B82F6",COMPLETED:"#10B981",FAILED:"#EF4444",CANCELLED:"#6B7280"})[t]||"#6B7280"}getTransactionTypeColor(t){return({DEPOSIT:"#10B981",WITHDRAWAL:"#EF4444",CONTRIBUTION:"#3B82F6",INTEREST:"#8B5CF6",PENALTY:"#F59E0B",REFUND:"#10B981",TRANSFER:"#6B7280",FEE:"#F59E0B",BONUS:"#10B981"})[t]||"#6B7280"}formatTransactionDescription(t){let{type:e,subType:a,description:r,plan:s,goal:o,target:n,group:i}=t;if(r)return r;switch(e){case"DEPOSIT":return"Account deposit";case"WITHDRAWAL":return s?"Withdrawal from ".concat(s.name):"Account withdrawal";case"CONTRIBUTION":if(s)return"Contribution to ".concat(s.name);if(o)return"Contribution to ".concat(o.title);if(n)return"Contribution to ".concat(n.title);if(i)return"Contribution to ".concat(i.name);return"Savings contribution";case"INTEREST":return s?"Interest earned on ".concat(s.name):"Interest earned";case"PENALTY":return"Penalty charge";case"REFUND":return"Refund processed";case"TRANSFER":return"Account transfer";case"FEE":return"Service fee";case"BONUS":return"Bonus credit";default:return"Transaction"}}generateReference(){let t=Date.now().toString(),e=Math.random().toString(36).substring(2,8);return"TXN_".concat(t,"_").concat(e).toUpperCase()}calculateRunningBalance(t){let e=0;return t.map(t=>{let a=0;switch(t.type){case"DEPOSIT":case"REFUND":case"INTEREST":case"BONUS":a=t.netAmount;break;case"WITHDRAWAL":case"CONTRIBUTION":case"PENALTY":case"FEE":a=-t.netAmount;break;case"TRANSFER":var r;a=(null==(r=t.metadata)?void 0:r.direction)==="IN"?t.netAmount:-t.netAmount}return e+=a,{...t,balanceAfter:e}})}}let E=new y,S="http://localhost:8080";class A{getAuthHeaders(){let t=localStorage.getItem("auth_token");return{"Content-Type":"application/json",...t&&{Authorization:"Bearer ".concat(t)}}}async getUserNotifications(t){let e=new URLSearchParams;t&&Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&""!==r&&e.append(a,r.toString())});let a=await fetch("".concat(S,"/notifications/user?").concat(e),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch user notifications");return a.json()}async getUnreadNotifications(){let t=await fetch("".concat(S,"/notifications/user/unread"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch unread notifications");return t.json()}async getUnreadCount(){let t=await fetch("".concat(S,"/notifications/user/unread/count"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch unread count");return t.json()}async markAsRead(t){let e=await fetch("".concat(S,"/notifications/").concat(t,"/read"),{method:"PUT",headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to mark notification as read");return e.json()}async markAllAsRead(){let t=await fetch("".concat(S,"/notifications/user/read-all"),{method:"PUT",headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to mark all notifications as read");return t.json()}async archiveNotification(t){let e=await fetch("".concat(S,"/notifications/").concat(t,"/archive"),{method:"PUT",headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to archive notification");return e.json()}async deleteNotification(t){let e=await fetch("".concat(S,"/notifications/").concat(t),{method:"DELETE",headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to delete notification")}async createNotification(t){let e=await fetch("".concat(S,"/notifications"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!e.ok)throw Error((await e.json()).message||"Failed to create notification");return e.json()}async sendBulkNotification(t){let e=await fetch("".concat(S,"/notifications/bulk"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!e.ok)throw Error((await e.json()).message||"Failed to send bulk notification");return e.json()}async getAllNotifications(t){let e=new URLSearchParams;t&&Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&""!==r&&e.append(a,r.toString())});let a=await fetch("".concat(S,"/notifications/all?").concat(e),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch all notifications");return a.json()}async getNotificationById(t){let e=await fetch("".concat(S,"/notifications/").concat(t),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch notification");return e.json()}async getNotificationPreferences(){let t=await fetch("".concat(S,"/notifications/preferences"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch notification preferences");return t.json()}async updateNotificationPreferences(t){let e=await fetch("".concat(S,"/notifications/preferences"),{method:"PUT",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!e.ok)throw Error((await e.json()).message||"Failed to update notification preferences");return e.json()}async updateChannelPreference(t,e){let a=await fetch("".concat(S,"/notifications/preferences/channel"),{method:"PUT",headers:this.getAuthHeaders(),body:JSON.stringify({notificationType:t,channels:e})});if(!a.ok)throw Error((await a.json()).message||"Failed to update channel preference");return a.json()}async getNotificationStats(t){let e=new URLSearchParams;t&&Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&""!==r&&e.append(a,r.toString())});let a=await fetch("".concat(S,"/notifications/stats?").concat(e),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch notification statistics");return a.json()}async getDeliveryStats(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"monthly",e=await fetch("".concat(S,"/notifications/delivery-stats?period=").concat(t),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch delivery statistics");return e.json()}async subscribeToNotifications(t){let e=localStorage.getItem("auth_token"),a=new EventSource("".concat(S,"/notifications/stream?token=").concat(e));return a.onmessage=e=>{try{let a=JSON.parse(e.data);t(a)}catch(t){console.error("Failed to parse notification:",t)}},a.onerror=t=>{console.error("Notification stream error:",t)},()=>{a.close()}}async registerPushToken(t,e){let a=await fetch("".concat(S,"/notifications/push/register"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify({token:t,platform:e})});if(!a.ok)throw Error((await a.json()).message||"Failed to register push token")}async unregisterPushToken(t){let e=await fetch("".concat(S,"/notifications/push/unregister"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify({token:t})});if(!e.ok)throw Error((await e.json()).message||"Failed to unregister push token")}getNotificationTypeIcon(t){return({DEPOSIT:"\uD83D\uDCB0",WITHDRAWAL:"\uD83C\uDFE6",CONTRIBUTION:"\uD83D\uDCC8",PLAN_CREATED:"\uD83D\uDCCB",PLAN_COMPLETED:"✅",PLAN_PAUSED:"⏸️",GOAL_ACHIEVED:"\uD83C\uDFAF",MILESTONE_REACHED:"\uD83C\uDFC6",PAYMENT_DUE:"⏰",PAYMENT_OVERDUE:"⚠️",KYC_APPROVED:"✅",KYC_REJECTED:"❌",GROUP_INVITATION:"\uD83D\uDC65",GROUP_PAYOUT:"\uD83D\uDCB8",SYSTEM:"⚙️",PROMOTIONAL:"\uD83C\uDF81",SECURITY:"\uD83D\uDD12",OTHER:"\uD83D\uDCE2"})[t]||"\uD83D\uDCE2"}getPriorityColor(t){return({LOW:"#10B981",MEDIUM:"#F59E0B",HIGH:"#EF4444",URGENT:"#DC2626"})[t]||"#6B7280"}formatTimeAgo(t){let e=new Date(t),a=Math.floor((new Date().getTime()-e.getTime())/1e3);if(a<60)return"Just now";if(a<3600){let t=Math.floor(a/60);return"".concat(t," minute").concat(t>1?"s":""," ago")}if(a<86400){let t=Math.floor(a/3600);return"".concat(t," hour").concat(t>1?"s":""," ago")}{if(!(a<604800))return e.toLocaleDateString();let t=Math.floor(a/86400);return"".concat(t," day").concat(t>1?"s":""," ago")}}truncateMessage(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100;return t.length<=e?t:t.substring(0,e)+"..."}groupNotificationsByDate(t){return t.reduce((t,e)=>{let a=new Date(e.createdAt).toDateString();return t[a]||(t[a]=[]),t[a].push(e),t},{})}}let j=new A,k="http://localhost:8080";class v{getAuthHeaders(){let t=localStorage.getItem("auth_token");return{"Content-Type":"application/json",...t&&{Authorization:"Bearer ".concat(t)}}}getFileUploadHeaders(){let t=localStorage.getItem("auth_token");return{...t&&{Authorization:"Bearer ".concat(t)}}}async getUserKYC(){let t=await fetch("".concat(k,"/kyc/user"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch user KYC");return t.json()}async createKYC(t){let e=await fetch("".concat(k,"/kyc"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!e.ok)throw Error((await e.json()).message||"Failed to create KYC");return e.json()}async updateKYC(t,e){let a=await fetch("".concat(k,"/kyc/").concat(t),{method:"PUT",headers:this.getAuthHeaders(),body:JSON.stringify(e)});if(!a.ok)throw Error((await a.json()).message||"Failed to update KYC");return a.json()}async submitKYC(t){let e=await fetch("".concat(k,"/kyc/").concat(t,"/submit"),{method:"POST",headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to submit KYC");return e.json()}async uploadDocument(t){let e=new FormData;e.append("file",t.file),e.append("type",t.type),e.append("name",t.name),t.description&&e.append("description",t.description),t.expiryDate&&e.append("expiryDate",t.expiryDate);let a=await fetch("".concat(k,"/kyc/").concat(t.kycId,"/documents"),{method:"POST",headers:this.getFileUploadHeaders(),body:e});if(!a.ok)throw Error((await a.json()).message||"Failed to upload document");return a.json()}async getDocuments(t){let e=await fetch("".concat(k,"/kyc/").concat(t,"/documents"),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch documents");return e.json()}async deleteDocument(t,e){let a=await fetch("".concat(k,"/kyc/").concat(t,"/documents/").concat(e),{method:"DELETE",headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to delete document")}async downloadDocument(t,e){let a=await fetch("".concat(k,"/kyc/").concat(t,"/documents/").concat(e,"/download"),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to download document");return a.blob()}async getAllKYC(t){let e=new URLSearchParams;t&&Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&""!==r&&e.append(a,r.toString())});let a=await fetch("".concat(k,"/kyc/all?").concat(e),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch all KYC records");return a.json()}async getKYCById(t){let e=await fetch("".concat(k,"/kyc/").concat(t),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch KYC");return e.json()}async reviewKYC(t){let e=await fetch("".concat(k,"/kyc/").concat(t.kycId),{method:"PATCH",headers:this.getAuthHeaders(),body:JSON.stringify({status:t.status,reviewNotes:t.reviewNotes,rejectionReason:t.rejectionReason,documentReviews:t.documentReviews})});if(!e.ok)throw Error((await e.json()).message||"Failed to review KYC");return e.json()}async approveKYC(t,e){return this.reviewKYC({kycId:t,status:"APPROVED",reviewNotes:e})}async rejectKYC(t,e,a){return this.reviewKYC({kycId:t,status:"REJECTED",rejectionReason:e,reviewNotes:a})}async assignReviewer(t,e){let a=await fetch("".concat(k,"/kyc/").concat(t,"/assign"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify({reviewerId:e})});if(!a.ok)throw Error((await a.json()).message||"Failed to assign reviewer");return a.json()}async getKYCStats(t){let e=new URLSearchParams;t&&Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&""!==r&&e.append(a,r.toString())});let a=await fetch("".concat(k,"/kyc/stats?").concat(e),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch KYC statistics");return a.json()}async getKYCAnalytics(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"monthly",e=await fetch("".concat(k,"/kyc/analytics?period=").concat(t),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch KYC analytics");return e.json()}async getKYCRequirements(t){let e=await fetch("".concat(k,"/kyc/requirements/").concat(t),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch KYC requirements");return e.json()}async getAllKYCRequirements(){let t=await fetch("".concat(k,"/kyc/requirements"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch all KYC requirements");return t.json()}getStatusColor(t){return({PENDING:"#F59E0B",UNDER_REVIEW:"#3B82F6",APPROVED:"#10B981",REJECTED:"#EF4444",EXPIRED:"#6B7280"})[t]||"#6B7280"}getLevelColor(t){return({BASIC:"#10B981",INTERMEDIATE:"#F59E0B",ADVANCED:"#8B5CF6"})[t]||"#6B7280"}getDocumentTypeIcon(t){return({NATIONAL_ID:"\uD83C\uDD94",PASSPORT:"\uD83D\uDCD8",DRIVERS_LICENSE:"\uD83D\uDE97",VOTERS_CARD:"\uD83D\uDDF3️",UTILITY_BILL:"\uD83D\uDCC4",BANK_STATEMENT:"\uD83C\uDFE6",SELFIE:"\uD83E\uDD33",SIGNATURE:"✍️",OTHER:"\uD83D\uDCCE"})[t]||"\uD83D\uDCCE"}validateFileSize(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5;return t.size>1024*e*1024?{isValid:!1,error:"File size must be less than ".concat(e,"MB")}:{isValid:!0}}validateFileType(t,e){return e.includes(t.type)?{isValid:!0}:{isValid:!1,error:"File type ".concat(t.type," is not allowed. Allowed types: ").concat(e.join(", "))}}formatFileSize(t){if(0===t)return"0 Bytes";let e=Math.floor(Math.log(t)/Math.log(1024));return parseFloat((t/Math.pow(1024,e)).toFixed(2))+" "+["Bytes","KB","MB","GB"][e]}calculateKYCProgress(t,e){let a=e.requiredDocuments.filter(t=>t.isRequired),r=t.documents.filter(t=>"REJECTED"!==t.status),s=!!(t.personalInfo.firstName&&t.personalInfo.lastName&&t.personalInfo.dateOfBirth&&t.personalInfo.nationality),o=!!(t.addressInfo.street&&t.addressInfo.city&&t.addressInfo.state&&t.addressInfo.country);return[s,o,!!(t.contactInfo.phoneNumber&&t.contactInfo.email),r.length>=a.length].filter(Boolean).length/4*100}getKYCLevelBenefits(t){return({BASIC:["Basic account access","Limited transaction amounts","Basic savings plans"],INTERMEDIATE:["Increased transaction limits","Access to group savings","Target savings features","Basic withdrawal options"],ADVANCED:["Full platform access","Unlimited transactions","Premium features","Priority support","Advanced analytics"]})[t]||[]}}let T=new v,N="http://localhost:8080";class F{getAuthHeaders(){let t=localStorage.getItem("auth_token");return{"Content-Type":"application/json",...t&&{Authorization:"Bearer ".concat(t)}}}async getDashboardStats(t){let e=new URLSearchParams;t&&Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&""!==r&&e.append(a,r.toString())});let a=await fetch("".concat(N,"/api/admin/dashboard/stats?").concat(e),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch dashboard statistics");return a.json()}async getAnalytics(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"monthly",e=await fetch("".concat(N,"/api/admin/analytics?period=").concat(t),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch analytics");return e.json()}async getRevenueAnalytics(t){let e=new URLSearchParams;t&&Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&""!==r&&e.append(a,r.toString())});let a=await fetch("".concat(N,"/api/admin/analytics/revenue?").concat(e),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch revenue analytics");return a.json()}async getAllUsers(t){let e=new URLSearchParams;t&&Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&""!==r&&e.append(a,r.toString())});let a=await fetch("".concat(N,"/api/auth/users?").concat(e),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch users");let r=await a.json();return(console.log("Raw /api/auth/users response:",r),Array.isArray(r))?{users:r,total:r.length,page:1,limit:(null==t?void 0:t.limit)||20,totalPages:1}:{users:r.users||[],total:r.totalUsers||0,page:r.currentPage||1,limit:(null==t?void 0:t.limit)||20,totalPages:r.totalPages||0}}async getUserDetails(t){let e=await fetch("".concat(N,"/api/admin/users/").concat(t),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch user details");return e.json()}async updateUserStatus(t,e,a){let r=await fetch("".concat(N,"/api/admin/users/").concat(t,"/status"),{method:"PATCH",headers:this.getAuthHeaders(),body:JSON.stringify({isActive:e,reason:a})});if(!r.ok)throw Error((await r.json()).message||"Failed to update user status")}async resetUserPassword(t){let e=await fetch("".concat(N,"/api/admin/users/").concat(t,"/reset-password"),{method:"POST",headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to reset user password");return e.json()}async getAllSavingsPlans(t){let e=new URLSearchParams;t&&Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&""!==r&&e.append(a,r.toString())});let a=await fetch("".concat(N,"/api/admin/savings/plans?").concat(e),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch savings plans");return a.json()}async createSavingsPlan(t){let e=await fetch("".concat(N,"/api/admin/savings/plans"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!e.ok)throw Error((await e.json()).message||"Failed to create savings plan");return e.json()}async updateSavingsPlan(t,e){let a=await fetch("".concat(N,"/api/admin/savings/plans/").concat(t),{method:"PUT",headers:this.getAuthHeaders(),body:JSON.stringify(e)});if(!a.ok)throw Error((await a.json()).message||"Failed to update savings plan");return a.json()}async deleteSavingsPlan(t){let e=await fetch("".concat(N,"/api/admin/savings/plans/").concat(t),{method:"DELETE",headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to delete savings plan")}async getAllWithdrawals(t){let e=new URLSearchParams;t&&Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&""!==r&&e.append(a,r.toString())});let a=await fetch("".concat(N,"/api/admin/withdrawals?").concat(e),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch withdrawals");return a.json()}async approveWithdrawal(t,e){let a=await fetch("".concat(N,"/api/admin/withdrawals/").concat(t,"/approve"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify({notes:e})});if(!a.ok)throw Error((await a.json()).message||"Failed to approve withdrawal");return a.json()}async rejectWithdrawal(t,e,a){let r=await fetch("".concat(N,"/api/admin/withdrawals/").concat(t,"/reject"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify({reason:e,notes:a})});if(!r.ok)throw Error((await r.json()).message||"Failed to reject withdrawal");return r.json()}async getAllKYC(t){let e=new URLSearchParams;t&&Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&""!==r&&e.append(a,r.toString())});let a=await fetch("".concat(N,"/api/admin/kyc?").concat(e),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch KYC records");return a.json()}async reviewKYC(t,e,a,r){let s=await fetch("".concat(N,"/api/admin/kyc/").concat(t,"/review"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify({status:e,notes:a,reason:r})});if(!s.ok)throw Error((await s.json()).message||"Failed to review KYC");return s.json()}async getSettings(){let t=await fetch("".concat(N,"/api/admin/settings"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch settings");return t.json()}async updateSetting(t,e){let a=await fetch("".concat(N,"/api/admin/settings/").concat(t),{method:"PUT",headers:this.getAuthHeaders(),body:JSON.stringify({value:e})});if(!a.ok)throw Error((await a.json()).message||"Failed to update setting");return a.json()}async bulkUpdateSettings(t){let e=await fetch("".concat(N,"/api/admin/settings/bulk"),{method:"PUT",headers:this.getAuthHeaders(),body:JSON.stringify({settings:t})});if(!e.ok)throw Error((await e.json()).message||"Failed to update settings");return e.json()}async getSystemHealth(){let t=await fetch("".concat(N,"/api/admin/system/health"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch system health");return t.json()}async getSystemLogs(t){let e=new URLSearchParams;t&&Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&""!==r&&e.append(a,r.toString())});let a=await fetch("".concat(N,"/api/admin/system/logs?").concat(e),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch system logs");return a.json()}async getAdminActivity(t){let e=new URLSearchParams;t&&Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&""!==r&&e.append(a,r.toString())});let a=await fetch("".concat(N,"/api/admin/activity?").concat(e),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch admin activity");return a.json()}async logAdminActivity(t,e,a,r){let s=await fetch("".concat(N,"/api/admin/activity"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify({action:t,resource:e,resourceId:a,metadata:r})});if(!s.ok)throw Error((await s.json()).message||"Failed to log admin activity")}async generateReport(t){let e=await fetch("".concat(N,"/api/admin/reports"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!e.ok)throw Error((await e.json()).message||"Failed to generate report");return e.json()}async getReports(t){let e=new URLSearchParams;t&&Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&""!==r&&e.append(a,r.toString())});let a=await fetch("".concat(N,"/api/admin/reports?").concat(e),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch reports");return a.json()}async downloadReport(t){let e=await fetch("".concat(N,"/api/admin/reports/").concat(t,"/download"),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to download report");return e.blob()}formatCurrency(t){return new Intl.NumberFormat("en-NG",{style:"currency",currency:"NGN"}).format(t)}formatPercentage(t){return"".concat(t.toFixed(2),"%")}calculateGrowthRate(t,e){return 0===e?100*(t>0):(t-e)/e*100}getStatusColor(t){return({ACTIVE:"#10B981",INACTIVE:"#6B7280",PENDING:"#F59E0B",APPROVED:"#10B981",REJECTED:"#EF4444",COMPLETED:"#10B981",FAILED:"#EF4444",CANCELLED:"#6B7280"})[t]||"#6B7280"}exportToCSV(t,e){let a=new Blob([this.convertToCSV(t)],{type:"text/csv;charset=utf-8;"}),r=document.createElement("a");if(void 0!==r.download){let t=URL.createObjectURL(a);r.setAttribute("href",t),r.setAttribute("download",e),r.style.visibility="hidden",document.body.appendChild(r),r.click(),document.body.removeChild(r)}}convertToCSV(t){if(!t.length)return"";let e=Object.keys(t[0]);return[e.join(","),...t.map(t=>e.map(e=>{let a=t[e];return"string"==typeof a?'"'.concat(a.replace(/"/g,'""'),'"'):a}).join(","))].join("\n")}}let O=new F,b="http://localhost:8080";class P{getAuthHeaders(){let t=localStorage.getItem("auth_token");return{"Content-Type":"application/json",...t&&{Authorization:"Bearer ".concat(t)}}}async getAllSettings(){let t=await fetch("".concat(b,"/settings"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch settings");return t.json()}async getSettingsByCategory(t){let e=await fetch("".concat(b,"/settings/category/").concat(t),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch settings by category");return e.json()}async getSettingByKey(t){let e=await fetch("".concat(b,"/settings/").concat(t),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch setting");return e.json()}async updateSetting(t){let e=await fetch("".concat(b,"/settings/").concat(t.key),{method:"PUT",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!e.ok)throw Error((await e.json()).message||"Failed to update setting");return e.json()}async bulkUpdateSettings(t){let e=await fetch("".concat(b,"/settings/bulk"),{method:"PUT",headers:this.getAuthHeaders(),body:JSON.stringify({settings:t})});if(!e.ok)throw Error((await e.json()).message||"Failed to bulk update settings");return e.json()}async resetSetting(t){let e=await fetch("".concat(b,"/settings/").concat(t,"/reset"),{method:"POST",headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to reset setting");return e.json()}async getFinancialSettings(){let t=await fetch("".concat(b,"/settings/financial"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch financial settings");return t.json()}async getSecuritySettings(){let t=await fetch("".concat(b,"/settings/security"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch security settings");return t.json()}async getNotificationSettings(){let t=await fetch("".concat(b,"/settings/notifications"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch notification settings");return t.json()}async getFeatureSettings(){let t=await fetch("".concat(b,"/settings/features"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch feature settings");return t.json()}async getSettingsCategories(){let t=await fetch("".concat(b,"/settings/categories"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch settings categories");return t.json()}async validateSettings(t){let e=await fetch("".concat(b,"/settings/validate"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify({settings:t})});if(!e.ok)throw Error((await e.json()).message||"Failed to validate settings");return e.json()}async validateSetting(t,e){let a=await fetch("".concat(b,"/settings/").concat(t,"/validate"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify({value:e})});if(!a.ok)throw Error((await a.json()).message||"Failed to validate setting");return a.json()}async createBackup(t){let e=await fetch("".concat(b,"/settings/backup"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!e.ok)throw Error((await e.json()).message||"Failed to create settings backup");return e.json()}async getBackups(){let t=await fetch("".concat(b,"/settings/backups"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch settings backups");return t.json()}async restoreBackup(t){let e=await fetch("".concat(b,"/settings/restore"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!e.ok)throw Error((await e.json()).message||"Failed to restore settings backup");return e.json()}async deleteBackup(t){let e=await fetch("".concat(b,"/settings/backups/").concat(t),{method:"DELETE",headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to delete settings backup")}async getPublicSettings(){let t=await fetch("".concat(b,"/settings/public"),{headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch public settings");return t.json()}async exportSettings(t){let e=new URLSearchParams;t&&t.forEach(t=>e.append("categories",t));let a=await fetch("".concat(b,"/settings/export?").concat(e),{method:"POST",headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to export settings");return a.json()}async importSettings(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=new FormData;a.append("file",t),a.append("overwrite",e.toString());let r=localStorage.getItem("auth_token"),s=await fetch("".concat(b,"/settings/import"),{method:"POST",headers:{...r&&{Authorization:"Bearer ".concat(r)}},body:a});if(!s.ok)throw Error((await s.json()).message||"Failed to import settings");return s.json()}formatSettingValue(t){switch(t.type){case"BOOLEAN":return t.value?"Enabled":"Disabled";case"NUMBER":if(t.key.includes("rate")||t.key.includes("percentage"))return"".concat(t.value,"%");if(t.key.includes("amount")||t.key.includes("fee"))return this.formatCurrency(t.value);return t.value.toString();case"STRING":return t.value;case"JSON":case"ARRAY":return JSON.stringify(t.value);default:var e;return(null==(e=t.value)?void 0:e.toString())||""}}formatCurrency(t){return new Intl.NumberFormat("en-NG",{style:"currency",currency:"NGN"}).format(t)}getCategoryIcon(t){return({GENERAL:"⚙️",FINANCIAL:"\uD83D\uDCB0",SECURITY:"\uD83D\uDD12",NOTIFICATIONS:"\uD83D\uDD14",FEATURES:"\uD83C\uDF9B️",LIMITS:"\uD83D\uDCCA",PENALTIES:"⚠️"})[t]||"⚙️"}getCategoryColor(t){return({GENERAL:"#6B7280",FINANCIAL:"#10B981",SECURITY:"#EF4444",NOTIFICATIONS:"#3B82F6",FEATURES:"#8B5CF6",LIMITS:"#F59E0B",PENALTIES:"#DC2626"})[t]||"#6B7280"}validateSettingValue(t,e){let a=t.validationRules;if(!a)return{isValid:!0};if(a.required&&(null==e||""===e))return{isValid:!1,error:"This field is required"};switch(t.type){case"NUMBER":let r=Number(e);if(isNaN(r))return{isValid:!1,error:"Must be a valid number"};if(void 0!==a.min&&r<a.min)return{isValid:!1,error:"Must be at least ".concat(a.min)};if(void 0!==a.max&&r>a.max)return{isValid:!1,error:"Must be at most ".concat(a.max)};break;case"STRING":let s=String(e);if(a.pattern&&!new RegExp(a.pattern).test(s))return{isValid:!1,error:"Invalid format"};if(a.options&&!a.options.includes(s))return{isValid:!1,error:"Must be one of: ".concat(a.options.join(", "))};break;case"BOOLEAN":if("boolean"!=typeof e)return{isValid:!1,error:"Must be true or false"}}return{isValid:!0}}groupSettingsByCategory(t){return t.reduce((t,e)=>{let a=e.category;return t[a]||(t[a]=[]),t[a].push(e),t},{})}sortSettingsByDisplayOrder(t){return t.sort((t,e)=>t.displayOrder-e.displayOrder)}}new P},41277:(t,e,a)=>{a.d(e,{T:()=>o});let r="http://localhost:8080";class s{getAuthHeaders(){let t=localStorage.getItem("auth_token");return{"Content-Type":"application/json",...t&&{Authorization:"Bearer ".concat(t)}}}async getSavingsPlans(){let t=await fetch("".concat(r,"/savings"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error("Failed to fetch savings plans");return t.json()}async getUserSavingsPlans(){let t=await fetch("".concat(r,"/api/savings/my"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error("Failed to fetch user savings plans");return(await t.json()).plans||[]}async getSavingsPlan(t){let e=await fetch("".concat(r,"/savings/").concat(t),{headers:this.getAuthHeaders()});if(!e.ok)throw Error("Failed to fetch savings plan");return e.json()}async joinSavingsPlan(t){let e=await fetch("".concat(r,"/savings/join"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify({planId:t})});if(!e.ok)throw Error((await e.json()).message||"Failed to join savings plan");return e.json()}async createSavingsPlan(t){let e=await fetch("".concat(r,"/savings/create"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!e.ok)throw Error((await e.json()).message||"Failed to create savings plan");return e.json()}async updateSavingsPlan(t,e){let a=await fetch("".concat(r,"/savings/").concat(t),{method:"PUT",headers:this.getAuthHeaders(),body:JSON.stringify(e)});if(!a.ok)throw Error((await a.json()).message||"Failed to update savings plan");return a.json()}async pauseSavingsPlan(t){if(!(await fetch("".concat(r,"/savings/plans/").concat(t,"/pause"),{method:"POST",headers:this.getAuthHeaders()})).ok)throw Error("Failed to pause savings plan")}async resumeSavingsPlan(t){if(!(await fetch("".concat(r,"/savings/plans/").concat(t,"/resume"),{method:"POST",headers:this.getAuthHeaders()})).ok)throw Error("Failed to resume savings plan")}async deleteSavingsPlan(t){if(!(await fetch("".concat(r,"/savings/").concat(t),{method:"DELETE",headers:this.getAuthHeaders()})).ok)throw Error("Failed to delete savings plan")}async getSavingsGoals(){let t=await fetch("".concat(r,"/savings/goals"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error("Failed to fetch savings goals");return t.json()}async getSavingsGoal(t){let e=await fetch("".concat(r,"/savings/goals/").concat(t),{headers:this.getAuthHeaders()});if(!e.ok)throw Error("Failed to fetch savings goal");return e.json()}async createSavingsGoal(t){let e=await fetch("".concat(r,"/savings/goals"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!e.ok)throw Error((await e.json()).message||"Failed to create savings goal");return e.json()}async updateSavingsGoal(t,e){let a=await fetch("".concat(r,"/savings/goals/").concat(t),{method:"PUT",headers:this.getAuthHeaders(),body:JSON.stringify(e)});if(!a.ok)throw Error((await a.json()).message||"Failed to update savings goal");return a.json()}async deleteSavingsGoal(t){if(!(await fetch("".concat(r,"/savings/goals/").concat(t),{method:"DELETE",headers:this.getAuthHeaders()})).ok)throw Error("Failed to delete savings goal")}async getSavingsTransactions(t,e){let a=new URLSearchParams;t&&a.append("planId",t),e&&a.append("goalId",e);let s=await fetch("".concat(r,"/savings/transactions?").concat(a),{headers:this.getAuthHeaders()});if(!s.ok)throw Error("Failed to fetch transactions");return s.json()}async makeContribution(t,e){let a=await fetch("".concat(r,"/savings/plans/").concat(t,"/contribute"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify({amount:e})});if(!a.ok)throw Error((await a.json()).message||"Failed to make contribution");return a.json()}async makeGoalContribution(t,e){let a=await fetch("".concat(r,"/savings/goals/").concat(t,"/contribute"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify({amount:e})});if(!a.ok)throw Error((await a.json()).message||"Failed to make goal contribution");return a.json()}async getSavingsStats(){let t=await fetch("".concat(r,"/savings/stats"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error("Failed to fetch savings statistics");return t.json()}async calculateInterest(t,e,a,s){let o=await fetch("".concat(r,"/savings/calculate-interest"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify({principal:t,rate:e,time:a,frequency:s})});if(!o.ok)throw Error("Failed to calculate interest");return o.json()}calculateCompoundInterest(t,e,a){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:12;return t*Math.pow(1+e/100/r,a/12*r)}calculateMonthlyContribution(t,e,a){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,s=t-e;if(0===r)return s/a;let o=r/100/12;return s*o/(Math.pow(1+o,a)-1)}formatCurrency(t){return new Intl.NumberFormat("en-NG",{style:"currency",currency:"NGN"}).format(t)}calculateProgress(t,e){return Math.min(t/e*100,100)}}let o=new s},43851:(t,e,a)=>{a.d(e,{K:()=>o});var r=a(49509);class s{getHeaders(){return{Authorization:"Bearer ".concat(this.config.secretKey),"Content-Type":"application/json"}}async makeRequest(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"GET",a=arguments.length>2?arguments[2]:void 0,r="".concat(this.config.baseUrl).concat(t),s={method:e,headers:this.getHeaders()};a&&("POST"===e||"PUT"===e)&&(s.body=JSON.stringify(a));try{let t=await fetch(r,s),e=await t.json();if(!t.ok)throw Error(e.message||"HTTP ".concat(t.status,": ").concat(t.statusText));return e}catch(a){throw console.error("Paystack API Error (".concat(e," ").concat(t,"):"),a),a}}async getBankList(){return this.makeRequest("/bank")}async verifyAccountNumber(t,e){let a=await fetch("".concat("http://localhost:8080","/api/paystack/resolve-account"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({account_number:t,bank_code:e})});return await a.json()}async createTransferRecipient(t){return this.makeRequest("/transferrecipient","POST",t)}async getTransferRecipient(t){return this.makeRequest("/transferrecipient/".concat(t))}async updateTransferRecipient(t,e){return this.makeRequest("/transferrecipient/".concat(t),"PUT",e)}async deleteTransferRecipient(t){return this.makeRequest("/transferrecipient/".concat(t),"DELETE")}async initiateTransfer(t){return this.makeRequest("/transfer","POST",t)}async getTransfer(t){return this.makeRequest("/transfer/".concat(t))}async verifyTransfer(t){return this.makeRequest("/transfer/verify/".concat(t))}async getBalance(){return this.makeRequest("/balance")}convertToKobo(t){return Math.round(100*t)}convertFromKobo(t){return t/100}generateReference(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"AUTO",e=Date.now(),a=Math.random().toString(36).substring(2,8).toUpperCase();return"".concat(t,"-").concat(e,"-").concat(a)}validateBankAccount(t){let e=[];return t.accountNumber&&/^\d{10}$/.test(t.accountNumber)||e.push({code:"INVALID_ACCOUNT_NUMBER",message:"Account number must be exactly 10 digits"}),(!t.bankCode||t.bankCode.length<3)&&e.push({code:"INVALID_BANK_CODE",message:"Bank code is required"}),(!t.accountName||t.accountName.trim().length<2)&&e.push({code:"INVALID_ACCOUNT_NAME",message:"Account name must be at least 2 characters"}),{isValid:0===e.length,errors:e}}validateTransferAmount(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"NGN",a=[],r="NGN"===e?100:1,s="NGN"===e?1e7:1e5;return t<r&&a.push({code:"AMOUNT_TOO_LOW",message:"Minimum transfer amount is ".concat("NGN"===e?"₦":"$").concat(r)}),t>s&&a.push({code:"AMOUNT_TOO_HIGH",message:"Maximum transfer amount is ".concat("NGN"===e?"₦":"$").concat(s.toLocaleString())}),{isValid:0===a.length,errors:a}}handlePaystackError(t){var e,a;return(null==(a=t.response)||null==(e=a.data)?void 0:e.message)?{code:"PAYSTACK_API_ERROR",message:t.response.data.message,details:t.response.data}:t.message?{code:"PAYSTACK_ERROR",message:t.message}:{code:"UNKNOWN_ERROR",message:"An unknown error occurred while processing the request"}}verifyWebhookSignature(t,e){return a(8777).createHmac("sha512",this.config.secretKey).update(t,"utf8").digest("hex")===e}constructor(){this.config={publicKey:"pk_test_44f6dbb6159b73b84fff2fc441dcdd997e15c10d",secretKey:r.env.PAYSTACK_SECRET_KEY||"",baseUrl:"https://api.paystack.co"}}}let o=new s},46142:(t,e,a)=>{a.d(e,{u:()=>o});let r="http://localhost:8080";class s{getAuthHeaders(){let t=localStorage.getItem("auth_token");return{"Content-Type":"application/json",...t&&{Authorization:"Bearer ".concat(t)}}}async initiateWithdrawal(t){let e=await fetch("".concat(r,"/api/paystack/transfer"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!e.ok){let t;try{t=await e.json()}catch(a){t={message:"Failed to initiate withdrawal",details:await e.text()}}throw Error(t.message||"Failed to initiate withdrawal")}return e.json()}async initiatePlanClosure(t){let e;console.log("[initiatePlanClosure] Sending data:",t);let a="".concat(r,"/api/withdraw/plan");console.log("[initiatePlanClosure] POST URL:",a);let s=await fetch(a,{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(t)});try{e=await s.json()}catch(t){e=null}if(console.log("[initiatePlanClosure] Response status:",s.status),console.log("[initiatePlanClosure] Response body:",e),!s.ok)throw Error(e&&e.message||e&&e.error||"Failed to close plan");return e}async getUserWithdrawals(t){let e=new URLSearchParams;t&&Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&""!==r&&e.append(a,r.toString())});let a=await fetch("".concat(r,"/withdraw/user/").concat(localStorage.getItem("user_id"),"?").concat(e),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch user withdrawals");return a.json()}async getWithdrawalById(t){let e=await fetch("".concat(r,"/withdraw/").concat(t),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch withdrawal");return e.json()}async cancelWithdrawal(t){let e=await fetch("".concat(r,"/withdraw/").concat(t,"/cancel"),{method:"POST",headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to cancel withdrawal");return e.json()}async getAllWithdrawals(t){let e=new URLSearchParams;t&&Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&""!==r&&e.append(a,r.toString())});let a=await fetch("".concat(r,"/withdraw/all?").concat(e),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch all withdrawals");return a.json()}async approveWithdrawal(t){let e=await fetch("".concat(r,"/withdraw/").concat(t.withdrawalId),{method:"PATCH",headers:this.getAuthHeaders(),body:JSON.stringify({status:"APPROVED",adminNotes:t.adminNotes,adjustedAmount:t.adjustedAmount})});if(!e.ok)throw Error((await e.json()).message||"Failed to approve withdrawal");return e.json()}async rejectWithdrawal(t){let e=await fetch("".concat(r,"/withdraw/").concat(t.withdrawalId),{method:"PATCH",headers:this.getAuthHeaders(),body:JSON.stringify({status:"REJECTED",rejectionReason:t.rejectionReason,adminNotes:t.adminNotes})});if(!e.ok)throw Error((await e.json()).message||"Failed to reject withdrawal");return e.json()}async updateWithdrawalStatus(t,e,a){let s=await fetch("".concat(r,"/withdraw/").concat(t,"/status"),{method:"PUT",headers:this.getAuthHeaders(),body:JSON.stringify({status:e,notes:a})});if(!s.ok)throw Error((await s.json()).message||"Failed to update withdrawal status");return s.json()}async getWithdrawalStats(t){let e=new URLSearchParams;t&&Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&""!==r&&e.append(a,r.toString())});let a=await fetch("".concat(r,"/withdraw/stats?").concat(e),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch withdrawal statistics");return a.json()}async getPenaltySummary(t){let e=new URLSearchParams;t&&Object.entries(t).forEach(t=>{let[a,r]=t;null!=r&&""!==r&&e.append(a,r.toString())});let a=await fetch("".concat(r,"/withdraw/penalty-summary?").concat(e),{headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch penalty summary");return a.json()}async handlePaystackWebhook(t){let e=await fetch("".concat(r,"/withdraw/webhook"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!e.ok)throw Error((await e.json()).message||"Failed to handle webhook")}calculatePenalty(t,e,a){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:5,s=0,o="";return a>0&&(s=t*r/100,o="Early withdrawal penalty (".concat(a," days before maturity)")),{penaltyAmount:Math.round(s),netAmount:t-Math.round(s),reason:o}}calculateWithdrawalFees(t,e){let a={PLAN_CLOSURE:{percentage:0,fixed:0},EXTERNAL_PAYOUT:{percentage:1,fixed:100,cap:1e3}},r=a[e]||a.EXTERNAL_PAYOUT,s=t*r.percentage/100+r.fixed;return r.cap&&s>r.cap&&(s=r.cap),{fees:Math.round(s),netAmount:t-Math.round(s)}}formatCurrency(t){return new Intl.NumberFormat("en-NG",{style:"currency",currency:"NGN"}).format(t)}getStatusColor(t){return({PENDING:"#F59E0B",PROCESSING:"#3B82F6",APPROVED:"#10B981",COMPLETED:"#10B981",FAILED:"#EF4444",CANCELLED:"#6B7280",REJECTED:"#EF4444"})[t]||"#6B7280"}getTypeIcon(t){return({PLAN_CLOSURE:"\uD83D\uDCE6",EXTERNAL_PAYOUT:"\uD83C\uDFE6"})[t]||"\uD83D\uDCB0"}generateReference(){let t=Date.now().toString(),e=Math.random().toString(36).substring(2,8);return"WTH_".concat(t,"_").concat(e).toUpperCase()}validateWithdrawalAmount(t,e,a){let r="PLAN_CLOSURE"===a?e:1e6;return t<1e3?{isValid:!1,error:"Minimum withdrawal amount is ".concat(this.formatCurrency(1e3))}:t>r?{isValid:!1,error:"Maximum withdrawal amount is ".concat(this.formatCurrency(r))}:t>e?{isValid:!1,error:"Insufficient balance"}:{isValid:!0}}estimateProcessingTime(t,e){return"PLAN_CLOSURE"===t?"Instant":e>1e5?"1-3 business days":"24 hours"}}let o=new s},59972:(t,e,a)=>{a.d(e,{I:()=>o});let r="http://localhost:8080";class s{getAuthHeaders(){let t=localStorage.getItem("auth_token");return{"Content-Type":"application/json",...t&&{Authorization:"Bearer ".concat(t)}}}async getAllGroups(){let t="".concat(r,"/api/group-savings/group-plans/public");console.log("[GroupSavingsService] GET",t);try{let e=await fetch(t,{headers:this.getAuthHeaders()});console.log("[GroupSavingsService] Response status:",e.status);let a=await e.text();try{let t=JSON.parse(a);if(!e.ok)throw console.error("[GroupSavingsService] Error response:",t),Error(t.message||"Failed to fetch group savings");return t}catch(t){throw console.error("[GroupSavingsService] JSON parse error:",t,"Raw response:",a),Error("Invalid response from server.")}}catch(t){throw console.error("[GroupSavingsService] getAllGroups error:",t),t}}async getUserGroups(){let t="".concat(r,"/api/group-savings/group-plans/my");console.log("[GroupSavingsService] GET",t);try{let e=await fetch(t,{headers:this.getAuthHeaders()});console.log("[GroupSavingsService] Response status:",e.status);let a=await e.text();try{let t=JSON.parse(a);if(!e.ok)throw console.error("[GroupSavingsService] Error response:",t),Error(t.message||"Failed to fetch user groups");return t}catch(t){throw console.error("[GroupSavingsService] JSON parse error:",t,"Raw response:",a),Error("Invalid response from server.")}}catch(t){throw console.error("[GroupSavingsService] getUserGroups error:",t),t}}async getGroupById(t){let e="".concat(r,"/api/group-savings/group-plans/").concat(t);console.log("[GroupSavingsService] GET",e);try{let t=await fetch(e,{headers:this.getAuthHeaders()});console.log("[GroupSavingsService] Response status:",t.status);let a=await t.text();try{let e=JSON.parse(a);if(!t.ok)throw console.error("[GroupSavingsService] Error response:",e),Error(e.message||"Failed to fetch group details");return e}catch(t){throw console.error("[GroupSavingsService] JSON parse error:",t,"Raw response:",a),Error("Invalid response from server.")}}catch(t){throw console.error("[GroupSavingsService] getGroupById error:",t),t}}async createGroup(t){let e=t.frequency;"string"==typeof e&&(e=e.toLowerCase().includes("bi-week")?"WEEKLY":e.charAt(0).toUpperCase()+e.slice(1).toLowerCase());let a={title:t.name,depositFrequency:e,depositAmount:t.contributionAmount,targetDate:t.startDate,targetAmount:t.targetAmount,isPublic:t.isPublic,maxMembers:t.maxMembers,description:t.description,duration:t.duration,category:t.category},s="".concat(r,"/api/group-savings/group-plan");console.log("[GroupSavingsService] POST",s,"Payload:",a);try{let t=await fetch(s,{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(a)});console.log("[GroupSavingsService] Response status:",t.status);let e=await t.text();try{let a=JSON.parse(e);if(!t.ok)throw console.error("[GroupSavingsService] Error response:",a),Error(a.message||"Failed to create group");return a}catch(t){throw console.error("[GroupSavingsService] JSON parse error:",t,"Raw response:",e),Error("Invalid response from server.")}}catch(t){throw console.error("[GroupSavingsService] createGroup error:",t),t}}async updateGroup(t,e){let a="".concat(r,"/api/group-savings/").concat(t);console.log("[GroupSavingsService] PUT",a,"Payload:",e);try{let t=await fetch(a,{method:"PUT",headers:this.getAuthHeaders(),body:JSON.stringify(e)});console.log("[GroupSavingsService] Response status:",t.status);let r=await t.text();try{let e=JSON.parse(r);if(!t.ok)throw console.error("[GroupSavingsService] Error response:",e),Error(e.message||"Failed to update group");return e}catch(t){throw console.error("[GroupSavingsService] JSON parse error:",t,"Raw response:",r),Error("Invalid response from server.")}}catch(t){throw console.error("[GroupSavingsService] updateGroup error:",t),t}}async deleteGroup(t){let e="".concat(r,"/api/group-savings/").concat(t);console.log("[GroupSavingsService] DELETE",e);try{let t=await fetch(e,{method:"DELETE",headers:this.getAuthHeaders()});if(console.log("[GroupSavingsService] Response status:",t.status),!t.ok){let e;try{e=await t.json()}catch(t){throw console.error("[GroupSavingsService] JSON parse error:",t),Error("Invalid response from server.")}throw console.error("[GroupSavingsService] Error response:",e),Error(e.message||"Failed to delete group")}}catch(t){throw console.error("[GroupSavingsService] deleteGroup error:",t),t}}async joinGroup(t){let e=await fetch("".concat(r,"/api/group-savings/join"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!e.ok)throw Error((await e.json()).message||"Failed to join group");return e.json()}async leaveGroup(t){let e=await fetch("".concat(r,"/api/group-savings/").concat(t,"/leave"),{method:"POST",headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to leave group")}async inviteMember(t){let e=await fetch("".concat(r,"/api/group-savings/invite"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!e.ok)throw Error((await e.json()).message||"Failed to send invitation")}async removeMember(t,e){let a=await fetch("".concat(r,"/api/group-savings/").concat(t,"/members/").concat(e),{method:"DELETE",headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to remove member")}async updateMemberRole(t,e,a){let s=await fetch("".concat(r,"/api/group-savings/").concat(t,"/members/").concat(e,"/role"),{method:"PATCH",headers:this.getAuthHeaders(),body:JSON.stringify({role:a})});if(!s.ok)throw Error((await s.json()).message||"Failed to update member role")}async makeContribution(t){let e=await fetch("".concat(r,"/api/group-savings/contribute"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!e.ok)throw Error((await e.json()).message||"Failed to make contribution");return e.json()}async getGroupContributions(t,e){let a=new URLSearchParams;e&&Object.entries(e).forEach(t=>{let[e,r]=t;null!=r&&""!==r&&a.append(e,r.toString())});let s=await fetch("".concat(r,"/api/group-savings/").concat(t,"/contributions?").concat(a),{headers:this.getAuthHeaders()});if(!s.ok)throw Error((await s.json()).message||"Failed to fetch contributions");return s.json()}async getUserContributions(t){let e=await fetch("".concat(r,"/api/group-savings/").concat(t,"/contributions/user"),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch user contributions");return e.json()}async getPayoutSchedule(t){let e=await fetch("".concat(r,"/api/group-savings/").concat(t,"/payout-schedule"),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch payout schedule");return e.json()}async updatePayoutSchedule(t,e){let a=await fetch("".concat(r,"/api/group-savings/").concat(t,"/payout-schedule"),{method:"PUT",headers:this.getAuthHeaders(),body:JSON.stringify({schedule:e})});if(!a.ok)throw Error((await a.json()).message||"Failed to update payout schedule");return a.json()}async processPayout(t,e){let a=await fetch("".concat(r,"/api/group-savings/").concat(t,"/payout/").concat(e,"/process"),{method:"POST",headers:this.getAuthHeaders()});if(!a.ok)throw Error((await a.json()).message||"Failed to process payout")}async getGroupStats(){let t=await fetch("".concat(r,"/api/group-savings/stats"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch group statistics");return t.json()}async getGroupAnalytics(t){let e=await fetch("".concat(r,"/api/group-savings/").concat(t,"/analytics"),{headers:this.getAuthHeaders()});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch group analytics");return e.json()}async getActiveGroups(){let t=await fetch("".concat(r,"/api/savings/group/active"),{headers:this.getAuthHeaders()});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch active group savings");return(await t.json()).activeGroups||[]}calculateGroupProgress(t){return Math.min(t.currentAmount/t.targetAmount*100,100)}calculateMemberContributionRate(t,e){return 0===e?0:t.totalContributions/e*100}getNextPayoutDate(t){let e=t.filter(t=>"PENDING"===t.status).sort((t,e)=>new Date(t.scheduledDate).getTime()-new Date(e.scheduledDate).getTime());return e.length>0?e[0].scheduledDate:null}formatCurrency(t){return new Intl.NumberFormat("en-NG",{style:"currency",currency:"NGN"}).format(t)}generateInviteCode(){return Math.random().toString(36).substring(2,8).toUpperCase()}}let o=new s}}]);