(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4345],{11024:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>o});var a=i(95155),n=i(6874),s=i.n(n),r=i(68289),c=i(10351),l=i(48016);function o(){return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-green-900 to-black text-white flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-center max-w-md mx-auto px-6",children:(0,a.jsxs)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)(l.UU,{size:"lg",variant:"light"})}),(0,a.jsx)(r.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"w-24 h-24 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,a.jsx)(c.CKj,{className:"text-red-400 text-4xl"})}),(0,a.jsx)(r.P.h1,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},className:"text-6xl font-bold text-white mb-4",children:"404"}),(0,a.jsx)(r.P.h2,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},className:"text-2xl font-semibold text-white mb-4",children:"Page Not Found"}),(0,a.jsx)(r.P.p,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.6},className:"text-gray-400 mb-8",children:"The page you're looking for doesn't exist or has been moved. Let's get you back on track to your savings goals."}),(0,a.jsxs)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.7},className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsxs)(s(),{href:"/",className:"flex items-center justify-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg transition-colors",children:[(0,a.jsx)(c.V5Y,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Go Home"})]}),(0,a.jsxs)("button",{onClick:()=>window.history.back(),className:"flex items-center justify-center space-x-2 bg-gray-700 hover:bg-gray-600 text-white px-6 py-3 rounded-lg transition-colors",children:[(0,a.jsx)(c.kRp,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:"Go Back"})]})]}),(0,a.jsxs)(r.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.8},className:"mt-8 pt-8 border-t border-gray-700",children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:"Quick Links:"}),(0,a.jsxs)("div",{className:"flex flex-wrap justify-center gap-4 text-sm",children:[(0,a.jsx)(s(),{href:"/auth/login",className:"text-green-400 hover:text-green-300 transition-colors",children:"Login"}),(0,a.jsx)(s(),{href:"/auth/signup",className:"text-green-400 hover:text-green-300 transition-colors",children:"Sign Up"}),(0,a.jsx)(s(),{href:"/features",className:"text-green-400 hover:text-green-300 transition-colors",children:"Features"}),(0,a.jsx)(s(),{href:"/contact",className:"text-green-400 hover:text-green-300 transition-colors",children:"Contact"})]})]})]})})})}},48016:(e,t,i)=>{"use strict";i.d(t,{Qh:()=>o,UU:()=>l});var a=i(95155),n=i(68289),s=i(66766);let r={sm:{container:"h-8",text:"text-lg",icon:"w-6 h-6"},md:{container:"h-10",text:"text-xl",icon:"w-8 h-8"},lg:{container:"h-12",text:"text-2xl",icon:"w-10 h-10"},xl:{container:"h-16",text:"text-3xl",icon:"w-12 h-12"}},c={light:"text-white",dark:"text-gray-900",gradient:"bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"};function l(e){let{size:t="md",variant:i="gradient",showIcon:l=!0,className:o=""}=e,x=r[t],h=c[i];return(0,a.jsxs)(n.P.div,{className:"flex items-center space-x-2 ".concat(x.container," ").concat(o),whileHover:{scale:1.05},transition:{type:"spring",stiffness:400,damping:10},children:[l&&(0,a.jsx)(n.P.div,{className:"".concat(x.icon," flex items-center justify-center relative"),whileHover:{scale:1.1},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:10},children:(0,a.jsx)(s.default,{src:"/images/logo-text.png",alt:"Better Interest Logo",width:4*parseInt(x.icon.split(" ")[0].replace("w-","")),height:4*parseInt(x.icon.split(" ")[1].replace("h-","")),className:"".concat(x.icon," object-contain"),priority:!0})}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)(n.P.h1,{className:"font-inter font-bold leading-tight ".concat(x.text," ").concat(h," relative"),initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5},style:{letterSpacing:"0.02em",textShadow:"0 0 20px rgba(255, 255, 255, 0.1)"},children:[(0,a.jsx)("span",{className:"text-white relative",style:{textShadow:"0 0 15px rgba(255, 255, 255, 0.2)",fontWeight:"700"},children:"Better"}),(0,a.jsx)("span",{className:"text-green-400 ml-1 relative",style:{textShadow:"0 0 20px rgba(34, 197, 94, 0.4), 0 0 40px rgba(34, 197, 94, 0.2)",fontWeight:"800",letterSpacing:"0.05em"},children:"Interest"})]}),"lg"===t||"xl"===t?(0,a.jsx)(n.P.p,{className:"text-xs text-gray-400 -mt-1",initial:{opacity:0},animate:{opacity:1},transition:{duration:.5,delay:.2},children:"Smart Savings Platform"}):null]})]})}function o(e){let{size:t="md",className:i=""}=e,c=r[t];return(0,a.jsx)(n.P.div,{className:"".concat(c.icon," flex items-center justify-center ").concat(i),whileHover:{scale:1.1},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:10},children:(0,a.jsx)(s.default,{src:"/images/logo-text.png",alt:"Better Interest Logo",width:4*parseInt(c.icon.split(" ")[0].replace("w-","")),height:4*parseInt(c.icon.split(" ")[1].replace("h-","")),className:"".concat(c.icon," object-contain"),priority:!0})})}},56127:(e,t,i)=>{Promise.resolve().then(i.bind(i,11024))}},e=>{e.O(0,[844,5236,6874,6766,8441,5964,7358],()=>e(e.s=56127)),_N_E=e.O()}]);